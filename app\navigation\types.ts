export type RootStackParamList = {
  Auth: { screen: keyof AuthStackParamList };
  Main: { screen: keyof TabNavigatorParamList };
  InitialGoalSetup: undefined;
  GoalSetup: undefined;
  InitialBalance: undefined;
  CreateTransaction: undefined;
  EditTransaction: { id: string };
  TransactionDetail: { id: string };
  TransactionList: undefined;
  TransactionStatistics: undefined;
  BudgetStatistics: undefined;
  GoalDetail: { id: string };
  UpdateGoal: { id: string };
  CategoryDetail: { id: string };
  BudgetDetail: { id: string; month?: number; year?: number };
  SetupBudget: undefined;
  CreateBudget: undefined;
  EditBudget: { id: string };
  BudgetReport: { month: number; year: number };
  AddGoal: undefined;
  AddCategory: undefined;
  EditCategory: { categoryId: string };
  ChartDetail: { type: string };
};

export type AuthStackParamList = {
  Login: undefined;
  Register: undefined;
  ForgotPassword: undefined;
  ResetPassword: { email: string; otp: string };
  InitialGoalSetup: undefined;
};

export type TabNavigatorParamList = {
  Home: undefined;
  Goals: undefined;
  Categories: undefined;
  Transactions: undefined;
  Budget: undefined;
  Analytics: undefined;
  More: undefined;
};