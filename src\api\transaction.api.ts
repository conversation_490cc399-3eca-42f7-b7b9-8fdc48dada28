import axios from '../lib/axios';

const TRANSACTIONS_ENDPOINT = '/transactions';

export enum TransactionType {
  INCOME = 'income',
  EXPENSE = 'expense',
}

export enum InputMethod {
  MANUAL = 'manual',
  VOICE = 'voice',
  CHAT = 'chat',
  IMAGE = 'image',
}

export interface ITransaction {
  id: string;
  amount: number;
  type: TransactionType;
  category_id?: string;
  transaction_date: string;
  description?: string;
  notes?: string;
  input_method: InputMethod;
  image_url?: string;
  created_at: string;
  updated_at: string;
  user_id: string;
  category?: {
    id: string;
    name: string;
    icon?: string;
    type: string;
  };
}

export interface CreateTransactionDto {
  amount: number;
  type: TransactionType;
  category_id?: string;
  transaction_date: Date;
  description?: string;
  notes?: string;
  input_method?: InputMethod;
  image_url?: string;
}

export interface UpdateTransactionDto {
  amount?: number;
  type?: TransactionType;
  category_id?: string;
  transaction_date?: Date;
  description?: string;
  notes?: string;
  image_url?: string;
}

export interface TransactionQueryParams {
  page?: number;
  limit?: number;
  start_date?: Date;
  end_date?: Date;
  type?: TransactionType;
  category_id?: string;
  search?: string;
  sort_by?: 'amount' | 'transaction_date' | 'created_at';
  sort_order?: 'ASC' | 'DESC';
}

export interface CategoryStatistics {
  category_id: string;
  category_name: string;
  total_amount: number;
  transaction_count: number;
  percentage: number;
}

export interface TransactionStatistics {
  total_income: number;
  total_expense: number;
  net_amount: number;
  transaction_count: number;
  average_amount: number;
  categories: CategoryStatistics[];
}

export interface BudgetCategoryStatistics {
  budget_id: string;
  category_id: string;
  category_name: string;
  budget_amount: number;
  spent_amount: number;
  remaining_amount: number;
  transaction_count: number;
  percentage_used: number;
  is_exceeded: boolean;
  transactions: Array<{
    id: string;
    amount: number;
    transaction_date: string;
    description?: string;
  }>;
}

export interface BudgetStatistics {
  month: number;
  year: number;
  total_budget: number;
  total_spent: number;
  remaining_budget: number;
  overall_percentage: number;
  categories: BudgetCategoryStatistics[];
}

export interface PaginationMeta {
  currentPage: number;
  itemCount: number;
  itemsPerPage: number;
  totalItems: number;
  totalPages: number;
}

export interface TransactionListResponse {
  data: ITransaction[];
  meta: PaginationMeta;
}

// Backend response wrapper
export interface BackendTransactionListResponse {
  status: string;
  statusCode: number;
  message: string;
  data: {
    data: ITransaction[];
    meta: PaginationMeta;
  };
  meta: {};
  timestamp: string;
}

const buildQueryString = (params: Record<string, any>): Record<string, string> => {
  const queryParams: Record<string, string> = {};
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      if (value instanceof Date) {
        queryParams[key] = value.toISOString();
      } else {
        queryParams[key] = String(value);
      }
    }
  });
  
  return queryParams;
};

export const TransactionAPI = {
  // Create a new transaction
  create: async (data: CreateTransactionDto): Promise<ITransaction> => {
    
    const formData = new FormData();
    formData.append('amount', String(data.amount));
    formData.append('type', data.type);
    if (data.category_id) formData.append('category_id', data.category_id);
    formData.append('transaction_date', data.transaction_date.toISOString());
    if (data.description) formData.append('description', data.description);
    if (data.notes) formData.append('notes', data.notes);
    if (data.input_method) formData.append('input_method', data.input_method);
    if (data.image_url) formData.append('image_url', data.image_url);

    const response = await axios.post(TRANSACTIONS_ENDPOINT, formData);
    
    return response.data.data || response.data;
  },

  // Get all transactions with filters and pagination
  getAll: async (query: TransactionQueryParams = {}): Promise<TransactionListResponse> => {

    const params = buildQueryString(query);
    const response = await axios.get(TRANSACTIONS_ENDPOINT, { params });


    // Handle backend response structure
    if (response.data.data && response.data.data.data) {
      return {
        data: response.data.data.data,
        meta: response.data.data.meta
      };
    }

    return response.data;
  },

  // Get a single transaction by ID
  getById: async (id: string): Promise<ITransaction> => {
    
    const response = await axios.get(`${TRANSACTIONS_ENDPOINT}/${id}`);
    
    return response.data.data || response.data;
  },

  // Update a transaction
  update: async (id: string, data: UpdateTransactionDto): Promise<ITransaction> => {
    
    const formData = new FormData();
    if (data.amount !== undefined) formData.append('amount', String(data.amount));
    if (data.type !== undefined) formData.append('type', data.type);
    if (data.category_id !== undefined) formData.append('category_id', data.category_id);
    if (data.transaction_date !== undefined) formData.append('transaction_date', data.transaction_date.toISOString());
    if (data.description !== undefined) formData.append('description', data.description);
    if (data.notes !== undefined) formData.append('notes', data.notes);

    const response = await axios.patch(`${TRANSACTIONS_ENDPOINT}/${id}`, formData);
    
    return response.data.data || response.data;
  },

  // Delete a transaction
  delete: async (id: string): Promise<void> => {
    
    await axios.delete(`${TRANSACTIONS_ENDPOINT}/${id}`);
  },

  // Get transaction statistics
  getStatistics: async (startDate: Date, endDate: Date): Promise<TransactionStatistics> => {
    
    const params = buildQueryString({
      start_date: startDate,
      end_date: endDate,
    });
    
    const response = await axios.get(`${TRANSACTIONS_ENDPOINT}/statistics/overview`, { params });
    
    return response.data.data || response.data;
  },

  // Get budget statistics
  getBudgetStatistics: async (month: number, year: number): Promise<BudgetStatistics> => {
    
    const response = await axios.get(`${TRANSACTIONS_ENDPOINT}/budget-statistics/${month}/${year}`);
    
    return response.data.data || response.data;
  },
}; 