import { GoalsAPI, CreateGoalDto, UpdateGoalDto, GoalStatus } from '../goals.api';

// Mock axios
jest.mock('../lib/axios');

describe('GoalsAPI', () => {
  const mockGoal = {
    id: '1',
    name: 'Buy House',
    target_amount: 1000000,
    current_amount: 100000,
    start_date: '2024-01-01T00:00:00.000Z',
    target_date: '2025-12-31T00:00:00.000Z',
    status: GoalStatus.ACTIVE,
    user_id: 'user1',
    created_at: '2024-01-01T00:00:00.000Z',
    updated_at: '2024-01-01T00:00:00.000Z'
  };

  const mockCreateGoalDto: CreateGoalDto = {
    name: 'Buy House',
    target_amount: 1000000,
    current_amount: 100000,
    start_date: '2024-01-01T00:00:00.000Z',
    target_date: '2025-12-31T00:00:00.000Z'
  };

  const mockUpdateGoalDto: UpdateGoalDto = {
    name: 'Buy House Updated',
    target_amount: 1200000,
    current_amount: 150000,
    status: GoalStatus.ACTIVE
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('createGoal', () => {
    it('should create a goal and return the goal data', async () => {
      const mockAxios = require('../lib/axios').default;
      mockAxios.post.mockResolvedValue({ data: { data: mockGoal } });

      const result = await GoalsAPI.createGoal(mockCreateGoalDto);

      expect(mockAxios.post).toHaveBeenCalledWith('/goals', mockCreateGoalDto);
      expect(result).toEqual(mockGoal);
    });
  });

  describe('getGoals', () => {
    it('should get goals with pagination', async () => {
      const mockResponse = {
        data: [mockGoal],
        total: 1,
        page: 1,
        limit: 10
      };
      const mockAxios = require('../lib/axios').default;
      mockAxios.get.mockResolvedValue({ data: mockResponse });

      const result = await GoalsAPI.getGoals({ page: 1, limit: 10 });

      expect(mockAxios.get).toHaveBeenCalledWith('/goals', { 
        params: { page: 1, limit: 10 } 
      });
      expect(result).toEqual(mockResponse);
    });
  });

  describe('getGoalById', () => {
    it('should get a goal by id', async () => {
      const mockAxios = require('../lib/axios').default;
      mockAxios.get.mockResolvedValue({ data: { data: mockGoal } });

      const result = await GoalsAPI.getGoalById('1');

      expect(mockAxios.get).toHaveBeenCalledWith('/goals/1');
      expect(result).toEqual(mockGoal);
    });
  });

  describe('updateGoal', () => {
    it('should update a goal', async () => {
      const updatedGoal = { ...mockGoal, ...mockUpdateGoalDto };
      const mockAxios = require('../lib/axios').default;
      mockAxios.patch.mockResolvedValue({ data: { data: updatedGoal } });

      const result = await GoalsAPI.updateGoal('1', mockUpdateGoalDto);

      expect(mockAxios.patch).toHaveBeenCalledWith('/goals/1', mockUpdateGoalDto);
      expect(result).toEqual(updatedGoal);
    });
  });

  describe('deleteGoal', () => {
    it('should delete a goal', async () => {
      const mockAxios = require('../lib/axios').default;
      mockAxios.delete.mockResolvedValue({ data: { data: { success: true } } });

      const result = await GoalsAPI.deleteGoal('1');

      expect(mockAxios.delete).toHaveBeenCalledWith('/goals/1');
      expect(result).toEqual({ success: true });
    });
  });
});
