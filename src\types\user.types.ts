// User enums that match backend exactly
export enum UserGender {
    MALE = 'MALE',
    FEMALE = 'FEMALE',
    OTHER = 'OTHER',
}

export enum UserRole {
    ADMIN = 'ADMIN',
    USER = 'USER',
}

export enum UserStatus {
    ACTIVE = 'ACTIVE',
    INACTIVE = 'INACTIVE',
    BLOCKED = 'BLOCKED',
}

// User interfaces
export interface IUser {
    userId: string;
    username: string;
    email: string;
    roles: UserRole[];
}

export interface UserProfile {
    id: string;
    username: string;
    email: string;
    firstName: string;
    lastName: string;
    gender?: UserGender;
    dateOfBirth?: string;
    phone?: string;
    role: UserRole;
    status: UserStatus;
    createdAt: string;
    updatedAt: string;
}
