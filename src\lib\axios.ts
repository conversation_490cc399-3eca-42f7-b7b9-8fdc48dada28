import axios from 'axios';
import { API_CONFIG } from '../api/config';
import { StorageService } from '../../app/services/storage.service';

const instance = axios.create({
    baseURL: API_CONFIG.BASE_URL,
    headers: API_CONFIG.HEADERS,
    timeout: 30000, // 30 seconds
});


// Add request interceptor to add Authorization header
instance.interceptors.request.use(
    async (config) => {
        try {
            // Skip adding token for auth endpoints
            const isAuthEndpoint = config.url?.includes('/auth/login') ||
                                 config.url?.includes('/auth/register') ||
                                 config.url?.includes('/auth/refresh-token');

            if (!isAuthEndpoint) {
                const token = await StorageService.getAccessToken();
                if (token) {
                    config.headers['Authorization'] = `Bearer ${token}`;
                }
            }

            
        } catch (error) {
        }
        return config;
    },
    (error) => {
        // Don't log to console to avoid showing error overlay
        return Promise.reject(error);
    }
);

// Flag to prevent multiple refresh attempts
let isRefreshing = false;
let failedQueue: any[] = [];

const processQueue = (error: any, token: string | null = null) => {
    failedQueue.forEach(prom => {
        if (error) {
            prom.reject(error);
        } else {
            prom.resolve(token);
        }
    });

    failedQueue = [];
};

// Add response interceptor for better error handling and token refresh
instance.interceptors.response.use(
    (response) => response,
    async (error) => {
        const originalRequest = error.config;

        if (error.response) {
            // Handle 401 Unauthorized - token expired or invalid
            if (error.response.status === 401 && !originalRequest._retry) {
                if (isRefreshing) {
                    // If already refreshing, queue this request
                    return new Promise((resolve, reject) => {
                        failedQueue.push({ resolve, reject });
                    }).then(token => {
                        originalRequest.headers['Authorization'] = `Bearer ${token}`;
                        return instance(originalRequest);
                    }).catch(err => {
                        return Promise.reject(err);
                    });
                }

                originalRequest._retry = true;
                isRefreshing = true;

                try {
                    // Try to refresh the token
                    const refreshToken = await StorageService.getRefreshToken();
                    if (!refreshToken) {
                        throw new Error('No refresh token available');
                    }

                    const response = await instance.post('/auth/refresh-token', { refreshToken });
                    const { accessToken, refreshToken: newRefreshToken } = response.data.data;

                    // Save new tokens
                    await StorageService.setAccessToken(accessToken);
                    await StorageService.setRefreshToken(newRefreshToken);

                    // Update the authorization header
                    originalRequest.headers['Authorization'] = `Bearer ${accessToken}`;

                    // Process queued requests
                    processQueue(null, accessToken);

                    // Retry the original request
                    return instance(originalRequest);
                } catch (refreshError) {
                    // Refresh failed, clear all tokens
                    await StorageService.removeAccessToken();
                    await StorageService.removeRefreshToken();

                    // Process queued requests with error
                    processQueue(refreshError, null);

                    return Promise.reject(refreshError);
                } finally {
                    isRefreshing = false;
                }
            }
            // Server responded with a status code outside of 2xx
            // Don't log to console to avoid showing error overlay
        } else if (error.request) {
            // Request was made but no response received
            // Don't log to console to avoid showing error overlay
        } else {
            // Something happened in setting up the request
            // Don't log to console to avoid showing error overlay
        }
        return Promise.reject(error);
    }
);

export default instance;