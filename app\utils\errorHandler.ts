// Disable error overlay in development
if (__DEV__) {
  // Disable console.error to prevent error overlay
  const originalConsoleError = console.error;
  console.error = (...args: any[]) => {
    // Only log critical errors that are not network/API related
    const message = args[0]?.toString() || '';
    
    // Skip logging these common errors that we handle with alerts
    if (
      message.includes('AxiosError') ||
      message.includes('Request failed') ||
      message.includes('Network Error') ||
      message.includes('timeout') ||
      message.includes('Login error') ||
      message.includes('Register error') ||
      message.includes('Error loading') ||
      message.includes('Error saving') ||
      message.includes('Error getting') ||
      message.includes('Error removing') ||
      message.includes('Error clearing')
    ) {
      return; // Don't log these errors
    }
    
    // Log other errors normally
    originalConsoleError.apply(console, args);
  };

  // Disable console.warn for less critical warnings
  const originalConsoleWarn = console.warn;
  console.warn = (...args: any[]) => {
    const message = args[0]?.toString() || '';
    
    // Skip common React Native warnings
    if (
      message.includes('Warning:') ||
      message.includes('componentWillReceiveProps') ||
      message.includes('componentWillMount') ||
      message.includes('VirtualizedList')
    ) {
      return;
    }
    
    originalConsoleWarn.apply(console, args);
  };
}

export {};
