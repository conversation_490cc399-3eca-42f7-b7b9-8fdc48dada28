import React, { useState, useCallback } from 'react';
import {
    View,
    Text,
    StyleSheet,
    FlatList,
    TouchableOpacity,
    Alert,
    RefreshControl,
    ActivityIndicator,
} from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import Screen from '../components/common/Screen';
import DrawerHeader from '../components/common/DrawerHeader';
import CategoryCard from '../components/categories/CategoryCard';
import theme from '../theme';
import { CategoryAPI, ICategory, CategoryStatus, CategoryType } from '../../src/api/category.api';
import { RootStackParamList } from '../navigation/types';

type NavigationProp = NativeStackNavigationProp<RootStackParamList>;

const CategoriesScreen = () => {
    const navigation = useNavigation<NavigationProp>();
    const [categories, setCategories] = useState<ICategory[]>([]);
    const [loading, setLoading] = useState(true);
    const [refreshing, setRefreshing] = useState(false);
    const [activeFilter, setActiveFilter] = useState<'all' | 'active' | 'inactive'>('all');
    const [typeFilter, setTypeFilter] = useState<'all' | CategoryType>('all');



    // Load categories from API
    const loadCategories = async () => {
        try {
            setLoading(true);
            const params: any = { page: 1, limit: 100 };

            if (activeFilter !== 'all') {
                params.status = activeFilter === 'active' ? CategoryStatus.ACTIVE : CategoryStatus.INACTIVE; // 'active' : 'inactive'
            }

            if (typeFilter !== 'all') {
                params.type = typeFilter;
            }

            const response = await CategoryAPI.getAll(params);
            setCategories(response.data || []);
        } catch (error: any) {
            Alert.alert('Error', 'Unable to load categories. Please try again.');
            console.error('Error loading categories:', error);
        } finally {
            setLoading(false);
        }
    };

    // Handle refresh
    const handleRefresh = async () => {
        setRefreshing(true);
        await loadCategories();
        setRefreshing(false);
    };

    // Toggle category status
    const handleToggleStatus = async (category: ICategory) => {
        try {
            // Check category before toggle
            if (!category || !category.id) {
                Alert.alert('Error', 'Invalid category data.');
                return;
            }

            // Toggle status manually since we don't have toggleCategoryStatus in new API
            const newStatus = category.status === CategoryStatus.ACTIVE ? CategoryStatus.INACTIVE : CategoryStatus.ACTIVE; // 'active' <-> 'inactive'
            const updatedCategory = await CategoryAPI.update(category.id, { status: newStatus });

            setCategories(prev =>
                prev.map(cat =>
                    cat.id === category.id ? updatedCategory : cat
                )
            );
        } catch (error: any) {
            Alert.alert('Error', 'Unable to change category status.');
            console.error('Error toggling category status:', error);
        }
    };

    // Navigate to category detail
    const handleCategoryPress = (category: ICategory) => {
        navigation.navigate('CategoryDetail', { id: category.id });
    };

    // Navigate to add category
    const handleAddCategory = () => {
        navigation.navigate('AddCategory');
    };

    // Load data when screen is focused
    useFocusEffect(
        useCallback(() => {
            loadCategories();
        }, [activeFilter, typeFilter])
    );

    // Filter buttons component
    const FilterButtons = () => (
        <View style={styles.filtersContainer}>
            {/* Status Filter */}
            <View style={styles.filterGroup}>
                <Text style={styles.filterLabel}>Status:</Text>
                <View style={styles.filterButtons}>
                    {[
                        { key: 'all', label: 'All' },
                        { key: 'active', label: 'Active' },
                        { key: 'inactive', label: 'Inactive' },
                    ].map((filter) => (
                        <TouchableOpacity
                            key={`status-${filter.key}`} // Unique key
                            style={[
                                styles.filterButton,
                                activeFilter === filter.key && styles.activeFilterButton
                            ]}
                            onPress={() => setActiveFilter(filter.key as any)}
                        >
                            <Text style={[
                                styles.filterButtonText,
                                activeFilter === filter.key && styles.activeFilterButtonText
                            ]}>
                                {filter.label}
                            </Text>
                        </TouchableOpacity>
                    ))}
                </View>
            </View>

            {/* Type Filter */}
            <View style={styles.filterGroup}>
                <Text style={styles.filterLabel}>Type:</Text>
                <View style={styles.filterButtons}>
                    {[
                        { key: 'all', label: 'All' },
                        { key: CategoryType.INCOME, label: 'Income' },
                        { key: CategoryType.EXPENSE, label: 'Expense' },
                    ].map((filter) => (
                        <TouchableOpacity
                            key={`type-${filter.key}`} // Unique key
                            style={[
                                styles.filterButton,
                                typeFilter === filter.key && styles.activeFilterButton
                            ]}
                            onPress={() => setTypeFilter(filter.key as any)}
                        >
                            <Text style={[
                                styles.filterButtonText,
                                typeFilter === filter.key && styles.activeFilterButtonText
                            ]}>
                                {filter.label}
                            </Text>
                        </TouchableOpacity>
                    ))}
                </View>
            </View>
        </View>
    );

    // Empty state component
    const EmptyState = () => (
        <View style={styles.emptyState}>
            <Text style={styles.emptyStateIcon}>📁</Text>
            <Text style={styles.emptyStateTitle}>No categories yet</Text>
            <Text style={styles.emptyStateText}>
                Add your first category to start organizing your expenses and income
            </Text>
            <TouchableOpacity style={styles.emptyStateButton} onPress={handleAddCategory}>
                <Text style={styles.emptyStateButtonText}>Add Category</Text>
            </TouchableOpacity>
        </View>
    );

    if (loading) {
        return (
            <Screen>
                <DrawerHeader title="Categories" />
                <View style={styles.loadingContainer}>
                    <ActivityIndicator size="large" color={theme.colors.primary[500]} />
                    <Text style={styles.loadingText}>Loading categories...</Text>
                </View>
            </Screen>
        );
    }

    return (
        <Screen>
            <DrawerHeader title="Categories" />

            {/* Header Section */}
            <View style={styles.header}>
                <Text style={styles.title}>Categories</Text>
                <Text style={styles.subtitle}>
                    Manage your {categories.length} categories
                </Text>
            </View>

            {/* Filters */}
            <FilterButtons />

            {/* Categories List */}
            <FlatList
                data={categories}
                keyExtractor={(item, index) => `category-${item.id || index}`}
                renderItem={({ item }) => (
                    <CategoryCard
                        category={item}
                        onPress={() => handleCategoryPress(item)}
                        onToggleStatus={() => handleToggleStatus(item)}
                    />
                )}
                contentContainerStyle={styles.listContainer}
                showsVerticalScrollIndicator={false}
                refreshControl={
                    <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
                }
                ListEmptyComponent={EmptyState}
            />

            {/* Add Button Container */}
            <View style={styles.buttonContainer}>
                <TouchableOpacity
                    style={styles.addButton}
                    onPress={handleAddCategory}
                    activeOpacity={0.8}
                >
                    <Text style={styles.addButtonText}>+ Add New Category</Text>
                </TouchableOpacity>
            </View>
        </Screen>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: theme.colors.background.secondary,
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
    },
    loadingText: {
        marginTop: theme.spacing.md,
        fontSize: theme.typography.fontSizes.md,
        color: theme.colors.text.secondary,
    },
    header: {
        paddingHorizontal: theme.spacing.md,
        paddingTop: theme.spacing.md,
        paddingBottom: theme.spacing.md,
    },
    title: {
        fontSize: theme.typography.fontSizes['2xl'],
        fontWeight: '700',
        color: theme.colors.text.primary,
        marginBottom: theme.spacing.xs,
    },
    subtitle: {
        fontSize: theme.typography.fontSizes.md,
        color: theme.colors.text.secondary,
    },
    filtersContainer: {
        backgroundColor: theme.colors.background.primary,
        padding: theme.spacing.md,
        borderBottomWidth: 1,
        borderBottomColor: theme.colors.gray[200],
    },
    filterGroup: {
        marginBottom: theme.spacing.sm,
    },
    filterLabel: {
        fontSize: theme.typography.fontSizes.sm,
        fontWeight: '500',
        color: theme.colors.text.primary,
        marginBottom: theme.spacing.xs,
    },
    filterButtons: {
        flexDirection: 'row',
        flexWrap: 'wrap',
    },
    filterButton: {
        paddingHorizontal: theme.spacing.md,
        paddingVertical: theme.spacing.sm,
        backgroundColor: theme.colors.gray[100],
        borderRadius: theme.borderRadius.lg,
        marginRight: theme.spacing.sm,
        marginBottom: theme.spacing.xs,
    },
    activeFilterButton: {
        backgroundColor: theme.colors.primary[500],
    },
    filterButtonText: {
        fontSize: theme.typography.fontSizes.sm,
        color: theme.colors.text.secondary,
        fontWeight: '500',
    },
    activeFilterButtonText: {
        color: theme.colors.background.primary,
    },
    listContainer: {
        padding: theme.spacing.md,
        paddingBottom: theme.spacing['4xl'],
    },
    emptyState: {
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: theme.spacing['4xl'],
    },
    emptyStateIcon: {
        fontSize: 64,
        marginBottom: theme.spacing.lg,
    },
    emptyStateTitle: {
        fontSize: theme.typography.fontSizes.xl,
        fontWeight: '600',
        color: theme.colors.text.primary,
        marginBottom: theme.spacing.sm,
        textAlign: 'center',
    },
    emptyStateText: {
        fontSize: theme.typography.fontSizes.md,
        color: theme.colors.text.secondary,
        textAlign: 'center',
        marginBottom: theme.spacing.xl,
        paddingHorizontal: theme.spacing.lg,
        lineHeight: 22,
    },
    emptyStateButton: {
        backgroundColor: theme.colors.primary[500],
        paddingHorizontal: theme.spacing.xl,
        paddingVertical: theme.spacing.md,
        borderRadius: theme.borderRadius.lg,
    },
    emptyStateButtonText: {
        color: theme.colors.background.primary,
        fontSize: theme.typography.fontSizes.md,
        fontWeight: '600',
    },
    buttonContainer: {
        padding: theme.spacing.md,
        backgroundColor: theme.colors.background.primary,
        borderTopWidth: 1,
        borderTopColor: theme.colors.gray[200],
    },
    addButton: {
        backgroundColor: theme.colors.primary[500],
        paddingVertical: theme.spacing.md,
        paddingHorizontal: theme.spacing.lg,
        borderRadius: theme.borderRadius.lg,
        alignItems: 'center',
        justifyContent: 'center',
    },
    addButtonText: {
        fontSize: theme.typography.fontSizes.md,
        color: theme.colors.background.primary,
        fontWeight: '600',
    },
});

export default CategoriesScreen;