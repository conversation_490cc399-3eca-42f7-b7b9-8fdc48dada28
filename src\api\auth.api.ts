import axios from '../lib/axios';
import { StorageService } from '../../app/services/storage.service';
import { Alert } from 'react-native';
import { UserGender } from '../types/user.types';

export interface LoginDto {
    email: string;
    password: string;
}

export interface RegisterDto {
    email: string;
    password: string;
    username: string;
    firstName: string;
    lastName: string;
    dateOfBirth: string;
    gender?: UserGender;
    phone?: string;
}

export interface RefreshTokenDto {
    refreshToken: string;
}

export interface ForgotPasswordDto {
    email: string;
}

export interface VerifyOtpDto {
    email: string;
    otp: string;
}

export interface ResetPasswordDto {
    email: string;
    otp: string;
    newPassword: string;
}

export interface RefreshTokenResponse {
    data: {
        accessToken: string;
        refreshToken: string;
    };
}

export interface AuthResponse {
    data: {
        user: {
            username: string;
            email: string;
            firstName: string;
            lastName: string;
            gender?: string;
            dateOfBirth?: string;
            phone?: string;
        };
        accessToken: string;
        refreshToken: string;
    };
}

export interface User {
    username: string;
    email: string;
    firstName: string;
    lastName: string;
    gender?: string;
    dateOfBirth?: string;
    phone?: string;
}

export const AuthAPI = {
    // Login user and save token
    async login(credentials: LoginDto): Promise<User> {
        try {
            const response = await axios.post('/auth/login', credentials);
            const authResponse: AuthResponse = response.data;

            // Save the access token and refresh token
            await StorageService.setAccessToken(authResponse.data.accessToken);
            await StorageService.setRefreshToken(authResponse.data.refreshToken);

            return authResponse.data.user;
        } catch (error) {
            // Don't log to console to avoid showing error overlay
            throw error;
        }
    },

    // Register user
    async register(userData: RegisterDto): Promise<User> {
        try {
            const response = await axios.post('/auth/register', userData);
            const authResponse: AuthResponse = response.data;

            // Save the access token and refresh token
            await StorageService.setAccessToken(authResponse.data.accessToken);
            await StorageService.setRefreshToken(authResponse.data.refreshToken);

            return authResponse.data.user;
        } catch (error) {
            // Don't log to console to avoid showing error overlay
            throw error;
        }
    },

    // Refresh access token
    async refreshToken(): Promise<{ accessToken: string; refreshToken: string }> {
        try {
            const refreshToken = await StorageService.getRefreshToken();
            if (!refreshToken) {
                throw new Error('No refresh token available');
            }

            const response = await axios.post('/auth/refresh-token', { refreshToken });
            const refreshResponse: RefreshTokenResponse = response.data;

            // Save the new tokens
            await StorageService.setAccessToken(refreshResponse.data.accessToken);
            await StorageService.setRefreshToken(refreshResponse.data.refreshToken);

            return refreshResponse.data;
        } catch (error) {
            // If refresh fails, clear all tokens
            await StorageService.removeAccessToken();
            await StorageService.removeRefreshToken();
            throw error;
        }
    },

    // Logout user
    async logout(): Promise<void> {
        try {
            // Clear stored tokens
            await StorageService.removeAccessToken();
            await StorageService.removeRefreshToken();
        } catch (error) {
            // Don't log to console to avoid showing error overlay
            throw error;
        }
    },

    // Forgot password
    async forgotPassword(forgotPasswordDto: ForgotPasswordDto): Promise<boolean> {
        try {
            const response = await axios.post('/auth/forgot-password', forgotPasswordDto);
            return response.data;
        } catch (error) {
            throw error;
        }
    },

    // Verify OTP
    async verifyOtp(verifyOtpDto: VerifyOtpDto): Promise<boolean> {
        try {
            const response = await axios.post('/auth/verify-otp', verifyOtpDto);
            return response.data;
        } catch (error) {
            throw error;
        }
    },

    // Reset password
    async resetPassword(resetPasswordDto: ResetPasswordDto): Promise<boolean> {
        try {
            const response = await axios.post('/auth/reset-password', resetPasswordDto);
            return response.data;
        } catch (error) {
            throw error;
        }
    },
};

// Legacy functions for backward compatibility
export const loginUser = async (formData: {
    email: string;
    password: string;
}) => {
    try {
        const user = await AuthAPI.login(formData);
        return { success: true, data: { user } };
    } catch (error: any) {
        const errorMessage = "Username or password is incorrect";
        Alert.alert('Error', errorMessage);
        return { success: false, error: errorMessage };
    }
};

export const registerUser = async (formData: {
    username: string;
    password: string;
    email: string;
    firstName: string;
    lastName: string;
    gender: UserGender | '';
    birthDate: string;
    phone?: string;
}) => {
    try {
        const userData: RegisterDto = {
            email: formData.email,
            password: formData.password,
            username: formData.username,
            firstName: formData.firstName,
            lastName: formData.lastName,
            dateOfBirth: formData.birthDate, 
            gender: formData.gender ? formData.gender as UserGender : undefined, 
            phone: formData.phone,
        };


        const user = await AuthAPI.register(userData);
        return { success: true, data: { user } };
    } catch (error: any) {
        return { success: false };
    }
};