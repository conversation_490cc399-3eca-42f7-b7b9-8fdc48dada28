import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Dimensions,
  Alert,
  Vibration,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation/types';
import theme from '../theme';
import Screen from '../components/common/Screen';
import DrawerHeader from '../components/common/DrawerHeader';
import Button from '../components/ui/Button';
import Card from '../components/ui/Card';
import { UserAPI, SetInitialBalanceDto } from '../../src/api/user.api';

const { width } = Dimensions.get('window');

type InitialBalanceScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'InitialBalance'
>;

const CURRENCIES = [
  { id: 'VND', label: 'VND', symbol: '₫' },
  { id: 'USD', label: 'USD', symbol: '$' },
  { id: 'EUR', label: 'EUR', symbol: '€' },
  { id: 'GBP', label: 'GBP', symbol: '£' },
  { id: 'JPY', label: 'JPY', symbol: '¥' },
];

const InitialBalanceScreen = () => {
  const navigation = useNavigation<InitialBalanceScreenNavigationProp>();
  const [amount, setAmount] = useState('');
  const [selectedCurrency, setSelectedCurrency] = useState(CURRENCIES[0]);
  const [loading, setLoading] = useState(false);
  const [rawAmount, setRawAmount] = useState('');

  // Format amount with commas and support negative numbers
  const formatAmount = (text: string) => {
    // Allow negative sign at the beginning
    const isNegative = text.startsWith('-');

    // Remove all non-numeric characters except the negative sign
    let numericValue = text.replace(/[^0-9-]/g, '');

    // Ensure only one negative sign at the beginning
    if (isNegative) {
      numericValue = '-' + numericValue.replace(/-/g, '');
    } else {
      numericValue = numericValue.replace(/-/g, '');
    }

    // Store raw amount for API call
    setRawAmount(numericValue);

    // Format with commas for display
    if (numericValue && numericValue !== '-') {
      const number = parseFloat(numericValue);
      if (!isNaN(number)) {
        // Check for maximum value (1 trillion)
        if (Math.abs(number) > 1000000000000) {
          Alert.alert('Error', 'Amount cannot exceed 1 trillion');
          return;
        }

        const formattedValue = number.toLocaleString(
          selectedCurrency.id === 'VND' ? 'vi-VN' : 'en-US'
        );
        setAmount(isNegative && number > 0 ? '-' + formattedValue : formattedValue);
      } else {
        setAmount('');
      }
    } else {
      setAmount(numericValue === '-' ? '-' : '');
    }
  };

  // Get formatted preview of the amount
  const getFormattedPreview = () => {
    if (!rawAmount || rawAmount === '-') return '';

    const number = parseFloat(rawAmount);
    if (isNaN(number)) return '';

    return new Intl.NumberFormat(
      selectedCurrency.id === 'VND' ? 'vi-VN' : 'en-US',
      {
        style: 'currency',
        currency: selectedCurrency.id,
        minimumFractionDigits: selectedCurrency.id === 'VND' ? 0 : 2,
      }
    ).format(number);
  };

  const handleSkip = () => {
    // Haptic feedback for skip action
    Vibration.vibrate(50);
    navigation.navigate('Main' as keyof RootStackParamList);
  };

  const handleContinue = async () => {
    if (!rawAmount || rawAmount === '-') {
      Alert.alert('Error', 'Please enter a valid amount');
      return;
    }

    const numericAmount = parseFloat(rawAmount);
    if (isNaN(numericAmount)) {
      Alert.alert('Error', 'Please enter a valid amount');
      return;
    }

    try {
      setLoading(true);

      // Haptic feedback for button press
      Vibration.vibrate(100);

      const balanceData: SetInitialBalanceDto = {
        initial_balance: numericAmount,
        currency: selectedCurrency.id,
      };

      await UserAPI.setInitialBalance(balanceData);

      // Success feedback
      Vibration.vibrate([100, 50, 100]);

      Alert.alert(
        'Success',
        `Initial balance of ${getFormattedPreview()} has been set successfully!`,
        [
          {
            text: 'Continue',
            onPress: () => navigation.navigate('Main' as keyof RootStackParamList),
          },
        ]
      );
    } catch (error: any) {
      // Error haptic feedback
      Vibration.vibrate([200, 100, 200]);

      let errorMessage = 'Failed to set initial balance. Please try again.';

      if (error.response?.status === 400) {
        errorMessage = error.response?.data?.message || 'Invalid amount. Please check your input.';
      } else if (error.response?.status === 401) {
        errorMessage = 'Authentication failed. Please login again.';
      } else if (error.response?.status >= 500) {
        errorMessage = 'Server error. Please try again later.';
      } else if (error.message) {
        errorMessage = error.message;
      }

      Alert.alert('Error', errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Screen>
      <DrawerHeader title="Initial Balance" showBackButton />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.headerContainer}>
            <Text style={styles.headerTitle}>Initial Balance</Text>
            <Text style={styles.headerSubtitle}>
              Enter your current balance to start tracking your finances effectively
            </Text>
          </View>

          <View style={styles.illustrationContainer}>
            <Text style={styles.illustrationEmoji}>💰</Text>
          </View>

          <Card style={styles.formContainer}>
            {/* Currency selection */}
            <Text style={styles.labelText}>Currency</Text>
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.currencyContainer}
            >
              {CURRENCIES.map((currency) => (
                <TouchableOpacity
                  key={currency.id}
                  style={[
                    styles.currencyOption,
                    selectedCurrency.id === currency.id && styles.selectedCurrency,
                  ]}
                  onPress={() => setSelectedCurrency(currency)}
                >
                  <Text
                    style={[
                      styles.currencyText,
                      selectedCurrency.id === currency.id && styles.selectedCurrencyText,
                    ]}
                  >
                    {currency.label} ({currency.symbol})
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>

            {/* Amount input */}
            <Text style={styles.labelText}>Current Balance</Text>
            <View style={styles.amountInputContainer}>
              <Text style={styles.currencySymbol}>{selectedCurrency.symbol}</Text>
              <TextInput
                style={styles.amountInput}
                placeholder="1,000,000 (can be negative)"
                keyboardType="numeric"
                value={amount}
                onChangeText={formatAmount}
                editable={!loading}
              />
            </View>

            {/* Amount preview */}
            {getFormattedPreview() && (
              <View style={styles.previewContainer}>
                <Text style={styles.previewLabel}>Preview:</Text>
                <Text style={styles.previewAmount}>{getFormattedPreview()}</Text>
              </View>
            )}

            <Text style={styles.helperText}>
              You can enter negative amounts if you have debt. Skip this step to set up later.
            </Text>
          </Card>

          <View style={styles.buttonsContainer}>
            <Button
              title="Skip"
              variant="outline"
              onPress={handleSkip}
              style={styles.skipButton}
              disabled={loading}
            />

            <Button
              title="Continue"
              variant="primary"
              onPress={handleContinue}
              disabled={!rawAmount || rawAmount === '-' || loading}
              loading={loading}
              style={[
                styles.continueButton,
                (!rawAmount || rawAmount === '-') && styles.disabledButton,
              ]}
            />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </Screen>
  );
};

const styles = StyleSheet.create({
  scrollContainer: {
    flexGrow: 1,
    paddingHorizontal: theme.spacing.xl,
    paddingBottom: theme.spacing.xl,
  },
  headerContainer: {
    paddingTop: theme.spacing.xl,
    marginBottom: theme.spacing.xl,
  },
  headerTitle: {
    fontSize: theme.typography.fontSizes['2xl'],
    fontWeight: '700',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.sm,
  },
  headerSubtitle: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.secondary,
  },
  illustrationContainer: {
    alignItems: 'center',
    marginBottom: theme.spacing.xl,
  },
  illustrationEmoji: {
    fontSize: 80,
  },
  formContainer: {
    marginBottom: theme.spacing.xl,
    padding: theme.spacing.md,
  },
  labelText: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '500',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.sm,
  },
  currencyContainer: {
    flexDirection: 'row',
    marginBottom: theme.spacing.xl,
  },
  currencyOption: {
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.md,
    borderWidth: 1,
    borderColor: theme.colors.gray[200],
    borderRadius: theme.borderRadius.md,
    marginRight: theme.spacing.sm,
  },
  selectedCurrency: {
    backgroundColor: theme.colors.primary[100],
    borderColor: theme.colors.primary[500],
  },
  currencyText: {
    color: theme.colors.text.secondary,
    fontWeight: '500',
  },
  selectedCurrencyText: {
    color: theme.colors.primary[500],
  },
  amountInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.gray[100],
    borderRadius: theme.borderRadius.md,
    paddingHorizontal: theme.spacing.md,
    marginBottom: theme.spacing.md,
  },
  currencySymbol: {
    fontSize: theme.typography.fontSizes.lg,
    color: theme.colors.text.secondary,
    marginRight: theme.spacing.sm,
  },
  amountInput: {
    flex: 1,
    padding: theme.spacing.md,
    fontSize: theme.typography.fontSizes.lg,
  },
  previewContainer: {
    backgroundColor: theme.colors.gray[50],
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.sm,
    marginBottom: theme.spacing.md,
    borderLeftWidth: 3,
    borderLeftColor: theme.colors.primary[500],
  },
  previewLabel: {
    fontSize: theme.typography.fontSizes.xs,
    color: theme.colors.text.secondary,
    marginBottom: theme.spacing.xs,
    fontWeight: '500',
  },
  previewAmount: {
    fontSize: theme.typography.fontSizes.lg,
    color: theme.colors.primary[600],
    fontWeight: '600',
  },
  helperText: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.gray[500],
    fontStyle: 'italic',
  },
  buttonsContainer: {
    flexDirection: 'row',
    marginTop: 'auto',
    marginBottom: theme.spacing.md,
  },
  skipButton: {
    flex: 1,
    marginRight: theme.spacing.sm,
    borderColor: theme.colors.gray[200],
  },
  continueButton: {
    flex: 1,
  },
  disabledButton: {
    opacity: 0.6,
  },
});

export default InitialBalanceScreen;