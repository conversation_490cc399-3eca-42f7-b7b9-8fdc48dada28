import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Dimensions,
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { StatusBar } from 'expo-status-bar';
import { RootStackParamList } from '../navigation/types';
import theme from '../theme';
import Screen from '../components/common/Screen';
import Card from '../components/ui/Card';

const { width } = Dimensions.get('window');
const CARD_WIDTH = width - 40; // Full width minus padding

type ChartDetailScreenRouteProp = RouteProp<
  RootStackParamList,
  'ChartDetail'
>;

type ChartDetailScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'ChartDetail'
>;

// Mock data for expense categories
const expenseCategories = [
  { id: '1', name: 'Ăn uống', percentage: 30, amount: 1500000, color: '#38BDF8' },
  { id: '2', name: '<PERSON> chuyể<PERSON>', percentage: 15, amount: 750000, color: '#A78BFA' },
  { id: '3', name: 'Mua sắm', percentage: 20, amount: 1000000, color: '#FB923C' },
  { id: '4', name: 'Nhà cửa', percentage: 25, amount: 1250000, color: '#34D399' },
  { id: '5', name: 'Giải trí', percentage: 10, amount: 500000, color: '#F87171' },
];

// Mock data for monthly income vs expense
const monthlyComparisonData = [
  { month: 'T1', income: 7000000, expense: 5500000, savings: 1500000 },
  { month: 'T2', income: 7200000, expense: 5800000, savings: 1400000 },
  { month: 'T3', income: 7100000, expense: 5600000, savings: 1500000 },
  { month: 'T4', income: 7500000, expense: 6000000, savings: 1500000 },
  { month: 'T5', income: 7300000, expense: 5000000, savings: 2300000 },
  { month: 'T6', income: 7400000, expense: 5200000, savings: 2200000 },
];

// Mock data for daily expenses
const dailyExpenseData = [
  { day: '01/05', amount: 250000 },
  { day: '02/05', amount: 150000 },
  { day: '03/05', amount: 300000 },
  { day: '04/05', amount: 100000 },
  { day: '05/05', amount: 500000 },
  { day: '06/05', amount: 200000 },
  { day: '07/05', amount: 150000 },
  { day: '08/05', amount: 300000 },
  { day: '09/05', amount: 400000 },
  { day: '10/05', amount: 250000 },
  { day: '11/05', amount: 150000 },
  { day: '12/05', amount: 200000 },
  { day: '13/05', amount: 350000 },
  { day: '14/05', amount: 300000 },
  { day: '15/05', amount: 250000 },
];

const ChartDetailScreen = () => {
  const navigation = useNavigation<ChartDetailScreenNavigationProp>();
  const route = useRoute<ChartDetailScreenRouteProp>();
  const { type } = route.params;
  
  const [timeFilter, setTimeFilter] = useState<'week' | 'month' | 'year'>('month');
  const [selectedTimeIndex, setSelectedTimeIndex] = useState<number>(0);
  
  const formatCurrency = (amount: number) => {
    return amount.toLocaleString('vi-VN') + ' ₫';
  };
  
  const getChartTitle = () => {
    switch (type) {
      case 'expense-categories':
        return 'Chi tiêu theo danh mục';
      case 'income-expense':
        return 'Thu nhập vs Chi tiêu';
      case 'savings-trend':
        return 'Xu hướng tiết kiệm';
      default:
        return 'Biểu đồ chi tiết';
    }
  };
  
  const renderExpenseCategoriesChart = () => (
    <View style={styles.chartSection}>
      {/* Pie chart representation */}
      <View style={styles.pieChartContainer}>
        <View style={styles.pieChart}>
          {expenseCategories.map((category, index) => {
            const rotation = index > 0 
              ? expenseCategories
                  .slice(0, index)
                  .reduce((sum, cat) => sum + cat.percentage, 0) 
              : 0;
            
            return (
              <View
                key={category.id}
                style={[
                  styles.pieSlice,
                  {
                    backgroundColor: category.color,
                    transform: [
                      { rotate: `${rotation}deg` },
                      { rotateZ: `${category.percentage * 3.6}deg` },
                    ],
                    zIndex: expenseCategories.length - index,
                  },
                ]}
              />
            );
          })}
          <View style={styles.pieChartCenter}>
            <Text style={styles.pieChartCenterText}>100%</Text>
          </View>
        </View>

        {/* Category legend */}
        <Card style={styles.categoryLegend}>
          {expenseCategories.map((category) => (
            <View key={category.id} style={styles.legendItem}>
              <View style={[styles.legendColorBox, { backgroundColor: category.color }]} />
              <View style={styles.legendTextContainer}>
                <Text style={styles.legendCategoryName}>{category.name}</Text>
                <Text style={styles.legendCategoryPercentage}>{category.percentage}%</Text>
              </View>
              <Text style={styles.legendCategoryAmount}>{formatCurrency(category.amount)}</Text>
            </View>
          ))}
        </Card>
        
        <Card style={styles.totalContainer}>
          <Text style={styles.totalLabel}>Tổng chi tiêu</Text>
          <Text style={styles.totalAmount}>
            {formatCurrency(expenseCategories.reduce((sum, cat) => sum + cat.amount, 0))}
          </Text>
        </Card>
      </View>
    </View>
  );
  
  const renderIncomeExpenseChart = () => (
    <View style={styles.chartSection}>
      {/* Time selector buttons */}
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false} 
        contentContainerStyle={styles.timeButtonsContainer}
      >
        {monthlyComparisonData.map((data, index) => (
          <TouchableOpacity
            key={index}
            style={[
              styles.timeButton,
              selectedTimeIndex === index && styles.selectedTimeButton,
            ]}
            onPress={() => setSelectedTimeIndex(index)}
          >
            <Text style={[
              styles.timeButtonText,
              selectedTimeIndex === index && styles.selectedTimeButtonText,
            ]}>
              {data.month}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
      
      {/* Selected month data */}
      <Card style={styles.selectedMonthCard}>
        <Text style={styles.selectedMonthTitle}>
          {monthlyComparisonData[selectedTimeIndex].month}/2023
        </Text>
        
        <View style={styles.compareContainer}>
          <View style={styles.compareItem}>
            <Text style={styles.compareLabel}>Thu nhập</Text>
            <Text style={styles.incomeAmount}>
              {formatCurrency(monthlyComparisonData[selectedTimeIndex].income)}
            </Text>
          </View>
          <View style={styles.compareDivider} />
          <View style={styles.compareItem}>
            <Text style={styles.compareLabel}>Chi tiêu</Text>
            <Text style={styles.expenseAmount}>
              {formatCurrency(monthlyComparisonData[selectedTimeIndex].expense)}
            </Text>
          </View>
        </View>
        
        <View style={styles.savingsContainer}>
          <Text style={styles.savingsLabel}>Tiết kiệm</Text>
          <Text style={styles.savingsAmount}>
            {formatCurrency(monthlyComparisonData[selectedTimeIndex].savings)}
          </Text>
          <View style={styles.savingsPercentContainer}>
            <Text style={styles.savingsPercent}>
              {Math.round((monthlyComparisonData[selectedTimeIndex].savings / 
                monthlyComparisonData[selectedTimeIndex].income) * 100)}%
            </Text>
            <Text style={styles.savingsPercentLabel}>thu nhập</Text>
          </View>
        </View>
      </Card>
      
      {/* Bar chart for all months */}
      <Card style={styles.barChartContainer}>
        {monthlyComparisonData.map((data, index) => {
          const maxValue = Math.max(
            ...monthlyComparisonData.map(d => Math.max(d.income, d.expense))
          );
          const incomeHeight = (data.income / maxValue) * 150; // 150px is max bar height
          const expenseHeight = (data.expense / maxValue) * 150;
          const containerWidth = (CARD_WIDTH - 32) / monthlyComparisonData.length;

          return (
            <View 
              key={index} 
              style={[
                styles.barGroup,
                { width: containerWidth },
                selectedTimeIndex === index && styles.selectedBarGroup,
              ]}
            >
              <View style={styles.barColumn}>
                <View style={styles.barWrapper}>
                  <View 
                    style={[
                      styles.bar, 
                      styles.incomeBar, 
                      { height: incomeHeight }
                    ]} 
                  />
                </View>
              </View>
              
              <View style={styles.barColumn}>
                <View style={styles.barWrapper}>
                  <View 
                    style={[
                      styles.bar, 
                      styles.expenseBar, 
                      { height: expenseHeight }
                    ]} 
                  />
                </View>
              </View>
              
              <Text style={styles.barMonth}>{data.month}</Text>
            </View>
          );
        })}
      </Card>
      
      {/* Chart legend */}
      <View style={styles.barChartLegend}>
        <View style={styles.legendRow}>
          <View style={[styles.legendColorBox, styles.incomeLegendBox]} />
          <Text style={styles.legendText}>Thu nhập</Text>
        </View>
        <View style={styles.legendRow}>
          <View style={[styles.legendColorBox, styles.expenseLegendBox]} />
          <Text style={styles.legendText}>Chi tiêu</Text>
        </View>
      </View>
    </View>
  );
  
  const renderSavingsTrendChart = () => (
    <View style={styles.chartSection}>
      <View style={styles.timeFilterContainer}>
        <TouchableOpacity
          style={[styles.filterButton, timeFilter === 'week' && styles.activeFilterButton]}
          onPress={() => setTimeFilter('week')}
        >
          <Text style={[styles.filterText, timeFilter === 'week' && styles.activeFilterText]}>
            Tuần
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.filterButton, timeFilter === 'month' && styles.activeFilterButton]}
          onPress={() => setTimeFilter('month')}
        >
          <Text style={[styles.filterText, timeFilter === 'month' && styles.activeFilterText]}>
            Tháng
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.filterButton, timeFilter === 'year' && styles.activeFilterButton]}
          onPress={() => setTimeFilter('year')}
        >
          <Text style={[styles.filterText, timeFilter === 'year' && styles.activeFilterText]}>
            Năm
          </Text>
        </TouchableOpacity>
      </View>
      
      {/* Summary card */}
      <Card style={styles.summaryCard}>
        <View style={styles.summaryRow}>
          <View style={styles.summaryItem}>
            <Text style={styles.summaryLabel}>Tổng tiết kiệm</Text>
            <Text style={styles.summaryAmount}>{formatCurrency(10500000)}</Text>
          </View>
          <View style={styles.summaryItem}>
            <Text style={styles.summaryLabel}>Tăng trưởng</Text>
            <Text style={styles.positiveGrowth}>+15%</Text>
          </View>
        </View>
        <View style={styles.summaryRow}>
          <View style={styles.summaryItem}>
            <Text style={styles.summaryLabel}>Trung bình/tháng</Text>
            <Text style={styles.summaryAmount}>{formatCurrency(1750000)}</Text>
          </View>
          <View style={styles.summaryItem}>
            <Text style={styles.summaryLabel}>Dự kiến năm nay</Text>
            <Text style={styles.summaryAmount}>{formatCurrency(21000000)}</Text>
          </View>
        </View>
      </Card>
      
      {/* Line chart */}
      <Card style={styles.lineChartContainer}>
        <View style={styles.lineChartHeader}>
          <Text style={styles.lineChartTitle}>Biểu đồ tiết kiệm</Text>
          <View style={styles.lineChartPeriod}>
            <Text style={styles.lineChartPeriodText}>6 tháng gần nhất</Text>
          </View>
        </View>
        
        <View style={styles.lineChart}>
          {/* Simple line chart visualization */}
          <View style={styles.lineChartGrid}>
            {[...Array(5)].map((_, i) => (
              <View key={i} style={styles.gridLine} />
            ))}
          </View>
          
          {/* Line path (simplified) */}
          <View style={styles.linePath} />
          
          {/* Data points */}
          <View style={[styles.dataPoint, { left: '0%', bottom: '20%' }]} />
          <View style={[styles.dataPoint, { left: '20%', bottom: '35%' }]} />
          <View style={[styles.dataPoint, { left: '40%', bottom: '30%' }]} />
          <View style={[styles.dataPoint, { left: '60%', bottom: '50%' }]} />
          <View style={[styles.dataPoint, { left: '80%', bottom: '65%' }]} />
          <View style={[styles.dataPoint, { left: '100%', bottom: '75%' }]} />
          
          {/* X-axis labels */}
          <View style={styles.xAxisLabels}>
            {monthlyComparisonData.map((data, index) => (
              <Text key={index} style={styles.axisLabel}>{data.month}</Text>
            ))}
          </View>
        </View>
      </Card>
      
      {/* Daily expenses list for detailed view */}
      <Card style={styles.detailedDataContainer}>
        <Text style={styles.detailedDataTitle}>Chi tiết theo ngày (Tháng 5)</Text>
        
        <View style={styles.dayListHeader}>
          <Text style={styles.dayHeaderDate}>Ngày</Text>
          <Text style={styles.dayHeaderAmount}>Số tiền</Text>
        </View>
        
        {dailyExpenseData.map((day, index) => (
          <View key={index} style={styles.dayItem}>
            <Text style={styles.dayDate}>{day.day}</Text>
            <Text style={styles.dayAmount}>{formatCurrency(day.amount)}</Text>
          </View>
        ))}
      </Card>
    </View>
  );
  
  const renderChartContent = () => {
    switch (type) {
      case 'expense-categories':
        return renderExpenseCategoriesChart();
      case 'income-expense':
        return renderIncomeExpenseChart();
      case 'savings-trend':
        return renderSavingsTrendChart();
      default:
        return (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>Không tìm thấy biểu đồ phù hợp</Text>
          </View>
        );
    }
  };

  return (
    <Screen>
      <ScrollView 
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.header}>
          <Text style={styles.title}>{getChartTitle()}</Text>
          <Text style={styles.subtitle}>
            Phân tích chi tiết và xu hướng theo thời gian
          </Text>
        </View>
        
        {renderChartContent()}
      </ScrollView>
    </Screen>
  );
};

const styles = StyleSheet.create({
  scrollContainer: {
    flexGrow: 1,
    paddingBottom: theme.spacing.xl,
  },
  header: {
    paddingHorizontal: theme.spacing.md,
    paddingTop: theme.spacing.md,
    paddingBottom: theme.spacing.md,
  },
  title: {
    fontSize: theme.typography.fontSizes['2xl'],
    fontWeight: '700',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.sm,
  },
  subtitle: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.secondary,
  },
  chartSection: {
    marginTop: theme.spacing.md,
  },
  // Pie Chart Styles
  pieChartContainer: {
    alignItems: 'center',
    paddingHorizontal: theme.spacing.md,
  },
  pieChart: {
    width: 200,
    height: 200,
    borderRadius: 100,
    backgroundColor: theme.colors.gray[100],
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: theme.spacing.xl,
    overflow: 'hidden',
    position: 'relative',
  },
  pieSlice: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    borderRadius: 100,
    left: 0,
    top: 0,
    overflow: 'hidden',
  },
  pieChartCenter: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: theme.colors.background.primary,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  pieChartCenterText: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '600',
    color: theme.colors.text.primary,
  },
  categoryLegend: {
    width: '100%',
    marginBottom: theme.spacing.xl,
    padding: theme.spacing.md,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  legendColorBox: {
    width: 16,
    height: 16,
    borderRadius: theme.borderRadius.sm,
    marginRight: theme.spacing.sm,
  },
  legendTextContainer: {
    flex: 1,
  },
  legendCategoryName: {
    fontSize: theme.typography.fontSizes.sm,
    fontWeight: '500',
    color: theme.colors.text.primary,
  },
  legendCategoryPercentage: {
    fontSize: theme.typography.fontSizes.xs,
    color: theme.colors.text.secondary,
  },
  legendCategoryAmount: {
    fontSize: theme.typography.fontSizes.sm,
    fontWeight: '500',
    color: theme.colors.text.primary,
  },
  totalContainer: {
    width: '100%',
    backgroundColor: theme.colors.primary[100],
    padding: theme.spacing.md,
    alignItems: 'center',
  },
  totalLabel: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.primary[700],
    marginBottom: theme.spacing.sm,
  },
  totalAmount: {
    fontSize: theme.typography.fontSizes['2xl'],
    fontWeight: '700',
    color: theme.colors.primary[500],
  },
  
  // Income vs Expense Chart Styles
  timeButtonsContainer: {
    paddingHorizontal: theme.spacing.md,
    marginBottom: theme.spacing.md,
  },
  timeButton: {
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.md,
    backgroundColor: theme.colors.gray[100],
    borderRadius: theme.borderRadius.full,
    marginRight: theme.spacing.sm,
  },
  selectedTimeButton: {
    backgroundColor: theme.colors.primary[500],
  },
  timeButtonText: {
    fontSize: theme.typography.fontSizes.sm,
    fontWeight: '500',
    color: theme.colors.text.secondary,
  },
  selectedTimeButtonText: {
    color: theme.colors.background.primary,
  },
  selectedMonthCard: {
    margin: theme.spacing.md,
    marginTop: 0,
  },
  selectedMonthTitle: {
    fontSize: theme.typography.fontSizes.lg,
    fontWeight: '600',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.md,
    textAlign: 'center',
  },
  compareContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: theme.spacing.xl,
  },
  compareItem: {
    flex: 1,
    alignItems: 'center',
  },
  compareLabel: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
    marginBottom: theme.spacing.sm,
  },
  incomeAmount: {
    fontSize: theme.typography.fontSizes.lg,
    fontWeight: '600',
    color: theme.colors.success[500],
  },
  expenseAmount: {
    fontSize: theme.typography.fontSizes.lg,
    fontWeight: '600',
    color: theme.colors.error[500],
  },
  compareDivider: {
    width: 1,
    backgroundColor: theme.colors.gray[200],
    marginHorizontal: theme.spacing.md,
  },
  savingsContainer: {
    alignItems: 'center',
    padding: theme.spacing.md,
    backgroundColor: theme.colors.primary[100],
    borderRadius: theme.borderRadius.md,
  },
  savingsLabel: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.primary[700],
    marginBottom: theme.spacing.sm,
  },
  savingsAmount: {
    fontSize: theme.typography.fontSizes['2xl'],
    fontWeight: '700',
    color: theme.colors.primary[500],
    marginBottom: theme.spacing.sm,
  },
  savingsPercentContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
  },
  savingsPercent: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '600',
    color: theme.colors.success[500],
    marginRight: theme.spacing.xs,
  },
  savingsPercentLabel: {
    fontSize: theme.typography.fontSizes.xs,
    color: theme.colors.text.secondary,
  },
  barChartContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    height: 200,
    margin: theme.spacing.md,
    marginTop: theme.spacing.md,
    marginBottom: theme.spacing.md,
    padding: theme.spacing.md,
  },
  barGroup: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    justifyContent: 'center',
  },
  selectedBarGroup: {
    backgroundColor: theme.colors.primary[50],
    borderRadius: theme.borderRadius.md,
  },
  barColumn: {
    width: 12,
    alignItems: 'center',
    marginHorizontal: 2,
  },
  barWrapper: {
    height: 150,
    alignItems: 'center',
    justifyContent: 'flex-end',
  },
  bar: {
    width: '100%',
    borderTopLeftRadius: theme.borderRadius.sm,
    borderTopRightRadius: theme.borderRadius.sm,
  },
  incomeBar: {
    backgroundColor: theme.colors.success[500],
  },
  expenseBar: {
    backgroundColor: theme.colors.error[500],
  },
  barMonth: {
    position: 'absolute',
    bottom: -20,
    fontSize: theme.typography.fontSizes.xs,
    color: theme.colors.text.secondary,
    width: '100%',
    textAlign: 'center',
  },
  barChartLegend: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: theme.spacing.xl,
    marginBottom: theme.spacing.md,
  },
  legendRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: theme.spacing.sm,
  },
  incomeLegendBox: {
    backgroundColor: theme.colors.success[500],
  },
  expenseLegendBox: {
    backgroundColor: theme.colors.error[500],
  },
  legendText: {
    fontSize: theme.typography.fontSizes.xs,
    color: theme.colors.text.secondary,
    marginLeft: theme.spacing.xs,
  },
  
  // Savings Trend Chart Styles
  timeFilterContainer: {
    flexDirection: 'row',
    paddingHorizontal: theme.spacing.md,
    marginBottom: theme.spacing.md,
  },
  filterButton: {
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.md,
    borderRadius: theme.borderRadius.full,
    marginRight: theme.spacing.sm,
    backgroundColor: theme.colors.gray[100],
  },
  activeFilterButton: {
    backgroundColor: theme.colors.primary[100],
  },
  filterText: {
    fontSize: theme.typography.fontSizes.sm,
    fontWeight: '500',
    color: theme.colors.text.secondary,
  },
  activeFilterText: {
    color: theme.colors.primary[500],
  },
  summaryCard: {
    margin: theme.spacing.md,
    marginTop: 0,
    padding: theme.spacing.md,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: theme.spacing.md,
  },
  summaryItem: {
    flex: 1,
    alignItems: 'center',
  },
  summaryLabel: {
    fontSize: theme.typography.fontSizes.xs,
    color: theme.colors.text.secondary,
    marginBottom: theme.spacing.xs,
  },
  summaryAmount: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '600',
    color: theme.colors.text.primary,
  },
  positiveGrowth: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '600',
    color: theme.colors.success[500],
  },
  lineChartContainer: {
    margin: theme.spacing.md,
    marginTop: 0,
    padding: theme.spacing.md,
  },
  lineChartHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  lineChartTitle: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '600',
    color: theme.colors.text.primary,
  },
  lineChartPeriod: {
    backgroundColor: theme.colors.gray[100],
    paddingVertical: theme.spacing.xs,
    paddingHorizontal: theme.spacing.sm,
    borderRadius: theme.borderRadius.md,
  },
  lineChartPeriodText: {
    fontSize: theme.typography.fontSizes.xs,
    color: theme.colors.text.secondary,
  },
  lineChart: {
    height: 200,
    position: 'relative',
    marginBottom: theme.spacing.md,
  },
  lineChartGrid: {
    height: '100%',
    position: 'absolute',
    width: '100%',
    justifyContent: 'space-between',
  },
  gridLine: {
    width: '100%',
    height: 1,
    backgroundColor: theme.colors.gray[100],
  },
  linePath: {
    position: 'absolute',
    bottom: '20%',
    left: '0%',
    right: '0%',
    height: 2,
    backgroundColor: theme.colors.primary[500],
    borderRadius: 1,
    transform: [
      { translateY: -50 },
      { rotate: '10deg' },
    ],
  },
  dataPoint: {
    position: 'absolute',
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: theme.colors.primary[500],
    borderWidth: 2,
    borderColor: theme.colors.background.primary,
  },
  xAxisLabels: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    position: 'absolute',
    bottom: -20,
    left: 0,
    right: 0,
  },
  axisLabel: {
    fontSize: theme.typography.fontSizes.xs,
    color: theme.colors.text.secondary,
    textAlign: 'center',
    width: '16.6%', // 100% / 6 months
  },
  detailedDataContainer: {
    margin: theme.spacing.md,
    marginTop: 0,
    padding: theme.spacing.md,
  },
  detailedDataTitle: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '600',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.md,
  },
  dayListHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: theme.spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray[200],
    marginBottom: theme.spacing.sm,
  },
  dayHeaderDate: {
    fontSize: theme.typography.fontSizes.sm,
    fontWeight: '500',
    color: theme.colors.text.secondary,
  },
  dayHeaderAmount: {
    fontSize: theme.typography.fontSizes.sm,
    fontWeight: '500',
    color: theme.colors.text.secondary,
  },
  dayItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: theme.spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray[100],
  },
  dayDate: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.primary,
  },
  dayAmount: {
    fontSize: theme.typography.fontSizes.sm,
    fontWeight: '500',
    color: theme.colors.text.primary,
  },
  errorContainer: {
    padding: theme.spacing.md,
    alignItems: 'center',
    justifyContent: 'center',
    height: 300,
  },
  errorText: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.secondary,
    textAlign: 'center',
  },
});

export default ChartDetailScreen; 