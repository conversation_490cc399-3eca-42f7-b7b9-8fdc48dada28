import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Switch,
  ScrollView,
  SafeAreaView,
  Alert,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { StatusBar } from 'expo-status-bar';
import { RootStackParamList, AuthStackParamList } from '../navigation/types';
import theme from '../theme';
import Screen from '../components/common/Screen';
import DrawerHeader from '../components/common/DrawerHeader';

type MoreScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList & { Login: undefined }
>;

const MoreScreen = () => {
  const navigation = useNavigation<MoreScreenNavigationProp>();

  const [darkMode, setDarkMode] = useState(false);
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [biometricEnabled, setBiometricEnabled] = useState(false);
  const [autoBackup, setAutoBackup] = useState(true);

  // Mock user data
  const userData = {
    name: '<PERSON><PERSON><PERSON>n Văn A',
    email: '<EMAIL>',
    phone: '+84 123 456 789',
    avatar: '👤',
  };

  const handleLogout = () => {
    Alert.alert(
      'Đăng xuất',
      'Bạn có chắc chắn muốn đăng xuất?',
      [
        {
          text: 'Hủy',
          style: 'cancel',
        },
        {
          text: 'Đăng xuất',
          onPress: () => {
            // Navigate to Login screen
            navigation.navigate('Login');
          },
          style: 'destructive',
        },
      ],
      { cancelable: true }
    );
  };

  const handleExportData = () => {
    Alert.alert(
      'Xuất dữ liệu',
      'Dữ liệu của bạn sẽ được xuất ra dưới dạng file CSV. Bạn có muốn tiếp tục?',
      [
        {
          text: 'Hủy',
          style: 'cancel',
        },
        {
          text: 'Xuất',
          onPress: () => {
            // Code to export data
            Alert.alert('Thành công', 'Dữ liệu đã được xuất ra thành công!');
          },
        },
      ],
      { cancelable: true }
    );
  };

  const handleDeleteAccount = () => {
    Alert.alert(
      'Xóa tài khoản',
      'Bạn có chắc chắn muốn xóa tài khoản? Hành động này không thể hoàn tác và tất cả dữ liệu của bạn sẽ bị mất.',
      [
        {
          text: 'Hủy',
          style: 'cancel',
        },
        {
          text: 'Xóa',
          onPress: () => {
            // Code to delete account
            navigation.navigate('Login');
          },
          style: 'destructive',
        },
      ],
      { cancelable: true }
    );
  };

  return (
    <Screen>
      <DrawerHeader title="More" />
      <StatusBar style="dark" />
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <Text style={styles.title}>Cài đặt</Text>
          <Text style={styles.subtitle}>Quản lý tài khoản và tùy chỉnh ứng dụng</Text>
        </View>

        {/* User Profile Section */}
        <View style={styles.profileSection}>
          <View style={styles.avatarContainer}>
            <Text style={styles.avatarText}>{userData.avatar}</Text>
          </View>
          <View style={styles.userInfoContainer}>
            <Text style={styles.userName}>{userData.name}</Text>
            <Text style={styles.userEmail}>{userData.email}</Text>
            <Text style={styles.userPhone}>{userData.phone}</Text>
          </View>
          <TouchableOpacity
            style={styles.editProfileButton}
            onPress={() => Alert.alert('Thông báo', 'Tính năng đang được phát triển')}
          >
            <Text style={styles.editProfileText}>Chỉnh sửa</Text>
          </TouchableOpacity>
        </View>

        {/* Settings Sections */}
        <View style={styles.settingsSection}>
          <Text style={styles.sectionTitle}>Giao diện</Text>

          <View style={styles.settingItem}>
            <Text style={styles.settingLabel}>Chế độ tối</Text>
            <Switch
              value={darkMode}
              onValueChange={setDarkMode}
              trackColor={{ false: theme.colors.gray[200], true: theme.colors.primary[100] }}
              thumbColor={darkMode ? theme.colors.primary[500] : theme.colors.background.primary}
            />
          </View>
        </View>

        <View style={styles.settingsSection}>
          <Text style={styles.sectionTitle}>Thông báo</Text>

          <View style={styles.settingItem}>
            <Text style={styles.settingLabel}>Bật thông báo</Text>
            <Switch
              value={notificationsEnabled}
              onValueChange={setNotificationsEnabled}
              trackColor={{ false: theme.colors.gray[200], true: theme.colors.primary[100] }}
              thumbColor={notificationsEnabled ? theme.colors.primary[500] : theme.colors.background.primary}
            />
          </View>

          <TouchableOpacity
            style={[
              styles.settingItem,
              { opacity: notificationsEnabled ? 1 : 0.5 }
            ]}
            disabled={!notificationsEnabled}
            onPress={() => Alert.alert('Thông báo', 'Tính năng đang được phát triển')}
          >
            <Text style={styles.settingLabel}>Tùy chỉnh thông báo</Text>
            <Text style={styles.settingArrow}>›</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.settingsSection}>
          <Text style={styles.sectionTitle}>Bảo mật</Text>

          <View style={styles.settingItem}>
            <Text style={styles.settingLabel}>Đăng nhập sinh trắc học</Text>
            <Switch
              value={biometricEnabled}
              onValueChange={setBiometricEnabled}
              trackColor={{ false: theme.colors.gray[200], true: theme.colors.primary[100] }}
              thumbColor={biometricEnabled ? theme.colors.primary[500] : theme.colors.background.primary}
            />
          </View>

          <TouchableOpacity
            style={styles.settingItem}
            onPress={() => Alert.alert('Thông báo', 'Tính năng đang được phát triển')}
          >
            <Text style={styles.settingLabel}>Đổi mật khẩu</Text>
            <Text style={styles.settingArrow}>›</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.settingsSection}>
          <Text style={styles.sectionTitle}>Dữ liệu</Text>

          <View style={styles.settingItem}>
            <Text style={styles.settingLabel}>Tự động sao lưu</Text>
            <Switch
              value={autoBackup}
              onValueChange={setAutoBackup}
              trackColor={{ false: theme.colors.gray[200], true: theme.colors.primary[100] }}
              thumbColor={autoBackup ? theme.colors.primary[500] : theme.colors.background.primary}
            />
          </View>

          <TouchableOpacity
            style={styles.settingItem}
            onPress={handleExportData}
          >
            <Text style={styles.settingLabel}>Xuất dữ liệu</Text>
            <Text style={styles.settingArrow}>›</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.settingsSection}>
          <Text style={styles.sectionTitle}>Ứng dụng</Text>

          <TouchableOpacity
            style={styles.settingItem}
            onPress={() => Alert.alert('Thông báo', 'Tính năng đang được phát triển')}
          >
            <Text style={styles.settingLabel}>Ngôn ngữ</Text>
            <View style={styles.settingValueContainer}>
              <Text style={styles.settingValue}>Tiếng Việt</Text>
              <Text style={styles.settingArrow}>›</Text>
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.settingItem}
            onPress={() => Alert.alert('Thông báo', 'Tính năng đang được phát triển')}
          >
            <Text style={styles.settingLabel}>Đơn vị tiền tệ</Text>
            <View style={styles.settingValueContainer}>
              <Text style={styles.settingValue}>VND (₫)</Text>
              <Text style={styles.settingArrow}>›</Text>
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.settingItem}
            onPress={() => Alert.alert('Thông báo', 'Phiên bản 1.0.0')}
          >
            <Text style={styles.settingLabel}>Phiên bản</Text>
            <Text style={styles.settingValue}>1.0.0</Text>
          </TouchableOpacity>
        </View>

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <TouchableOpacity
            style={styles.logoutButton}
            onPress={handleLogout}
          >
            <Text style={styles.logoutButtonText}>Đăng xuất</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.deleteAccountButton}
            onPress={handleDeleteAccount}
          >
            <Text style={styles.deleteAccountButtonText}>Xóa tài khoản</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </Screen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background.primary,
  },
  header: {
    paddingHorizontal: theme.spacing.xl,
    paddingTop: theme.spacing.xl,
    paddingBottom: theme.spacing.md,
  },
  title: {
    fontSize: theme.typography.fontSizes['2xl'],
    fontWeight: '700',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.sm,
  },
  subtitle: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.secondary,
  },
  profileSection: {
    backgroundColor: theme.colors.background.primary,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.xl,
    marginHorizontal: theme.spacing.xl,
    marginBottom: theme.spacing.xl,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
  },
  avatarContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: theme.colors.primary[100],
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: theme.spacing.md,
  },
  avatarText: {
    fontSize: 30,
  },
  userInfoContainer: {
    flex: 1,
  },
  userName: {
    fontSize: theme.typography.fontSizes.lg,
    fontWeight: '600',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.xs,
  },
  userEmail: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
    marginBottom: theme.spacing.xs,
  },
  userPhone: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
  },
  editProfileButton: {
    backgroundColor: theme.colors.gray[100],
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
  },
  editProfileText: {
    fontSize: theme.typography.fontSizes.sm,
    fontWeight: '500',
    color: theme.colors.text.primary,
  },
  settingsSection: {
    backgroundColor: theme.colors.background.primary,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.xl,
    marginHorizontal: theme.spacing.xl,
    marginBottom: theme.spacing.md,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '600',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.md,
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: theme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray[100],
  },
  settingLabel: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.primary,
  },
  settingValueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingValue: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
    marginRight: theme.spacing.sm,
  },
  settingArrow: {
    fontSize: 18,
    color: theme.colors.gray[400],
    marginLeft: theme.spacing.xs,
  },
  actionButtons: {
    marginHorizontal: theme.spacing.xl,
    marginBottom: theme.spacing.xl,
    marginTop: theme.spacing.sm,
  },
  logoutButton: {
    backgroundColor: theme.colors.gray[100],
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.md,
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  logoutButtonText: {
    color: theme.colors.text.primary,
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '600',
  },
  deleteAccountButton: {
    backgroundColor: theme.colors.error[50],
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.md,
    alignItems: 'center',
  },
  deleteAccountButtonText: {
    color: theme.colors.error[500],
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '600',
  },
});

export default MoreScreen;