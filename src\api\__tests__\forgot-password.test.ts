import { AuthAPI, ForgotPasswordDto, VerifyOtpDto, ResetPasswordDto } from '../auth.api';

// Mock axios
jest.mock('../lib/axios');

describe('Forgot Password Flow', () => {
  const mockEmail = '<EMAIL>';
  const mockOtp = '123456';
  const mockNewPassword = 'newPassword123';

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('forgotPassword', () => {
    it('should send forgot password request successfully', async () => {
      const mockAxios = require('../lib/axios').default;
      mockAxios.post.mockResolvedValue({ data: true });

      const forgotPasswordData: ForgotPasswordDto = { email: mockEmail };
      const result = await AuthAPI.forgotPassword(forgotPasswordData);

      expect(mockAxios.post).toHaveBeenCalledWith('/auth/forgot-password', forgotPasswordData);
      expect(result).toBe(true);
    });

    it('should handle email not found error', async () => {
      const mockAxios = require('../lib/axios').default;
      mockAxios.post.mockRejectedValue({
        response: {
          status: 400,
          data: { message: 'Email does not exist in the system' }
        }
      });

      const forgotPasswordData: ForgotPasswordDto = { email: '<EMAIL>' };

      await expect(AuthAPI.forgotPassword(forgotPasswordData)).rejects.toMatchObject({
        response: {
          status: 400,
          data: { message: 'Email does not exist in the system' }
        }
      });
    });
  });

  describe('verifyOtp', () => {
    it('should verify OTP successfully', async () => {
      const mockAxios = require('../lib/axios').default;
      mockAxios.post.mockResolvedValue({ data: true });

      const verifyOtpData: VerifyOtpDto = { email: mockEmail, otp: mockOtp };
      const result = await AuthAPI.verifyOtp(verifyOtpData);

      expect(mockAxios.post).toHaveBeenCalledWith('/auth/verify-otp', verifyOtpData);
      expect(result).toBe(true);
    });

    it('should handle incorrect OTP error', async () => {
      const mockAxios = require('../lib/axios').default;
      mockAxios.post.mockRejectedValue({
        response: {
          status: 400,
          data: { message: 'Incorrect OTP code' }
        }
      });

      const verifyOtpData: VerifyOtpDto = { email: mockEmail, otp: 'wrong123' };

      await expect(AuthAPI.verifyOtp(verifyOtpData)).rejects.toMatchObject({
        response: {
          status: 400,
          data: { message: 'Incorrect OTP code' }
        }
      });
    });

    it('should handle expired OTP error', async () => {
      const mockAxios = require('../lib/axios').default;
      mockAxios.post.mockRejectedValue({
        response: {
          status: 400,
          data: { message: 'OTP has expired' }
        }
      });

      const verifyOtpData: VerifyOtpDto = { email: mockEmail, otp: mockOtp };

      await expect(AuthAPI.verifyOtp(verifyOtpData)).rejects.toMatchObject({
        response: {
          status: 400,
          data: { message: 'OTP has expired' }
        }
      });
    });
  });

  describe('resetPassword', () => {
    it('should reset password successfully', async () => {
      const mockAxios = require('../lib/axios').default;
      mockAxios.post.mockResolvedValue({ data: true });

      const resetPasswordData: ResetPasswordDto = {
        email: mockEmail,
        otp: mockOtp,
        newPassword: mockNewPassword
      };
      const result = await AuthAPI.resetPassword(resetPasswordData);

      expect(mockAxios.post).toHaveBeenCalledWith('/auth/reset-password', resetPasswordData);
      expect(result).toBe(true);
    });

    it('should handle same password error', async () => {
      const mockAxios = require('../lib/axios').default;
      mockAxios.post.mockRejectedValue({
        response: {
          status: 400,
          data: { message: 'New password must be different from the current password' }
        }
      });

      const resetPasswordData: ResetPasswordDto = {
        email: mockEmail,
        otp: mockOtp,
        newPassword: 'currentPassword'
      };

      await expect(AuthAPI.resetPassword(resetPasswordData)).rejects.toMatchObject({
        response: {
          status: 400,
          data: { message: 'New password must be different from the current password' }
        }
      });
    });
  });

  describe('Complete Flow Integration', () => {
    it('should complete the entire forgot password flow', async () => {
      const mockAxios = require('../lib/axios').default;
      
      // Step 1: Send forgot password request
      mockAxios.post.mockResolvedValueOnce({ data: true });
      const forgotResult = await AuthAPI.forgotPassword({ email: mockEmail });
      expect(forgotResult).toBe(true);

      // Step 2: Verify OTP
      mockAxios.post.mockResolvedValueOnce({ data: true });
      const verifyResult = await AuthAPI.verifyOtp({ email: mockEmail, otp: mockOtp });
      expect(verifyResult).toBe(true);

      // Step 3: Reset password
      mockAxios.post.mockResolvedValueOnce({ data: true });
      const resetResult = await AuthAPI.resetPassword({
        email: mockEmail,
        otp: mockOtp,
        newPassword: mockNewPassword
      });
      expect(resetResult).toBe(true);

      // Verify all API calls were made
      expect(mockAxios.post).toHaveBeenCalledTimes(3);
      expect(mockAxios.post).toHaveBeenNthCalledWith(1, '/auth/forgot-password', { email: mockEmail });
      expect(mockAxios.post).toHaveBeenNthCalledWith(2, '/auth/verify-otp', { email: mockEmail, otp: mockOtp });
      expect(mockAxios.post).toHaveBeenNthCalledWith(3, '/auth/reset-password', {
        email: mockEmail,
        otp: mockOtp,
        newPassword: mockNewPassword
      });
    });
  });
});
