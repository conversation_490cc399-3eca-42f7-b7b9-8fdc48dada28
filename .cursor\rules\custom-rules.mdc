---
description: 
globs: 
alwaysApply: false
---
# Project Development Rules & Guidelines

## Table of Contents
1. [Project Overview](mdc:#project-overview)
2. [Project Structure](mdc:#project-structure)
3. [Dependencies & Libraries](mdc:#dependencies--libraries)
4. [Development Patterns](mdc:#development-patterns)
5. [Screen Development Guidelines](mdc:#screen-development-guidelines)
6. [Component Development](mdc:#component-development)
7. [API Integration](mdc:#api-integration)
8. [Navigation System](mdc:#navigation-system)
9. [Theme & Styling](mdc:#theme--styling)
10. [TypeScript Guidelines](mdc:#typescript-guidelines)
11. [Error Handling](mdc:#error-handling)
12. [Performance Guidelines](mdc:#performance-guidelines)
13. [Testing Guidelines](mdc:#testing-guidelines)
14. [Common Pitfalls & Solutions](mdc:#common-pitfalls--solutions)

## Project Overview

This is a **React Native Expo** project with **TypeScript**, **NativeWind**, and a custom theme system. It's designed as a finance management application with features like budget tracking, goal setting, transaction management, and analytics.

### Tech Stack
- **React Native**: 0.79.2
- **Expo**: ~53.0.9
- **TypeScript**: ~5.8.3
- **React Navigation**: v7.x
- **NativeWind**: v4.1.23
- **React Hook Form**: v7.56.4
- **Axios**: v1.9.0

## Project Structure

```
finance-app-fe/
├── app/                           # Main application code
│   ├── components/               # Reusable components
│   │   ├── common/              # Common components (Screen, Header, etc.)
│   │   ├── ui/                  # UI components (Button, TextInput, Card)
│   │   ├── icons/               # Icon components
│   │   ├── categories/          # Category-specific components
│   │   └── goals/               # Goal-specific components
│   ├── screens/                 # Screen components
│   ├── navigation/              # Navigation configuration
│   │   ├── types.ts             # Navigation type definitions
│   │   ├── RootNavigator.tsx    # Main stack navigator
│   │   ├── DrawerNavigator.tsx  # Drawer navigation
│   │   ├── TabNavigator.tsx     # Tab navigation
│   │   └── AuthNavigator.tsx    # Authentication navigation
│   ├── theme/                   # Theme system
│   │   └── index.ts             # Main theme configuration
│   ├── constants/               # App constants
│   │   └── strings.ts           # String constants/i18n
│   ├── services/                # Business logic services
│   ├── types/                   # App-specific types
│   └── utils/                   # Utility functions
├── src/                         # Source code for backend integration
│   ├── api/                     # API layer
│   │   ├── config.ts            # API configuration
│   │   ├── auth.api.ts          # Authentication API
│   │   ├── user.api.ts          # User API
│   │   ├── goals.api.ts         # Goals API
│   │   ├── budget.api.ts        # Budget API
│   │   └── category.api.ts      # Category API
│   ├── lib/                     # Library configurations
│   │   └── axios.ts             # Axios configuration
│   └── types/                   # Backend type definitions
│       ├── api.types.ts         # API response types
│       └── user.types.ts        # User-related types
├── assets/                      # Static assets
├── android/                     # Android-specific files
├── package.json                 # Dependencies and scripts
├── tsconfig.json               # TypeScript configuration
├── app.json                    # Expo configuration
├── babel.config.js             # Babel configuration
├── metro.config.js             # Metro configuration
├── tailwind.config.js          # TailwindCSS configuration
└── THEME_GUIDE.md              # Theme usage guide
```

## Dependencies & Libraries

### ✅ AVAILABLE LIBRARIES (Must Use These)

#### Navigation
```typescript
"@react-navigation/native": "^7.1.9"
"@react-navigation/native-stack": "^7.3.13"
"@react-navigation/drawer": "^7.3.12"
"@react-navigation/bottom-tabs": "^7.3.13"
```

#### UI & Icons
```typescript
"@expo/vector-icons": "^14.1.0"  // ✅ USE THIS FOR ICONS
"react-native-paper": "^5.14.5"
"react-native-svg": "15.11.2"
"nativewind": "^4.1.23"
```

#### Forms & Input
```typescript
"react-hook-form": "^7.56.4"
"@react-native-community/datetimepicker": "^8.3.0"
"react-native-keyboard-aware-scroll-view": "^0.9.5"
```

#### Data & API
```typescript
"axios": "^1.9.0"
"@react-native-async-storage/async-storage": "^2.1.2"
"js-cookie": "^3.0.5"
```

#### Media & Files
```typescript
"expo-image-picker": "^16.1.4"  // ✅ RECENTLY ADDED
"expo-font": "^13.3.1"
```

#### Charts & Visualization
```typescript
"react-native-chart-kit": "^6.12.0"  // ✅ RECENTLY ADDED
```

#### Date & Time
```typescript
"date-fns": "^4.1.0"
```

#### Development
```typescript
"typescript": "~5.8.3"
"@types/react": "~19.0.10"
"@types/react-native": "^0.72.8"
```

### ❌ DO NOT INSTALL WITHOUT CHECKING
- Any duplicate UI libraries
- Additional icon libraries (use @expo/vector-icons)
- Different navigation libraries
- Different form libraries

### 🔍 BEFORE ADDING NEW DEPENDENCIES

1. **Check if functionality exists** in current libraries
2. **Read package.json** thoroughly
3. **Check bundle size impact**
4. **Verify React Native compatibility**
5. **Check Expo compatibility**

## Development Patterns

### Import Order
```typescript
// 1. React and React Native core
import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';

// 2. Third-party libraries
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

// 3. Internal theme and components
import theme from '../theme';
import Screen from '../components/common/Screen';
import Card from '../components/ui/Card';

// 4. Types and constants
import { RootStackParamList } from '../navigation/types';
import STRINGS from '../constants/strings';

// 5. API and services
import { UserAPI } from '../../src/api/user.api';
```

### File Naming Conventions
- **Components**: PascalCase (e.g., `UserProfile.tsx`)
- **Screens**: PascalCase with "Screen" suffix (e.g., `HomeScreen.tsx`)
- **Utilities**: camelCase (e.g., `formatCurrency.ts`)
- **Constants**: camelCase (e.g., `apiEndpoints.ts`)
- **Types**: camelCase with `.types.ts` suffix (e.g., `user.types.ts`)

## Screen Development Guidelines

### 1. Screen Template Structure

```typescript
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

import theme from '../theme';
import Screen from '../components/common/Screen';
import DrawerHeader from '../components/common/DrawerHeader'; // For main screens
import Card from '../components/ui/Card';
import Button from '../components/ui/Button';
import TextInput from '../components/ui/TextInput';
import { RootStackParamList } from '../navigation/types';
import STRINGS from '../constants/strings';

// Type definition for navigation
type YourScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'YourScreen'
>;

const YourScreen = () => {
  const navigation = useNavigation<YourScreenNavigationProp>();

  // State management
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [data, setData] = useState(null);
  const [error, setError] = useState<string | null>(null);

  // API calls and data loading
  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // API call here
      // const response = await YourAPI.getData();
      // setData(response);
      
    } catch (error: any) {
      setError(error.message || 'An error occurred');
      Alert.alert('Error', error.message || 'An error occurred');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Load data on mount and focus
  useEffect(() => {
    loadData();
  }, []);

  useFocusEffect(
    React.useCallback(() => {
      // Reload data when screen comes into focus
      loadData();
    }, [])
  );

  // Event handlers
  const handleRefresh = () => {
    setRefreshing(true);
    loadData();
  };

  // Loading state
  if (loading && !data) {
    return (
      <Screen>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary[500]} />
          <Text style={styles.loadingText}>Loading...</Text>
        </View>
      </Screen>
    );
  }

  return (
    <Screen>
      <DrawerHeader title="Your Screen" />
      <ScrollView
        style={styles.container}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
      >
        {/* Your content here */}
      </ScrollView>
    </Screen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background.secondary,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: theme.spacing.md,
    color: theme.colors.text.secondary,
    fontSize: theme.typography.fontSizes.md,
  },
});

export default YourScreen;
```

### 2. Screen Requirements Checklist

- [ ] ✅ **Always wrap with `<Screen>` component**
- [ ] ✅ **Use proper navigation typing**
- [ ] ✅ **Include loading states**
- [ ] ✅ **Include error handling**
- [ ] ✅ **Add refresh functionality for data screens**
- [ ] ✅ **Use theme values for all styling**
- [ ] ✅ **Use constants from STRINGS**
- [ ] ✅ **Handle keyboard-aware scrolling when needed**
- [ ] ✅ **Add proper TypeScript types**
- [ ] ✅ **Include proper cleanup in useEffect**

### 3. Navigation Updates

When creating a new screen, update these files:

#### a. Add to Navigation Types (`app/navigation/types.ts`)
```typescript
export type RootStackParamList = {
  // ... existing screens
  YourNewScreen: { id?: string }; // Add your screen here
};
```

#### b. Add to Root Navigator (`app/navigation/RootNavigator.tsx`)
```typescript
// Add import
import YourNewScreen from '../screens/YourNewScreen';

// Add screen definition
<Stack.Screen
  name="YourNewScreen"
  component={YourNewScreen}
  options={{
    headerShown: true,
    title: 'Your Screen Title',
    headerTitleStyle: {
      fontWeight: '600',
      fontSize: 18,
      color: '#111827',
    },
    headerTintColor: '#0EA5E9',
  }}
/>
```

#### c. Add to Drawer Navigator (if it's a main screen)
```typescript
// Add to DrawerNavigator.tsx if it's a main navigation item
```

## Component Development

### 1. UI Component Guidelines

#### Use Existing Components First
```typescript
// ✅ CORRECT - Use existing components
import Button from '../components/ui/Button';
import TextInput from '../components/ui/TextInput';
import Card from '../components/ui/Card';

// ❌ WRONG - Don't create custom buttons/inputs without reason
```

#### Available UI Components
- **Button**: `../components/ui/Button`
- **TextInput**: `../components/ui/TextInput`
- **Card**: `../components/ui/Card`
- **FormInput**: `../components/ui/FormInput`
- **Screen**: `../components/common/Screen`
- **DrawerHeader**: `../components/common/DrawerHeader`
- **EmptyState**: `../components/common/EmptyState`

### 2. Component Creation Template

```typescript
import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import theme from '../../theme';

interface YourComponentProps {
  title: string;
  onPress?: () => void;
  disabled?: boolean;
  children?: React.ReactNode;
  style?: any;
}

const YourComponent: React.FC<YourComponentProps> = ({
  title,
  onPress,
  disabled = false,
  children,
  style,
}) => {
  return (
    <View style={[styles.container, style]}>
      <Text style={styles.title}>{title}</Text>
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: theme.spacing.md,
    backgroundColor: theme.colors.background.primary,
    borderRadius: theme.borderRadius.lg,
  },
  title: {
    fontSize: theme.typography.fontSizes.lg,
    fontWeight: theme.typography.fontWeights.bold,
    color: theme.colors.text.primary,
  },
});

export default YourComponent;
```

## API Integration

### 1. API Structure Pattern

#### API Configuration (`src/api/config.ts`)
```typescript
import { Platform } from 'react-native';

const getBaseUrl = () => {
    if (__DEV__) {
        const LAN_IP = 'YOUR_LOCAL_IP';
        if (Platform.OS === 'android') {
            return `http://${LAN_IP}:8000/api/v1`;
        }
        return `http://${LAN_IP}:8000/api/v1`;
    }
    return 'https://api.yourproductionurl.com';
};

export const API_CONFIG = {
    BASE_URL: getBaseUrl(),
    TEMP_USER_ID: '123e4567-e89b-12d3-a456-426614174000',
    HEADERS: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
    },
};
```

#### API Service Template (`src/api/your.api.ts`)
```typescript
import axios from '../lib/axios';

const YOUR_ENDPOINT = '/your-endpoint';

export interface YourCreateDto {
  name: string;
  value: number;
}

export interface YourUpdateDto {
  name?: string;
  value?: number;
}

export interface YourResponse {
  id: string;
  name: string;
  value: number;
  created_at: string;
  updated_at: string;
}

export const YourAPI = {
  // Get all items
  getAll: async (params?: any): Promise<YourResponse[]> => {
    const response = await axios.get(YOUR_ENDPOINT, { params });
    return response.data.data;
  },

  // Get single item
  getById: async (id: string): Promise<YourResponse> => {
    const response = await axios.get(`${YOUR_ENDPOINT}/${id}`);
    return response.data.data;
  },

  // Create item
  create: async (data: YourCreateDto): Promise<YourResponse> => {
    const response = await axios.post(YOUR_ENDPOINT, data);
    return response.data.data;
  },

  // Update item
  update: async (id: string, data: YourUpdateDto): Promise<YourResponse> => {
    const response = await axios.patch(`${YOUR_ENDPOINT}/${id}`, data);
    return response.data.data;
  },

  // Delete item
  delete: async (id: string): Promise<void> => {
    await axios.delete(`${YOUR_ENDPOINT}/${id}`);
  },
};
```

### 2. Error Handling Pattern

```typescript
const loadData = async () => {
  try {
    setLoading(true);
    setError(null);

    const response = await YourAPI.getData();
    setData(response);

  } catch (error: any) {
    let errorMessage = 'Failed to load data. Please try again.';

    if (error.code === 'NETWORK_ERROR' || error.message?.includes('timeout')) {
      errorMessage = 'Network connection failed. Please check your internet connection and try again.';
    } else if (error.response?.status === 401) {
      errorMessage = 'Authentication failed. Please login again.';
    } else if (error.response?.status >= 500) {
      errorMessage = 'Server error. Please try again later.';
    } else if (error.message) {
      errorMessage = error.message;
    }

    setError(errorMessage);
    Alert.alert('Error', errorMessage);
  } finally {
    setLoading(false);
    setRefreshing(false);
  }
};
```

## Navigation System

### 1. Navigation Structure
- **RootNavigator**: Main stack navigator
- **AuthNavigator**: Authentication flows
- **DrawerNavigator**: Main app navigation (Home, Goals, Budget, etc.)
- **TabNavigator**: Tab-based navigation (if needed)

### 2. Navigation Usage

```typescript
// Type-safe navigation
type YourScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'YourScreen'
>;

const YourScreen = () => {
  const navigation = useNavigation<YourScreenNavigationProp>();

  // Navigate to another screen
  const navigateToDetails = (id: string) => {
    navigation.navigate('DetailScreen', { id });
  };

  // Go back
  const goBack = () => {
    navigation.goBack();
  };

  // Navigate and replace
  const navigateAndReplace = () => {
    navigation.replace('AnotherScreen');
  };
};
```

### 3. Adding New Navigation Routes

1. **Add to types** (`app/navigation/types.ts`)
2. **Add to RootNavigator** (`app/navigation/RootNavigator.tsx`)
3. **Add to drawer/tab navigator** if needed
4. **Update all affected navigation calls**

## Theme & Styling

### 1. Theme Usage Rules

#### ✅ ALWAYS USE THEME VALUES
```typescript
// ✅ CORRECT
const styles = StyleSheet.create({
  container: {
    backgroundColor: theme.colors.background.primary,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
  },
  title: {
    fontSize: theme.typography.fontSizes.xl,
    fontWeight: theme.typography.fontWeights.bold,
    color: theme.colors.text.primary,
  },
});
```

#### ❌ NEVER USE HARDCODED VALUES
```typescript
// ❌ WRONG
const styles = StyleSheet.create({
  container: {
    backgroundColor: '#ffffff',  // Use theme.colors.background.primary
    padding: 16,                 // Use theme.spacing.md
    borderRadius: 12,            // Use theme.borderRadius.lg
  },
  title: {
    fontSize: 20,               // Use theme.typography.fontSizes.xl
    fontWeight: 'bold',         // Use theme.typography.fontWeights.bold
    color: '#111827',           // Use theme.colors.text.primary
  },
});
```

### 2. Available Theme Values

#### Colors
```typescript
theme.colors.primary[500]          // Primary color
theme.colors.background.primary    // Primary background
theme.colors.background.secondary  // Secondary background
theme.colors.text.primary          // Primary text
theme.colors.text.secondary        // Secondary text
theme.colors.success               // Success color
theme.colors.warning               // Warning color
theme.colors.error                 // Error color
```

#### Spacing
```typescript
theme.spacing.xs     // 4px
theme.spacing.sm     // 8px
theme.spacing.md     // 16px
theme.spacing.lg     // 24px
theme.spacing.xl     // 32px
theme.spacing['2xl'] // 48px
```

#### Typography
```typescript
theme.typography.fontSizes.xs      // 12px
theme.typography.fontSizes.sm      // 14px
theme.typography.fontSizes.md      // 16px
theme.typography.fontSizes.lg      // 18px
theme.typography.fontSizes.xl      // 20px

theme.typography.fontWeights.normal   // 'normal'
theme.typography.fontWeights.medium   // '500'
theme.typography.fontWeights.semibold // '600'
theme.typography.fontWeights.bold     // 'bold'
```

#### Border Radius
```typescript
theme.borderRadius.sm   // 4px
theme.borderRadius.md   // 8px
theme.borderRadius.lg   // 12px
theme.borderRadius.xl   // 16px
```

### 3. Icons Usage

#### ✅ USE @expo/vector-icons
```typescript
import { Feather, AntDesign, Ionicons } from '@expo/vector-icons';

// Usage
<Feather name="heart" size={24} color={theme.colors.primary[500]} />
<AntDesign name="home" size={24} color={theme.colors.text.primary} />
<Ionicons name="settings" size={24} color={theme.colors.text.secondary} />
```

#### ❌ DON'T INSTALL ADDITIONAL ICON LIBRARIES

## TypeScript Guidelines

### 1. Interface Definitions

```typescript
// Always define proper interfaces
interface UserData {
  id: string;
  name: string;
  email: string;
  created_at: string;
  updated_at: string;
}

// Use optional properties when needed
interface CreateUserDto {
  name: string;
  email: string;
  password: string;
  avatar?: string;
}

// Extend interfaces when needed
interface ExtendedUserData extends UserData {
  profile_image?: string;
  last_login?: string;
}
```

### 2. Type Safety for Navigation

```typescript
// Always type your navigation props
type ScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'ScreenName'
>;

// Type your route params
type ScreenRouteProp = RouteProp<RootStackParamList, 'ScreenName'>;
```

### 3. API Response Types

```typescript
// Use proper API response types
interface ApiResponse<T> {
  status: string;
  statusCode: number;
  message: string;
  data: T;
  meta?: any;
  timestamp: string;
}

// Usage
const response: ApiResponse<UserData[]> = await UserAPI.getUsers();
```

## Error Handling

### 1. API Error Handling

```typescript
try {
  const response = await YourAPI.getData();
  setData(response);
} catch (error: any) {
  let errorMessage = 'An error occurred';

  // Network errors
  if (error.code === 'NETWORK_ERROR' || error.message?.includes('timeout')) {
    errorMessage = 'Network connection failed. Please check your internet connection.';
  }
  // Authentication errors
  else if (error.response?.status === 401) {
    errorMessage = 'Authentication failed. Please login again.';
    // Optionally redirect to login
  }
  // Server errors
  else if (error.response?.status >= 500) {
    errorMessage = 'Server error. Please try again later.';
  }
  // Client errors
  else if (error.response?.status >= 400) {
    errorMessage = error.response.data?.message || 'Invalid request.';
  }
  // Custom error messages
  else if (error.message) {
    errorMessage = error.message;
  }

  setError(errorMessage);
  Alert.alert('Error', errorMessage);
}
```

### 2. Form Validation

```typescript
// Use react-hook-form for form validation
import { useForm, Controller } from 'react-hook-form';

interface FormData {
  name: string;
  email: string;
  amount: number;
}

const YourFormScreen = () => {
  const { control, handleSubmit, formState: { errors } } = useForm<FormData>();

  const onSubmit = (data: FormData) => {
    // Handle form submission
  };

  return (
    <Controller
      control={control}
      name="email"
      rules={{
        required: 'Email is required',
        pattern: {
          value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
          message: 'Invalid email address'
        }
      }}
      render={({ field: { onChange, onBlur, value } }) => (
        <TextInput
          placeholder="Email"
          onBlur={onBlur}
          onChangeText={onChange}
          value={value}
          error={errors.email?.message}
        />
      )}
    />
  );
};
```

## Performance Guidelines

### 1. List Rendering

```typescript
// Use FlatList for large lists
import { FlatList } from 'react-native';

const renderItem = ({ item }: { item: ItemType }) => (
  <ItemComponent item={item} />
);

<FlatList
  data={items}
  renderItem={renderItem}
  keyExtractor={(item) => item.id}
  onEndReached={loadMore}
  onEndReachedThreshold={0.5}
  refreshControl={
    <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
  }
  showsVerticalScrollIndicator={false}
/>
```

### 2. Image Handling

```typescript
// Use optimized image props
<Image
  source={{ uri: imageUrl }}
  style={styles.image}
  resizeMode="cover"
  fadeDuration={300}
  defaultSource={require('../assets/placeholder.png')}
/>
```

### 3. Memory Management

```typescript
useEffect(() => {
  // Setup code here

  return () => {
    // Cleanup code here
    // Cancel API requests, clear timers, etc.
  };
}, []);
```

## Testing Guidelines

### 1. Component Testing

```typescript
// Basic component test structure
import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import YourComponent from '../YourComponent';

describe('YourComponent', () => {
  it('renders correctly', () => {
    const { getByText } = render(
      <YourComponent title="Test Title" />
    );
    
    expect(getByText('Test Title')).toBeTruthy();
  });

  it('handles press events', () => {
    const mockOnPress = jest.fn();
    const { getByText } = render(
      <YourComponent title="Test" onPress={mockOnPress} />
    );
    
    fireEvent.press(getByText('Test'));
    expect(mockOnPress).toHaveBeenCalled();
  });
});
```

### 2. API Testing

```typescript
// Mock API calls in tests
jest.mock('../../src/api/user.api', () => ({
  UserAPI: {
    getProfile: jest.fn().mockResolvedValue({
      id: '1',
      name: 'Test User',
      email: '<EMAIL>'
    }),
  },
}));
```

## Common Pitfalls & Solutions

### 1. ❌ Common Mistakes

#### Import Errors
```typescript
// ❌ WRONG - Using non-existent icons
import { Ionicons } from '@expo/vector-icons';
<Ionicons name="non-existent-icon" />

// ✅ CORRECT - Check icon names
import { Feather } from '@expo/vector-icons';
<Feather name="heart" />
```

#### Theme Errors
```typescript
// ❌ WRONG - Using non-existent properties
theme.colors.primary.main      // doesn't exist
theme.typography.sizes.md      // doesn't exist

// ✅ CORRECT - Use actual properties
theme.colors.primary[500]      // exists
theme.typography.fontSizes.md  // exists
```

#### Navigation Errors
```typescript
// ❌ WRONG - Not updating navigation types
navigation.navigate('NewScreen'); // TypeScript error if not in types

// ✅ CORRECT - Add to navigation types first
// Then use navigation
```

### 2. ✅ Best Practices

#### State Management
```typescript
// ✅ GOOD - Proper state management
const [loading, setLoading] = useState(false);
const [error, setError] = useState<string | null>(null);
const [data, setData] = useState<DataType[]>([]);

// Handle all states properly
if (loading) return <LoadingComponent />;
if (error) return <ErrorComponent message={error} />;
return <DataComponent data={data} />;
```

#### Component Organization
```typescript
// ✅ GOOD - Organized component structure
const YourScreen = () => {
  // 1. Hooks
  const navigation = useNavigation();
  
  // 2. State
  const [data, setData] = useState([]);
  
  // 3. Effects
  useEffect(() => {
    loadData();
  }, []);
  
  // 4. Functions
  const loadData = async () => {
    // Implementation
  };
  
  // 5. Event handlers
  const handlePress = () => {
    // Implementation
  };
  
  // 6. Render helpers
  const renderItem = () => {
    // Implementation
  };
  
  // 7. Main render
  return (
    // JSX
  );
};
```

### 3. 🚀 Quick Fixes for Common Issues

#### Build Errors
1. **Clear Metro cache**: `npx expo start --clear`
2. **Reinstall node_modules**: `rm -rf node_modules && npm install`
3. **Check TypeScript errors**: `npx tsc --noEmit`

#### Performance Issues
1. **Use FlatList** for long lists
2. **Implement pagination** for large datasets
3. **Optimize images** with proper sizes
4. **Use React.memo** for expensive components

#### Navigation Issues
1. **Check navigation types** are updated
2. **Verify screen names** match exactly
3. **Ensure proper parameter types**

## Checklist for New Features

### Before Starting Development

- [ ] ✅ **Read this document completely**
- [ ] ✅ **Check existing components** for reusability
- [ ] ✅ **Verify required dependencies** are available
- [ ] ✅ **Plan the component/screen structure**
- [ ] ✅ **Define TypeScript interfaces**

### During Development

- [ ] ✅ **Use existing theme values**
- [ ] ✅ **Follow import order**
- [ ] ✅ **Add proper TypeScript types**
- [ ] ✅ **Implement error handling**
- [ ] ✅ **Add loading states**
- [ ] ✅ **Test on both iOS and Android**

### Before Submitting

- [ ] ✅ **Update navigation types** if needed
- [ ] ✅ **Add to navigation stack** if needed
- [ ] ✅ **Test all functionality**
- [ ] ✅ **Check for TypeScript errors**
- [ ] ✅ **Verify theme consistency**
- [ ] ✅ **Test error scenarios**
- [ ] ✅ **Check performance** on slower devices

---


**Remember**: This project values consistency, type safety, and maintainability. Always prefer existing patterns and components over creating new ones unless absolutely necessary. 