# Hướng Dẫn Chuyển Đổi Màn <PERSON> Theme System

## Quy Trình Chung

1. **Xóa các import và khởi tạo cssInterop**:
   ```tsx
   // XÓA
   import { cssInterop } from 'nativewind';
   const StyledView = cssInterop(View, { className: 'style' });
   const StyledText = cssInterop(Text, { className: 'style' });
   // ... các StyledComponent khác
   ```

2. **Thêm import theme**:
   ```tsx
   // THÊM
   import { StyleSheet } from 'react-native';
   import theme from '../theme';
   ```

3. **Thay thế các Styled components bằng React Native component gốc**:
   ```tsx
   // TỪ
   <StyledView className="flex-1 p-4 bg-white">
     <StyledText className="text-xl font-bold text-gray-800">Title</StyledText>
   </StyledView>
   
   // SANG
   <View style={styles.container}>
     <Text style={styles.title}>Title</Text>
   </View>
   ```

4. **Thêm StyleSheet với theme**:
   ```tsx
   const styles = StyleSheet.create({
     container: {
       flex: 1,
       padding: theme.spacing.md,
       backgroundColor: theme.colors.background.primary,
     },
     title: {
       fontSize: theme.typography.fontSizes.xl,
       fontWeight: '700',
       color: theme.colors.text.primary,
     },
     // ... các style khác
   });
   ```

## Bảng Chuyển Đổi Các Thuộc Tính Phổ Biến

### Màu Sắc

| Tailwind Class   | Theme Style                            |
|------------------|----------------------------------------|
| `bg-white`       | `backgroundColor: theme.colors.background.primary` |
| `bg-gray-100`    | `backgroundColor: theme.colors.gray[100]` |
| `text-gray-800`  | `color: theme.colors.text.primary` |
| `text-gray-500`  | `color: theme.colors.text.secondary` |
| `text-primary-600` | `color: theme.colors.primary[600]` |
| `border-gray-200` | `borderColor: theme.colors.gray[200]` |

### Typography

| Tailwind Class   | Theme Style                            |
|------------------|----------------------------------------|
| `text-xs`        | `fontSize: theme.typography.fontSizes.xs` |
| `text-sm`        | `fontSize: theme.typography.fontSizes.sm` |
| `text-base`      | `fontSize: theme.typography.fontSizes.md` |
| `text-lg`        | `fontSize: theme.typography.fontSizes.lg` |
| `text-xl`        | `fontSize: theme.typography.fontSizes.xl` |
| `text-2xl`       | `fontSize: theme.typography.fontSizes['2xl']` |
| `font-normal`    | `fontWeight: '400'` |
| `font-medium`    | `fontWeight: '500'` |
| `font-semibold`  | `fontWeight: '600'` |
| `font-bold`      | `fontWeight: '700'` |

### Spacing

| Tailwind Class   | Theme Style                            |
|------------------|----------------------------------------|
| `p-1`            | `padding: theme.spacing.xs` |
| `p-2`            | `padding: theme.spacing.sm` |
| `p-4`            | `padding: theme.spacing.md` |
| `p-6`            | `padding: theme.spacing.lg` |
| `p-8`            | `padding: theme.spacing.xl` |
| `m-1`            | `margin: theme.spacing.xs` |
| `m-2`            | `margin: theme.spacing.sm` |
| `m-4`            | `margin: theme.spacing.md` |
| `m-6`            | `margin: theme.spacing.lg` |
| `m-8`            | `margin: theme.spacing.xl` |
| `mt-1`           | `marginTop: theme.spacing.xs` |
| `mb-2`           | `marginBottom: theme.spacing.sm` |
| `py-2 px-4`      | `paddingVertical: theme.spacing.sm, paddingHorizontal: theme.spacing.md` |

### Radius

| Tailwind Class   | Theme Style                            |
|------------------|----------------------------------------|
| `rounded-sm`     | `borderRadius: theme.borderRadius.sm` |
| `rounded`        | `borderRadius: theme.borderRadius.md` |
| `rounded-lg`     | `borderRadius: theme.borderRadius.lg` |
| `rounded-xl`     | `borderRadius: theme.borderRadius.xl` |
| `rounded-full`   | `borderRadius: theme.borderRadius.full` |

### Layout

| Tailwind Class   | Theme Style                            |
|------------------|----------------------------------------|
| `flex-1`         | `flex: 1` |
| `flex-row`       | `flexDirection: 'row'` |
| `flex-col`       | `flexDirection: 'column'` |
| `items-center`   | `alignItems: 'center'` |
| `justify-center` | `justifyContent: 'center'` |
| `justify-between`| `justifyContent: 'space-between'` |
| `space-x-2`      | Sử dụng `marginRight` cho tất cả trừ phần tử cuối |
| `space-y-2`      | Sử dụng `marginBottom` cho tất cả trừ phần tử cuối |

## Sử Dụng Components Tích Hợp Theme

### 1. Screen Component

```tsx
// TỪ
<SafeAreaView className="flex-1 bg-white">
  {/* content */}
</SafeAreaView>

// SANG
<Screen>
  {/* content */}
</Screen>
```

### 2. Button Component

```tsx
// TỪ
<TouchableOpacity className="bg-primary-500 px-4 py-2 rounded-md">
  <Text className="text-white font-medium">Submit</Text>
</TouchableOpacity>

// SANG
<Button 
  title="Submit"
  onPress={handleSubmit}
  variant="primary"
/>
```

### 3. TextInput Component

```tsx
// TỪ
<View>
  <Text className="text-sm font-medium mb-1">Email</Text>
  <TextInput 
    className="border border-gray-300 rounded-md px-3 py-2"
    placeholder="Enter your email" 
    value={email}
    onChangeText={setEmail}
  />
</View>

// SANG
<TextInput 
  label="Email"
  placeholder="Enter your email"
  value={email}
  onChangeText={setEmail}
/>
```

### 4. Card Component

```tsx
// TỪ
<View className="bg-white shadow rounded-lg p-4">
  {/* card content */}
</View>

// SANG
<Card>
  {/* card content */}
</Card>
```

## Mẫu Chuyển Đổi Hoàn Chỉnh

### Màn Hình Trước Khi Chuyển Đổi

```tsx
import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { cssInterop } from 'nativewind';

const StyledView = cssInterop(View, { className: 'style' });
const StyledText = cssInterop(Text, { className: 'style' });
const StyledTouchableOpacity = cssInterop(TouchableOpacity, { className: 'style' });

const MyScreen = () => {
  return (
    <StyledView className="flex-1 bg-white p-4">
      <StyledText className="text-2xl font-bold text-gray-800 mb-4">My Title</StyledText>
      
      <StyledView className="bg-gray-100 p-4 rounded-lg mb-6">
        <StyledText className="text-gray-800">Some content here</StyledText>
      </StyledView>
      
      <StyledTouchableOpacity className="bg-primary-500 py-3 px-6 rounded-md">
        <StyledText className="text-white font-medium text-center">Submit</StyledText>
      </StyledTouchableOpacity>
    </StyledView>
  );
};

export default MyScreen;
```

### Màn Hình Sau Khi Chuyển Đổi

```tsx
import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import theme from '../theme';
import Screen from '../components/common/Screen';
import Button from '../components/ui/Button';

const MyScreen = () => {
  return (
    <Screen>
      <View style={styles.container}>
        <Text style={styles.title}>My Title</Text>
        
        <View style={styles.contentBox}>
          <Text style={styles.contentText}>Some content here</Text>
        </View>
        
        <Button
          title="Submit"
          onPress={() => {}}
          variant="primary"
        />
      </View>
    </Screen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: theme.spacing.md,
  },
  title: {
    fontSize: theme.typography.fontSizes['2xl'],
    fontWeight: '700',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.md,
  },
  contentBox: {
    backgroundColor: theme.colors.gray[100],
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
    marginBottom: theme.spacing.xl,
  },
  contentText: {
    color: theme.colors.text.primary,
  },
});

export default MyScreen;
```

## Lời Khuyên Bổ Sung

1. **Từng Bước**: Chuyển đổi từng màn hình một, bắt đầu với các màn hình đơn giản nhất
2. **Sử dụng Components**: Ưu tiên sử dụng các UI components có sẵn khi có thể
3. **Kiểm Tra Tương Thích**: Kiểm tra lại giao diện sau khi chuyển đổi
4. **Refactor**: Tạo các shared styles cho những style dùng chung
5. **Mở Rộng Theme**: Nếu cần thêm các style mới, hãy cập nhật theme thay vì tạo style cục bộ 