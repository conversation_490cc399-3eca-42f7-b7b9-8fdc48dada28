import axios from '../lib/axios';
import { API_CONFIG } from './config';

const STATISTICS_ENDPOINT = `${API_CONFIG.BASE_URL}/statistics`;

// Types for API requests and responses
export interface IncomeExpenseChartParams {
  period: 'week' | 'month' | 'year';
  startDate?: string;
  endDate?: string;
}

export interface IncomeExpenseChartData {
  labels: string[];
  incomeData: number[];
  expenseData: number[];
  totalIncome: number;
  totalExpense: number;
  netAmount: number;
}

export interface CategoryAnalysisParams {
  period: 'month' | 'year';
  type?: 'income' | 'expense';
  month?: number;
  year?: number;
}

export interface CategoryData {
  categoryId: string;
  categoryName: string;
  amount: number;
  percentage: number;
  color: string;
}

export interface CategoryAnalysisData {
  categories: CategoryData[];
  totalAmount: number;
  period: string;
}

export interface FinancialTrendsParams {
  period: 'month' | 'year';
  limit?: number;
}

export interface TrendData {
  period: string;
  income: number;
  expense: number;
  balance: number;
  growth: number;
  movingAverage?: number;
}

export interface FinancialTrendsData {
  trends: TrendData[];
  overallGrowth: number;
  averageBalance: number;
}

export interface ComparisonParams {
  periods: string[];
  periodType: 'month' | 'year';
}

export interface ComparisonPeriod {
  period: string;
  income: number;
  expense: number;
  balance: number;
  transactionCount: number;
}

export interface ComparisonData {
  periods: ComparisonPeriod[];
  bestPeriod: string;
  worstPeriod: string;
}

export interface ApiResponse<T> {
  status: string;
  statusCode: number;
  message: string;
  data: T;
  meta: any;
  timestamp: string;
}

export const StatisticsAPI = {
  // 1. Income Expense Chart
  getIncomeExpenseChart: async (params: IncomeExpenseChartParams): Promise<IncomeExpenseChartData> => {
    const response = await axios.get<ApiResponse<IncomeExpenseChartData>>(
      `${STATISTICS_ENDPOINT}/income-expense-chart`,
      { params }
    );
    return response.data.data;
  },

  // 2. Category Analysis
  getCategoryAnalysis: async (params: CategoryAnalysisParams): Promise<CategoryAnalysisData> => {
    const response = await axios.get<ApiResponse<CategoryAnalysisData>>(
      `${STATISTICS_ENDPOINT}/category-analysis`,
      { params }
    );
    return response.data.data;
  },

  // 3. Financial Trends
  getFinancialTrends: async (params: FinancialTrendsParams): Promise<FinancialTrendsData> => {
    const response = await axios.get<ApiResponse<FinancialTrendsData>>(
      `${STATISTICS_ENDPOINT}/financial-trends`,
      { params }
    );
    return response.data.data;
  },

  // 4. Comparison
  getComparison: async (params: ComparisonParams): Promise<ComparisonData> => {
    const response = await axios.get<ApiResponse<ComparisonData>>(
      `${STATISTICS_ENDPOINT}/comparison`,
      { params }
    );
    return response.data.data;
  },
};

