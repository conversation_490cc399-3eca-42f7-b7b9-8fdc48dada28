import axios from 'axios';
import { API_CONFIG } from './config';
import { StorageService } from '../../app/services/storage.service';

export interface UploadResponse {
  url: string;
  thumbnail: string;
}

export interface UploadSingleResponse {
  data: UploadResponse;
}

export interface UploadMultipleResponse {
  data: {
    files: UploadResponse[];
  };
}

const UPLOAD_ENDPOINT = '/upload';

// Create a separate axios instance for uploads with longer timeout
const createUploadInstance = async () => {
  const uploadInstance = axios.create({
    baseURL: API_CONFIG.BASE_URL,
    timeout: 120000, // 2 minutes for uploads
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });

  // Add auth token
  const token = await StorageService.getAccessToken();
  if (token) {
    uploadInstance.defaults.headers['Authorization'] = `Bearer ${token}`;
  }

  return uploadInstance;
};

export const UploadAPI = {
  // Upload single file
  uploadSingle: async (file: {
    uri: string;
    type: string;
    name: string;
  }): Promise<UploadResponse> => {
    console.log('UploadAPI.uploadSingle called with:', file);

    try {
      const uploadInstance = await createUploadInstance();

      const formData = new FormData();
      formData.append('file', {
        uri: file.uri,
        type: file.type,
        name: file.name,
      } as any);

      console.log('Starting upload to:', `${API_CONFIG.BASE_URL}${UPLOAD_ENDPOINT}/single`);

      const response = await uploadInstance.post(`${UPLOAD_ENDPOINT}/single`, formData);

      console.log('Upload single response:', response.data);
      return response.data.data;
    } catch (error) {
      console.error('Upload error details:', error);
      if ((error as { code?: string }).code === 'ECONNABORTED') {
        throw new Error('Upload timeout - please try with a smaller image or check your connection');
      }
      throw error;
    }
  },

  // Upload multiple files
  uploadMultiple: async (files: Array<{
    uri: string;
    type: string;
    name: string;
  }>): Promise<UploadResponse[]> => {
    console.log('UploadAPI.uploadMultiple called with:', files);
    
    const formData = new FormData();
    files.forEach((file, index) => {
      formData.append('files', {
        uri: file.uri,
        type: file.type,
        name: file.name,
      } as any);
    });

    const response = await axios.post(`${UPLOAD_ENDPOINT}/multi`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    
    console.log('Upload multiple response:', response.data);
    return response.data.data.files;
  },
};
