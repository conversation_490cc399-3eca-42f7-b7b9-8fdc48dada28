import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  Alert,
  TouchableOpacity,
  FlatList,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation/types';
import theme from '../theme';
import Screen from '../components/common/Screen';
import Button from '../components/ui/Button';
import Card from '../components/ui/Card';
import { BudgetAPI, BudgetReportDto, BudgetCategoryReportDto, ExportBudgetReportDto } from '../../src/api/budget.api';
import { downloadFile, getMimeType } from '../utils/fileDownload';

type BudgetReportScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;

const BudgetReportScreen = () => {
  const navigation = useNavigation<BudgetReportScreenNavigationProp>();
  const route = useRoute();
  const { month, year } = route.params as { month: number; year: number };

  // State management
  const [report, setReport] = useState<BudgetReportDto | null>(null);
  const [loading, setLoading] = useState(true);
  const [exporting, setExporting] = useState(false);

  // Load budget report
  const loadReport = async () => {
    try {
      setLoading(true);
      const reportData = await BudgetAPI.getReport(month, year);
      setReport(reportData);
    } catch (error: any) {
      let errorMessage = 'Failed to load budget report. Please try again.';

      if (error.response?.status === 401) {
        errorMessage = 'Authentication failed. Please login again.';
      } else if (error.response?.status >= 500) {
        errorMessage = 'Server error. Please try again later.';
      } else if (error.message) {
        errorMessage = error.message;
      }

      Alert.alert('Error', errorMessage);
      navigation.goBack();
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadReport();
  }, [month, year]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const getMonthYearText = () => {
    const monthNames = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return `${monthNames[month - 1]} ${year}`;
  };

  const calculateProgress = (spent: number, budget: number) => {
    if (budget === 0) return 0;
    return Math.min((spent / budget) * 100, 100);
  };

  const handleExport = async (format: 'csv' | 'pdf') => {
    try {
      setExporting(true);

      const exportData: ExportBudgetReportDto = {
        month,
        year,
        format,
      };

      console.log('🔍 Exporting budget report:', exportData);

      const { blob, filename } = await BudgetAPI.exportBudgetReport(exportData);

      console.log('📄 Export Response:');
      console.log('- Filename:', filename);
      console.log('- Blob type:', blob.type);
      console.log('- Blob size:', blob.size);
      console.log('- MIME type:', getMimeType(format));

      // Download file to device storage
      console.log('🚀 Starting download process...');
      try {
        await downloadFile({
          blob,
          filename,
          mimeType: getMimeType(format),
        });
        console.log('✅ Download completed successfully');
      } catch (downloadError) {
        console.error('💥 Download failed:', downloadError);
        throw downloadError;
      }

    } catch (error: any) {
      console.error('Export error:', error);
      let errorMessage = 'Failed to export report. Please try again.';

      if (error.response?.status === 401) {
        errorMessage = 'Authentication failed. Please login again.';
      } else if (error.response?.status >= 500) {
        errorMessage = 'Server error. Please try again later.';
      } else if (error.message) {
        errorMessage = error.message;
      }

      Alert.alert('Export Failed', errorMessage);
    } finally {
      setExporting(false);
    }
  };

  const renderCategoryItem = ({ item }: { item: BudgetCategoryReportDto }) => {
    const progress = calculateProgress(item.spentAmount, item.budgetAmount);
    const isOverBudget = item.spentAmount > item.budgetAmount;

    return (
      <Card style={styles.categoryCard}>
        <View style={styles.categoryHeader}>
          <View style={styles.categoryIconContainer}>
            <Text style={styles.categoryIcon}>💰</Text>
          </View>
          <View style={styles.categoryInfo}>
            <Text style={styles.categoryName}>{item.categoryName}</Text>
            <Text style={styles.transactionCount}>
              {item.transactions.length} transaction{item.transactions.length !== 1 ? 's' : ''}
            </Text>
          </View>
          {isOverBudget && (
            <View style={styles.overBudgetBadge}>
              <Text style={styles.overBudgetText}>Over Budget</Text>
            </View>
          )}
        </View>

        <View style={styles.progressContainer}>
          <View
            style={[
              styles.progressBar,
              { width: `${progress}%` },
              isOverBudget && styles.overBudgetBar,
            ]}
          />
        </View>

        <View style={styles.categoryStats}>
          <View style={styles.statItem}>
            <Text style={styles.statLabel}>Budget</Text>
            <Text style={styles.statValue}>{formatCurrency(item.budgetAmount)}</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statLabel}>Spent</Text>
            <Text style={[styles.statValue, isOverBudget && styles.overBudgetText]}>
              {formatCurrency(item.spentAmount)}
            </Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statLabel}>Remaining</Text>
            <Text style={[styles.statValue, isOverBudget ? styles.overBudgetText : styles.remainingText]}>
              {formatCurrency(Math.abs(item.remainingAmount))}
              {isOverBudget ? ' over' : ''}
            </Text>
          </View>
        </View>
      </Card>
    );
  };

  if (loading) {
    return (
      <Screen>
        <View style={[styles.container, { justifyContent: 'center', alignItems: 'center' }]}>
          <ActivityIndicator size="large" color={theme.colors.primary[500]} />
          <Text style={{ marginTop: theme.spacing.md, color: theme.colors.text.secondary }}>
            Loading budget report...
          </Text>
        </View>
      </Screen>
    );
  }

  if (!report) {
    return (
      <Screen>
        <View style={[styles.container, { justifyContent: 'center', alignItems: 'center' }]}>
          <Text style={styles.errorText}>Report not found</Text>
          <Button
            title="Go Back"
            onPress={() => navigation.goBack()}
            variant="primary"
            style={{ marginTop: theme.spacing.md }}
          />
        </View>
      </Screen>
    );
  }

  const overallProgress = report.totalBudget > 0 ? (report.totalSpent / report.totalBudget) * 100 : 0;
  const isOverallOverBudget = report.totalSpent > report.totalBudget;

  return (
    <Screen>
      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.container}>
          <Text style={styles.title}>Budget Report</Text>
          <Text style={styles.subtitle}>{getMonthYearText()}</Text>

          {/* Overall Summary */}
          <Card style={styles.summaryCard}>
            <Text style={styles.summaryTitle}>Overall Summary</Text>

            <View style={styles.progressContainer}>
              <View
                style={[
                  styles.progressBar,
                  { width: `${Math.min(overallProgress, 100)}%` },
                  isOverallOverBudget && styles.overBudgetBar,
                ]}
              />
            </View>

            <View style={styles.summaryStats}>
              <View style={styles.summaryStatItem}>
                <Text style={styles.summaryStatLabel}>Total Budget</Text>
                <Text style={styles.summaryStatValue}>{formatCurrency(report.totalBudget)}</Text>
              </View>
              <View style={styles.summaryStatItem}>
                <Text style={styles.summaryStatLabel}>Total Spent</Text>
                <Text style={[styles.summaryStatValue, isOverallOverBudget && styles.overBudgetText]}>
                  {formatCurrency(report.totalSpent)}
                </Text>
              </View>
              <View style={styles.summaryStatItem}>
                <Text style={styles.summaryStatLabel}>Remaining</Text>
                <Text style={[styles.summaryStatValue, isOverallOverBudget ? styles.overBudgetText : styles.remainingText]}>
                  {formatCurrency(Math.abs(report.totalRemaining))}
                  {isOverallOverBudget ? ' over' : ''}
                </Text>
              </View>
            </View>
          </Card>

          {/* Export Buttons */}
          <View style={styles.exportSection}>
            <Text style={styles.sectionTitle}>Export Report</Text>
            <View style={styles.exportButtons}>
              <Button
                title={exporting ? "Exporting..." : "Export CSV"}
                onPress={() => handleExport('csv')}
                variant="secondary"
                disabled={exporting}
                style={[styles.exportButton, { marginRight: theme.spacing.sm }]}
              />
              <Button
                title={exporting ? "Exporting..." : "Export PDF"}
                onPress={() => handleExport('pdf')}
                variant="primary"
                disabled={exporting}
                style={[styles.exportButton, { marginLeft: theme.spacing.sm }]}
              />
            </View>
          </View>

          {/* Categories Breakdown */}
          <View style={styles.categoriesSection}>
            <Text style={styles.sectionTitle}>Categories Breakdown</Text>
            {report.categories.length > 0 ? (
              <FlatList
                data={report.categories}
                renderItem={renderCategoryItem}
                keyExtractor={(item) => item.categoryId}
                scrollEnabled={false}
              />
            ) : (
              <Text style={styles.emptyText}>No budget categories for this period</Text>
            )}
          </View>
        </View>
      </ScrollView>
    </Screen>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: theme.spacing.md,
  },
  title: {
    fontSize: theme.typography.fontSizes['2xl'],
    fontWeight: '700',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.xs,
  },
  subtitle: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.secondary,
    marginBottom: theme.spacing.lg,
  },
  summaryCard: {
    marginBottom: theme.spacing.lg,
  },
  summaryTitle: {
    fontSize: theme.typography.fontSizes.lg,
    fontWeight: '600',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.md,
  },
  progressContainer: {
    height: 12,
    backgroundColor: theme.colors.gray[100],
    borderRadius: theme.borderRadius.full,
    marginBottom: theme.spacing.md,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    backgroundColor: theme.colors.primary[500],
  },
  overBudgetBar: {
    backgroundColor: theme.colors.error,
  },
  summaryStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  summaryStatItem: {
    alignItems: 'center',
  },
  summaryStatLabel: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
    marginBottom: theme.spacing.xs,
  },
  summaryStatValue: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '600',
    color: theme.colors.text.primary,
  },
  overBudgetText: {
    color: theme.colors.error,
  },
  remainingText: {
    color: theme.colors.success,
  },
  exportSection: {
    marginBottom: theme.spacing.lg,
  },
  sectionTitle: {
    fontSize: theme.typography.fontSizes.lg,
    fontWeight: '600',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.md,
  },
  exportButtons: {
    flexDirection: 'row',
  },
  exportButton: {
    flex: 1,
    marginVertical: 0,
  },
  categoriesSection: {
    marginBottom: theme.spacing.lg,
  },
  categoryCard: {
    marginBottom: theme.spacing.md,
  },
  categoryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  categoryIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.primary[100],
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: theme.spacing.sm,
  },
  categoryIcon: {
    fontSize: theme.typography.fontSizes.lg,
  },
  categoryInfo: {
    flex: 1,
  },
  categoryName: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '600',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.xs / 2,
  },
  transactionCount: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
  },
  overBudgetBadge: {
    backgroundColor: theme.colors.error,
    paddingVertical: theme.spacing.xs / 2,
    paddingHorizontal: theme.spacing.sm,
    borderRadius: theme.borderRadius.full,
  },
  categoryStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statItem: {
    alignItems: 'center',
  },
  statLabel: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
    marginBottom: theme.spacing.xs / 2,
  },
  statValue: {
    fontSize: theme.typography.fontSizes.sm,
    fontWeight: '600',
    color: theme.colors.text.primary,
  },
  emptyText: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.secondary,
    textAlign: 'center',
    paddingVertical: theme.spacing.xl,
  },
  errorText: {
    fontSize: theme.typography.fontSizes.lg,
    color: theme.colors.error,
    textAlign: 'center',
  },
});

export default BudgetReportScreen;
