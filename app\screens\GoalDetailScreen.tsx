import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation/types';
import theme from '../theme';
import Screen from '../components/common/Screen';
import Button from '../components/ui/Button';
import Card from '../components/ui/Card';
import { GoalsAPI, IGoal, GoalStatus } from '../../src/api/goals.api';
import { useFocusEffect } from '@react-navigation/native';
import STRINGS from '../constants/strings';

const { width } = Dimensions.get('window');

type GoalDetailScreenRouteProp = RouteProp<RootStackParamList, 'GoalDetail'>;
type GoalDetailScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'GoalDetail'>;

const GoalDetailScreen = () => {
  const navigation = useNavigation<GoalDetailScreenNavigationProp>();
  const route = useRoute<GoalDetailScreenRouteProp>();
  const [goal, setGoal] = useState<IGoal | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadGoalDetails();
  }, [route.params.id]);

  const loadGoalDetails = async () => {
    try {
      setLoading(true);
      setError(null);
      const goalData = await GoalsAPI.getGoalById(route.params.id);
      setGoal(goalData);
    } catch (err: any) {
      // Determine error message based on error type
      let errorMessage = 'Failed to load goal details. Please try again.';

      if (err.code === 'NETWORK_ERROR' || err.message?.includes('timeout')) {
        errorMessage = 'Network connection failed. Please check your internet connection and try again.';
      } else if (err.response?.status === 401) {
        errorMessage = 'Authentication failed. Please login again.';
      } else if (err.response?.status === 404) {
        errorMessage = 'Goal not found. It may have been deleted.';
      } else if (err.response?.status >= 500) {
        errorMessage = 'Server error. Please try again later.';
      } else if (err.message) {
        errorMessage = err.message;
      }

      setError(errorMessage);

      // Show alert for critical errors
      Alert.alert(
        'Error Loading Goal',
        errorMessage,
        [
          {
            text: 'Retry',
            onPress: () => loadGoalDetails()
          },
          {
            text: 'Go Back',
            style: 'cancel',
            onPress: () => navigation.goBack()
          }
        ]
      );
    } finally {
      setLoading(false);
    }
  };

  useFocusEffect(
    React.useCallback(() => {
      loadGoalDetails();
    }, [])
  );

  const calculateProgress = (current: number, target: number) => {
    return Math.min((current / target) * 100, 100);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(amount);
  };

  const getGoalIcon = (name: string) => {
    const iconMap: { [key: string]: string } = {
      'Mua nhà': '🏠',
      'Mua xe': '🚗',
      'Du lịch': '✈️',
      'Học tập': '🎓',
      'Đám cưới': '💍',
      'Tiết kiệm': '💰',
      'Quỹ khẩn cấp': '🚨',
    };
    return iconMap[name] || '✨';
  };

  const getStatusColor = (status: GoalStatus) => {
    const statusColors: { [key in GoalStatus]: string } = {
      [GoalStatus.ACTIVE]: theme.colors.primary[500],
      [GoalStatus.COMPLETED]: theme.colors.success[500],
      [GoalStatus.CANCELLED]: theme.colors.error[500],
      [GoalStatus.PAUSED]: theme.colors.warning[500] || '#f59e0b',
      [GoalStatus.PENDING]: theme.colors.gray[500],
    };
    return statusColors[status] || theme.colors.gray[500];
  };

  const getStatusText = (status: GoalStatus) => {
    const statusTexts: { [key in GoalStatus]: string } = {
      [GoalStatus.ACTIVE]: STRINGS.GOALS.ACTIVE,
      [GoalStatus.COMPLETED]: STRINGS.GOALS.COMPLETED,
      [GoalStatus.CANCELLED]: STRINGS.GOALS.CANCELLED,
      [GoalStatus.PAUSED]: STRINGS.GOALS.PAUSED,
      [GoalStatus.PENDING]: STRINGS.GOALS.PENDING,
    };
    return statusTexts[status] || STRINGS.GOALS.UNKNOWN;
  };

  if (loading) {
    return (
      <Screen>
        <View style={[styles.container, styles.centerContent]}>
          <ActivityIndicator size="large" color={theme.colors.primary[500]} />
        </View>
      </Screen>
    );
  }

  if (error || !goal) {
    return (
      <Screen>
        <View style={[styles.container, styles.centerContent]}>
          <Text style={styles.errorText}>{error || 'Goal not found'}</Text>
          <Button
            title="Thử lại"
            onPress={loadGoalDetails}
            variant="secondary"
            style={styles.retryButton}
          />
        </View>
      </Screen>
    );
  }

  const progress = calculateProgress(goal.current_amount, goal.target_amount);

  return (
    <Screen>
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <View style={styles.goalHeader}>
          <View style={styles.iconContainer}>
            <Text style={styles.iconText}>{getGoalIcon(goal.name)}</Text>
          </View>
          <Text style={styles.titleText}>{goal.name}</Text>
          {goal.description && (
            <Text style={styles.descriptionText}>{goal.description}</Text>
          )}
        </View>

        <Card style={styles.progressCard}>
          <View style={styles.progressTextContainer}>
            <Text style={styles.progressLabel}>Tiến độ</Text>
            <Text style={styles.progressPercentage}>{progress.toFixed(1)}%</Text>
          </View>

          <View style={styles.progressBarContainer}>
            <View style={[styles.progressBar, { width: `${progress}%` }]} />
          </View>

          <View style={styles.amountContainer}>
            <View>
              <Text style={styles.amountLabel}>Số tiền hiện tại</Text>
              <Text style={styles.currentAmount}>
                {formatCurrency(goal.current_amount)}
              </Text>
            </View>
            <View style={styles.targetAmountContainer}>
              <Text style={styles.amountLabel}>Mục tiêu</Text>
              <Text style={styles.targetAmount}>
                {formatCurrency(goal.target_amount)}
              </Text>
            </View>
          </View>
        </Card>

        <Card style={styles.detailsCard}>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Trạng thái</Text>
            <View style={[styles.statusBadge, { backgroundColor: getStatusColor(goal.status) }]}>
              <Text style={styles.statusText}>{getStatusText(goal.status)}</Text>
            </View>
          </View>

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Ngày đến hạn</Text>
            <Text style={styles.detailValue}>
              {new Date(goal.target_date).toLocaleDateString('vi-VN')}
            </Text>
          </View>

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Ngày tạo</Text>
            <Text style={styles.detailValue}>
              {new Date(goal.created_at).toLocaleDateString('vi-VN')}
            </Text>
          </View>
        </Card>

        <View style={styles.buttonContainer}>
          <Button
            title="Cập nhật mục tiêu"
            onPress={() => navigation.navigate('UpdateGoal', { id: goal?.id })}
            variant="primary"
            style={styles.updateButton}
          />
        </View>
      </ScrollView>
    </Screen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollContainer: {
    flexGrow: 1,
    paddingBottom: theme.spacing.xl,
  },
  goalHeader: {
    backgroundColor: theme.colors.primary[100],
    padding: theme.spacing.xl,
    alignItems: 'center',
  },
  iconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: theme.colors.background.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
    shadowColor: theme.colors.shadow.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  iconText: {
    fontSize: 32,
  },
  titleText: {
    fontSize: theme.typography.fontSizes['2xl'],
    fontWeight: '700',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.sm,
    textAlign: 'center',
  },
  descriptionText: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
    textAlign: 'center',
    paddingHorizontal: theme.spacing.md,
  },
  progressCard: {
    margin: theme.spacing.md,
    padding: theme.spacing.lg,
  },
  progressTextContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  progressLabel: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '500',
    color: theme.colors.text.primary,
  },
  progressPercentage: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '600',
    color: theme.colors.primary[500],
  },
  progressBarContainer: {
    height: 8,
    backgroundColor: theme.colors.gray[100],
    borderRadius: theme.borderRadius.sm,
    marginBottom: theme.spacing.lg,
    overflow: 'hidden',
  },
  progressBar: {
    height: 8,
    backgroundColor: theme.colors.primary[500],
    borderRadius: theme.borderRadius.sm,
  },
  amountContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  amountLabel: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
    marginBottom: theme.spacing.xs,
  },
  currentAmount: {
    fontSize: theme.typography.fontSizes.xl,
    fontWeight: '700',
    color: theme.colors.primary[500],
  },
  targetAmountContainer: {
    alignItems: 'flex-end',
  },
  targetAmount: {
    fontSize: theme.typography.fontSizes.xl,
    fontWeight: '700',
    color: theme.colors.text.primary,
  },
  detailsCard: {
    margin: theme.spacing.md,
    padding: theme.spacing.lg,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: theme.spacing.sm,
  },
  detailLabel: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.secondary,
  },
  detailValue: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.primary,
    fontWeight: '500',
  },
  statusBadge: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.full,
  },
  statusText: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.background.primary,
    fontWeight: '500',
  },
  buttonContainer: {
    padding: theme.spacing.md,
  },
  updateButton: {
    marginBottom: 0,
  },
  errorText: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.error[500],
    textAlign: 'center',
    marginBottom: theme.spacing.md,
  },
  retryButton: {
    width: 120,
  },
});

export default GoalDetailScreen;