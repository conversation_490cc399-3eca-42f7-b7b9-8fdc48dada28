import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import theme from '../../theme';
import { CategoryAPI, ICategory, CategoryType, CategoryStatus } from '../../../src/api/category.api';

interface CategoryCardProps {
    category: ICategory;
    onPress: () => void;
    onToggleStatus?: () => void;
}

// Icon mapping cho các danh mục
const getCategoryIcon = (categoryName: string, type: CategoryType): string => {
    // Kiểm tra categoryName trước khi gọi toLowerCase
    if (!categoryName) return type === CategoryType.INCOME ? '💰' : '💳';

    const name = categoryName.toLowerCase();

    if (type === CategoryType.INCOME) {
        if (name.includes('lương') || name.includes('salary')) return '💰';
        if (name.includes('thưởng') || name.includes('bonus')) return '🎁';
        if (name.includes('đầu tư') || name.includes('investment')) return '📈';
        if (name.includes('bán') || name.includes('sell')) return '💵';
        return '💰'; // Default income icon
    } else {
        // Expense categories
        if (name.includes('ăn') || name.includes('food') || name.includes('đồ ăn')) return '🍽️';
        if (name.includes('di chuyển') || name.includes('transport') || name.includes('xe')) return '🚗';
        if (name.includes('mua sắm') || name.includes('shopping') || name.includes('quần áo')) return '🛍️';
        if (name.includes('giải trí') || name.includes('entertainment') || name.includes('phim')) return '🎬';
        if (name.includes('sức khỏe') || name.includes('health') || name.includes('bệnh viện')) return '🏥';
        if (name.includes('giáo dục') || name.includes('education') || name.includes('học')) return '📚';
        if (name.includes('nhà ở') || name.includes('house') || name.includes('thuê nhà')) return '🏠';
        if (name.includes('điện') || name.includes('electric') || name.includes('tiền điện')) return '⚡';
        if (name.includes('nước') || name.includes('water') || name.includes('tiền nước')) return '💧';
        if (name.includes('internet') || name.includes('wifi') || name.includes('mạng')) return '📶';
        if (name.includes('xăng') || name.includes('fuel') || name.includes('gas')) return '⛽';
        if (name.includes('cà phê') || name.includes('coffee') || name.includes('trà')) return '☕';
        return '💳'; // Default expense icon
    }
};

const CategoryCard: React.FC<CategoryCardProps> = ({
    category,
    onPress,
    onToggleStatus
}) => {
    const isActive = category.status === CategoryStatus.ACTIVE; // 'active'
    const isIncome = category.type === CategoryType.INCOME; // 'income'
    const categoryIcon = getCategoryIcon(category.name, category.type);

    return (
        <TouchableOpacity
            style={[
                styles.container,
                !isActive && styles.inactiveContainer
            ]}
            onPress={onPress}
            activeOpacity={0.7}
        >
            {/* Icon Section - Luôn hiển thị rõ ràng */}
            <View style={[
                styles.iconContainer,
                isIncome ? styles.incomeIconContainer : styles.expenseIconContainer,
                !isActive && styles.inactiveIconContainer
            ]}>
                <Text style={[
                    styles.categoryIcon,
                    !isActive && styles.inactiveIcon
                ]}>
                    {categoryIcon}
                </Text>
            </View>

            <View style={styles.content}>
                {/* Header */}
                <View style={styles.header}>
                    <View style={styles.titleContainer}>
                        {/* Tên danh mục - Luôn hiển thị rõ ràng */}
                        <Text style={[
                            styles.name,
                            !isActive && styles.inactiveName // Chỉ làm mờ một chút
                        ]}>
                            {category.name}
                        </Text>
                        <View style={[
                            styles.typeBadge,
                            isIncome ? styles.incomeBadge : styles.expenseBadge,
                            !isActive && styles.inactiveTypeBadge
                        ]}>
                            <Text style={[
                                styles.typeText,
                                isIncome ? styles.incomeTypeText : styles.expenseTypeText,
                                !isActive && styles.inactiveTypeText
                            ]}>
                                {isIncome ? 'Thu nhập' : 'Chi tiêu'}
                            </Text>
                        </View>
                    </View>

                    {/* Status Toggle */}
                    <TouchableOpacity
                        style={[
                            styles.statusToggle,
                            isActive ? styles.activeToggle : styles.inactiveToggle
                        ]}
                        onPress={onToggleStatus}
                    >
                        <View style={[
                            styles.toggleThumb,
                            { transform: [{ translateX: isActive ? 20 : 2 }] }
                        ]} />
                    </TouchableOpacity>
                </View>

                {/* Status */}
                <View style={styles.statusContainer}>
                    <View style={[
                        styles.statusDot,
                        isActive ? styles.activeDot : styles.inactiveDot
                    ]} />
                    <Text style={[
                        styles.statusText,
                        !isActive && styles.inactiveStatusText
                    ]}>
                        {isActive ? 'Đang hoạt động' : 'Tạm dừng'}
                    </Text>
                </View>

                {/* Created Date */}
                <Text style={[
                    styles.dateText,
                    !isActive && styles.inactiveDateText
                ]}>
                    Tạo: {new Date(category.created_at).toLocaleDateString('vi-VN')}
                </Text>
            </View>

            {/* Right Arrow */}
            <View style={styles.arrowContainer}>
                <Text style={[
                    styles.arrow,
                    !isActive && styles.inactiveArrow
                ]}>
                    ›
                </Text>
            </View>
        </TouchableOpacity>
    );
};

const styles = StyleSheet.create({
    container: {
        backgroundColor: theme.colors.background.primary,
        borderRadius: theme.borderRadius.lg,
        padding: theme.spacing.md,
        marginBottom: theme.spacing.md,
        flexDirection: 'row',
        alignItems: 'center',
        borderWidth: 1,
        borderColor: theme.colors.gray[200],
        shadowColor: theme.colors.shadow.primary,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    inactiveContainer: {
        backgroundColor: theme.colors.gray[50],
        borderColor: theme.colors.gray[300],
        borderStyle: 'dashed', // Đường viền đứt nét cho inactive
    },
    iconContainer: {
        width: 44,
        height: 44,
        borderRadius: theme.borderRadius.lg,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: theme.spacing.md,
    },
    incomeIconContainer: {
        backgroundColor: theme.colors.success[100],
    },
    expenseIconContainer: {
        backgroundColor: theme.colors.error[100],
    },
    inactiveIconContainer: {
        backgroundColor: theme.colors.gray[200],
    },
    categoryIcon: {
        fontSize: 20,
    },
    inactiveIcon: {
        opacity: 0.7, // Giảm độ trong suốt một chút thôi
    },
    content: {
        flex: 1,
    },
    header: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
        marginBottom: theme.spacing.sm,
    },
    titleContainer: {
        flex: 1,
        marginRight: theme.spacing.md,
    },
    name: {
        fontSize: theme.typography.fontSizes.lg,
        fontWeight: '600',
        color: theme.colors.text.primary,
        marginBottom: theme.spacing.xs,
    },
    inactiveName: {
        color: theme.colors.gray[600], // Chỉ làm mờ một chút, vẫn đọc được
    },
    typeBadge: {
        paddingHorizontal: theme.spacing.sm,
        paddingVertical: theme.spacing.xs,
        borderRadius: theme.borderRadius.sm,
        alignSelf: 'flex-start',
    },
    incomeBadge: {
        backgroundColor: theme.colors.success[100],
    },
    expenseBadge: {
        backgroundColor: theme.colors.error[100],
    },
    inactiveTypeBadge: {
        backgroundColor: theme.colors.gray[200],
    },
    typeText: {
        fontSize: theme.typography.fontSizes.xs,
        fontWeight: '500',
    },
    incomeTypeText: {
        color: theme.colors.success[700],
    },
    expenseTypeText: {
        color: theme.colors.error[700],
    },
    inactiveTypeText: {
        color: theme.colors.gray[600],
    },
    statusToggle: {
        width: 44,
        height: 24,
        borderRadius: 12,
        justifyContent: 'center',
        position: 'relative',
    },
    activeToggle: {
        backgroundColor: theme.colors.primary[500],
    },
    inactiveToggle: {
        backgroundColor: theme.colors.gray[300],
    },
    toggleThumb: {
        width: 20,
        height: 20,
        borderRadius: 10,
        backgroundColor: theme.colors.background.primary,
        position: 'absolute',
    },
    statusContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: theme.spacing.xs,
    },
    statusDot: {
        width: 8,
        height: 8,
        borderRadius: 4,
        marginRight: theme.spacing.sm,
    },
    activeDot: {
        backgroundColor: theme.colors.success[500],
    },
    inactiveDot: {
        backgroundColor: theme.colors.gray[400],
    },
    statusText: {
        fontSize: theme.typography.fontSizes.sm,
        color: theme.colors.text.secondary,
        fontWeight: '500',
    },
    inactiveStatusText: {
        color: theme.colors.gray[500],
    },
    dateText: {
        fontSize: theme.typography.fontSizes.xs,
        color: theme.colors.text.secondary,
    },
    inactiveDateText: {
        color: theme.colors.gray[500],
    },
    arrowContainer: {
        justifyContent: 'center',
        alignItems: 'center',
        paddingLeft: theme.spacing.sm,
    },
    arrow: {
        fontSize: theme.typography.fontSizes.xl,
        color: theme.colors.gray[400],
        fontWeight: 'bold',
    },
    inactiveArrow: {
        color: theme.colors.gray[300],
    },
});

export default CategoryCard;