import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  FlatList,
  Modal,
  ActivityIndicator,
} from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

import { RootStackParamList } from '../navigation/types';
import { TransactionType, InputMethod, CreateTransactionData } from '../types/transaction';
import { TransactionAPI } from '../../src/api/transaction.api';
import { CategoryAPI, ICategory, CategoryType, CategoryStatus } from '../../src/api/category.api';
import theme from '../theme';
import Screen from '../components/common/Screen';
import Button from '../components/ui/Button';
import TextInput from '../components/ui/TextInput';
import Card from '../components/ui/Card';
import DatePicker from '../components/ui/DatePicker';
import ImagePicker from '../components/ui/ImagePicker';

type CreateTransactionScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'CreateTransaction'
>;

// Use ICategory from API instead of local interface

interface CategorySelectorProps {
  visible: boolean;
  onClose: () => void;
  onSelect: (category: ICategory) => void;
  transactionType: TransactionType;
}

const CategorySelector: React.FC<CategorySelectorProps> = ({
  visible,
  onClose,
  onSelect,
  transactionType,
}) => {
  const [categories, setCategories] = useState<ICategory[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (visible) {
      loadCategories();
    }
  }, [visible, transactionType]);

  const loadCategories = async () => {
    try {
      setLoading(true);

      // Map TransactionType to CategoryType
      const categoryType = transactionType === TransactionType.EXPENSE
        ? CategoryType.EXPENSE
        : CategoryType.INCOME;

      const response = await CategoryAPI.getAll({
        type: categoryType,
        status: CategoryStatus.ACTIVE,
        page: 1,
        limit: 100
      });

      setCategories(response.data || []);
    } catch (error) {
      console.error('Error loading categories:', error);
      Alert.alert('Error', 'Unable to load categories. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const renderCategoryItem = ({ item }: { item: ICategory }) => (
    <TouchableOpacity
      style={styles.categoryItem}
      onPress={() => {
        onSelect(item);
        onClose();
      }}
    >
      <Text style={styles.categoryItemIcon}>📂</Text>
      <Text style={styles.categoryItemName}>{item.name}</Text>
    </TouchableOpacity>
  );

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <View style={styles.modalContainer}>
        <View style={styles.modalHeader}>
          <TouchableOpacity onPress={onClose}>
            <Text style={styles.modalCancelText}>Cancel</Text>
          </TouchableOpacity>
          <Text style={styles.modalTitle}>Select Category</Text>
          <View style={{ width: 50 }} />
        </View>

        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.colors.primary[500]} />
            <Text style={styles.loadingText}>Loading categories...</Text>
          </View>
        ) : (
          <FlatList
            data={categories}
            renderItem={renderCategoryItem}
            keyExtractor={(item) => item.id}
            numColumns={2}
            contentContainerStyle={styles.categoriesGrid}
            showsVerticalScrollIndicator={false}
          />
        )}
      </View>
    </Modal>
  );
};

const CreateTransactionScreen = () => {
  const navigation = useNavigation<CreateTransactionScreenNavigationProp>();

  const [formData, setFormData] = useState<CreateTransactionData>({
    amount: 0,
    type: TransactionType.EXPENSE,
    transaction_date: new Date(),
    input_method: InputMethod.MANUAL,
  });

  const [selectedCategory, setSelectedCategory] = useState<ICategory | null>(null);
  const [amountText, setAmountText] = useState('');
  const [description, setDescription] = useState('');
  const [notes, setNotes] = useState('');
  const [imageUrl, setImageUrl] = useState<string>();
  const [showCategorySelector, setShowCategorySelector] = useState(false);
  const [loading, setLoading] = useState(false);

  // Format amount input
  const handleAmountChange = (text: string) => {
    // Remove non-numeric characters
    const numericValue = text.replace(/[^0-9]/g, '');

    if (numericValue) {
      const amount = parseInt(numericValue, 10);
      const formattedValue = amount.toLocaleString('vi-VN');
      setAmountText(formattedValue);
      setFormData(prev => ({ ...prev, amount }));
    } else {
      setAmountText('');
      setFormData(prev => ({ ...prev, amount: 0 }));
    }
  };

  const handleCategorySelect = (category: ICategory) => {
    setSelectedCategory(category);
    setFormData(prev => ({ ...prev, category_id: category.id }));
  };

  const handleSubmit = async () => {
    // Validation
    if (formData.amount <= 0) {
      Alert.alert('Error', 'Please enter a valid amount');
      return;
    }

    if (formData.transaction_date > new Date()) {
      Alert.alert('Error', 'Transaction date cannot be in the future');
      return;
    }

    try {
      setLoading(true);

      const transactionData: CreateTransactionData = {
        ...formData,
        description: description.trim() || undefined,
        notes: notes.trim() || undefined,
        image_url: imageUrl?.trim() || undefined,
      };

      await TransactionAPI.create(transactionData);

      Alert.alert(
        'Success',
        'Transaction created successfully',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (error) {
      console.error('Error creating transaction:', error);
      Alert.alert('Error', 'Unable to create transaction. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const renderTypeSelector = () => (
    <Card style={styles.sectionCard}>
      <Text style={styles.sectionTitle}>Transaction Type</Text>
      <View style={styles.typeSelector}>
        <TouchableOpacity
          style={[
            styles.typeButton,
            formData.type === TransactionType.EXPENSE && styles.activeTypeButton,
          ]}
          onPress={() => {
            setFormData(prev => ({ ...prev, type: TransactionType.EXPENSE }));
            setSelectedCategory(null); // Reset category when changing type
          }}
        >
          <Text style={styles.typeButtonIcon}>💸</Text>
          <Text
            style={[
              styles.typeButtonText,
              formData.type === TransactionType.EXPENSE && styles.activeTypeButtonText,
            ]}
          >
            Expense
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.typeButton,
            formData.type === TransactionType.INCOME && styles.activeTypeButton,
          ]}
          onPress={() => {
            setFormData(prev => ({ ...prev, type: TransactionType.INCOME }));
            setSelectedCategory(null); // Reset category when changing type
          }}
        >
          <Text style={styles.typeButtonIcon}>💰</Text>
          <Text
            style={[
              styles.typeButtonText,
              formData.type === TransactionType.INCOME && styles.activeTypeButtonText,
            ]}
          >
            Income
          </Text>
        </TouchableOpacity>
      </View>
    </Card>
  );

  const renderAmountSection = () => (
    <Card style={styles.sectionCard}>
      <Text style={styles.sectionTitle}>Amount *</Text>
      <View style={styles.amountContainer}>
        <Text style={styles.currencySymbol}>₫</Text>
        <TextInput
          placeholder="0"
          keyboardType="numeric"
          value={amountText}
          onChangeText={handleAmountChange}
          containerStyle={styles.amountInputContainer}
          inputStyle={styles.amountInput}
        />
      </View>
      {formData.amount > 0 && (
        <Text style={styles.amountHelper}>
          {formData.amount.toLocaleString('vi-VN')} VND
        </Text>
      )}
    </Card>
  );

  const renderCategorySection = () => (
    <Card style={styles.sectionCard}>
      <Text style={styles.sectionTitle}>Category</Text>
      <TouchableOpacity
        style={styles.categoryButton}
        onPress={() => setShowCategorySelector(true)}
      >
        <View style={styles.categoryButtonContent}>
          <Text style={styles.categoryButtonIcon}>
            📂
          </Text>
          <Text style={styles.categoryButtonText}>
            {selectedCategory?.name || 'Select Category'}
          </Text>
        </View>
        <Text style={styles.categoryButtonArrow}>›</Text>
      </TouchableOpacity>
    </Card>
  );

  const renderDateSection = () => (
    <DatePicker
      label="Transaction Date *"
      value={formData.transaction_date}
      onChange={(date) => setFormData(prev => ({ ...prev, transaction_date: date }))}
      mode="date"
      maximumDate={new Date()}
      minimumDate={new Date('2000-01-01')}
      placeholder="Select transaction date"
      containerStyle={styles.sectionCard}
    />
  );

  const renderDescriptionSection = () => (
    <Card style={styles.sectionCard}>
      <TextInput
        label="Description"
        placeholder="Transaction description (optional)"
        value={description}
        onChangeText={setDescription}
        maxLength={255}
        multiline
        numberOfLines={3}
      />
      <Text style={styles.characterCount}>{description.length}/255</Text>
    </Card>
  );

  const renderNotesSection = () => (
    <Card style={styles.sectionCard}>
      <TextInput
        label="Notes"
        placeholder="Additional notes (optional)"
        value={notes}
        onChangeText={setNotes}
        maxLength={1000}
        multiline
        numberOfLines={4}
      />
      <Text style={styles.characterCount}>{notes.length}/1000</Text>
    </Card>
  );

  const renderImageSection = () => (
    <ImagePicker
      label="Transaction Image"
      placeholder="Add an image of your transaction (optional)"
      value={imageUrl}
      onImageSelected={setImageUrl}
      onImageRemoved={() => setImageUrl(undefined)}
      containerStyle={styles.sectionCard}
    />
  );

  const renderInputMethodSection = () => (
    <Card style={styles.sectionCard}>
      <Text style={styles.sectionTitle}>Input Method</Text>
      <View style={styles.inputMethodContainer}>
        {[
          { key: InputMethod.MANUAL, label: 'Manual', icon: '✏️' },
          { key: InputMethod.VOICE, label: 'Voice', icon: '🎤' },
          { key: InputMethod.CHAT, label: 'Chat', icon: '💬' },
          { key: InputMethod.IMAGE, label: 'Image', icon: '📷' },
        ].map((method) => (
          <TouchableOpacity
            key={method.key}
            style={[
              styles.inputMethodButton,
              formData.input_method === method.key && styles.activeInputMethod,
            ]}
            onPress={() => setFormData(prev => ({ ...prev, input_method: method.key }))}
          >
            <Text style={styles.inputMethodIcon}>{method.icon}</Text>
            <Text style={[
              styles.inputMethodText,
              formData.input_method === method.key && styles.activeInputMethodText,
            ]}>
              {method.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </Card>
  );

  return (
    <Screen>

      <KeyboardAwareScrollView
        style={styles.container}
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
        enableOnAndroid={true}
        enableAutomaticScroll={true}
        extraScrollHeight={50}
        extraHeight={150}
        enableResetScrollToCoords={false}
        scrollEnabled={true}
      >

        {renderTypeSelector()}
        {renderAmountSection()}
        {renderCategorySection()}
        {renderDateSection()}
        {renderDescriptionSection()}
        {renderNotesSection()}
        {renderImageSection()}
        {renderInputMethodSection()}

        <Button
          title={loading ? 'Creating...' : 'Create Transaction'}
          onPress={handleSubmit}
          disabled={loading || formData.amount <= 0}
          style={styles.submitButton}
        />
      </KeyboardAwareScrollView>

      <CategorySelector
        visible={showCategorySelector}
        onClose={() => setShowCategorySelector(false)}
        onSelect={handleCategorySelect}
        transactionType={formData.type}
      />
    </Screen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background.secondary,
  },
  header: {
    paddingHorizontal: theme.spacing.md,
    paddingTop: theme.spacing.md,
    paddingBottom: theme.spacing.md,
  },
  title: {
    fontSize: theme.typography.fontSizes['2xl'],
    fontWeight: '700',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.xs,
  },
  subtitle: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.secondary,
  },
  scrollContainer: {
    padding: theme.spacing.md,
  },
  headerTitle: {
    fontSize: theme.typography.fontSizes['2xl'],
    fontWeight: theme.typography.fontWeights.bold,
    color: theme.colors.text.primary,
    textAlign: 'center',
    marginBottom: theme.spacing.xl,
  },
  sectionCard: {
    marginBottom: theme.spacing.md,
    padding: theme.spacing.md,
  },
  sectionTitle: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: theme.typography.fontWeights.semibold,
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.sm,
  },
  
  // Type Selector
  typeSelector: {
    flexDirection: 'row',
    backgroundColor: theme.colors.gray[100],
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.xs,
  },
  typeButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
    gap: theme.spacing.sm,
  },
  activeTypeButton: {
    backgroundColor: theme.colors.primary[500],
  },
  typeButtonIcon: {
    fontSize: 20,
  },
  typeButtonText: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '500' as const,
    color: theme.colors.text.secondary,
  },
  activeTypeButtonText: {
    color: theme.colors.text.inverse,
  },

  // Amount Section
  amountContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.gray[50],
    borderRadius: theme.borderRadius.lg,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
  },
  currencySymbol: {
    fontSize: theme.typography.fontSizes['2xl'],
    fontWeight: theme.typography.fontWeights.semibold,
    color: theme.colors.text.primary,
    marginRight: theme.spacing.sm,
  },
  amountInputContainer: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  amountInput: {
    fontSize: theme.typography.fontSizes['2xl'],
    fontWeight: theme.typography.fontWeights.semibold,
    textAlign: 'right',
  },
  amountHelper: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
    marginTop: theme.spacing.xs,
    textAlign: 'center',
  },

  // Category Section
  categoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.md,
    backgroundColor: theme.colors.gray[50],
    borderRadius: theme.borderRadius.md,
  },
  categoryButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.sm,
  },
  categoryButtonIcon: {
    fontSize: 24,
  },
  categoryButtonText: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.primary,
  },
  categoryButtonArrow: {
    fontSize: 20,
    color: theme.colors.text.secondary,
  },

  // Date Section
  dateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.md,
    backgroundColor: theme.colors.gray[50],
    borderRadius: theme.borderRadius.md,
    gap: theme.spacing.sm,
  },
  dateButtonIcon: {
    fontSize: 20,
  },
  dateButtonText: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.primary,
  },

  // Character Count
  characterCount: {
    fontSize: theme.typography.fontSizes.xs,
    color: theme.colors.text.secondary,
    textAlign: 'right',
    marginTop: theme.spacing.xs,
  },

  // Input Method Section
  inputMethodContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: theme.spacing.sm,
  },
  inputMethodButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.md,
    backgroundColor: theme.colors.gray[100],
    borderRadius: theme.borderRadius.md,
    gap: theme.spacing.xs,
  },
  activeInputMethod: {
    backgroundColor: theme.colors.primary[500],
  },
  inputMethodIcon: {
    fontSize: 16,
  },
  inputMethodText: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
  },
  activeInputMethodText: {
    color: theme.colors.text.inverse,
  },

  // Submit Button
  submitButton: {
    marginTop: theme.spacing.xl,
    marginBottom: theme.spacing.xl,
  },

  // Modal Styles
  modalContainer: {
    flex: 1,
    backgroundColor: theme.colors.background.primary,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray[200],
  },
  modalCancelText: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.secondary,
  },
  modalTitle: {
    fontSize: theme.typography.fontSizes.lg,
    fontWeight: theme.typography.fontWeights.semibold,
    color: theme.colors.text.primary,
  },
  categoriesGrid: {
    padding: theme.spacing.md,
  },
  categoryItem: {
    flex: 1,
    aspectRatio: 1,
    margin: theme.spacing.xs,
    padding: theme.spacing.md,
    backgroundColor: theme.colors.background.secondary,
    borderRadius: theme.borderRadius.lg,
    alignItems: 'center',
    justifyContent: 'center',
    gap: theme.spacing.sm,
  },
  categoryItemIcon: {
    fontSize: 32,
  },
  categoryItemName: {
    fontSize: theme.typography.fontSizes.sm,
    fontWeight: '500' as const,
    color: theme.colors.text.primary,
    textAlign: 'center',
  },

  // Loading styles
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: theme.spacing.xl,
  },
  loadingText: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.secondary,
    marginTop: theme.spacing.sm,
  },
});

export default CreateTransactionScreen; 