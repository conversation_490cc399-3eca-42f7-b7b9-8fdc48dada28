import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import { useNavigation, useRoute, useFocusEffect } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation/types';
import theme from '../theme';
import Screen from '../components/common/Screen';
import Button from '../components/ui/Button';
import Card from '../components/ui/Card';
import { CategoryAPI, ICategory } from '../../src/api/category.api';
import { TransactionAPI } from '../../src/api/transaction.api';
import { Transaction, TransactionType } from '../types/transaction';

const { width } = Dimensions.get('window');

type CategoryDetailScreenRouteProp = {
  params: {
    id: string;
  };
};

type CategoryDetailScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'CategoryDetail'
>;

// Remove old interface - using Transaction from types

const CategoryDetailScreen = () => {
  const navigation = useNavigation<CategoryDetailScreenNavigationProp>();
  const route = useRoute<CategoryDetailScreenRouteProp>();
  const { id } = route.params;

  // State for category data
  const [category, setCategory] = useState<ICategory | null>(null);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [totalAmount, setTotalAmount] = useState(0);

  // Load category data
  const loadCategoryData = async () => {
    try {
      setLoading(true);

      // Load category details
      const categoryResponse: any = await CategoryAPI.getOne(id, { page: 1, limit: 10 });

      // Handle different response structures for category
      let categoryData: ICategory;
      if (categoryResponse.data) {
        categoryData = categoryResponse.data.category || categoryResponse.data;
      } else {
        categoryData = categoryResponse;
      }
      setCategory(categoryData);

      // Load transactions for this category
      try {
        const transactionsResponse = await TransactionAPI.getAll({
          category_id: id,
          page: 1,
          limit: 10,
          sort_by: 'transaction_date',
          sort_order: 'DESC'
        });

        if (transactionsResponse.data) {
          setTransactions(transactionsResponse.data);

          // Calculate total amount
          const total = transactionsResponse.data.reduce((sum: number, transaction: Transaction) => {
            return sum + transaction.amount;
          }, 0);
          setTotalAmount(total);
        }
      } catch (transactionError) {
        console.error('Error loading transactions:', transactionError);
        // Don't show error for transactions, just set empty array
        setTransactions([]);
        setTotalAmount(0);
      }

    } catch (error: any) {
      console.error('Error loading category:', error);
      Alert.alert('Error', 'Unable to load category details. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Load data when screen focuses
  useFocusEffect(
    React.useCallback(() => {
      loadCategoryData();
    }, [id])
  );

  const formatCurrency = (amount: number) => {
    return amount.toLocaleString('en-US') + ' ₫';
  };

  const handleEditCategory = () => {
    navigation.navigate('EditCategory', { categoryId: id });
  };

  const handleDeleteCategory = () => {
    setShowDeleteConfirm(true);
  };

  const confirmDelete = async () => {
    try {
      await CategoryAPI.delete(id);
      Alert.alert('Success', 'Category deleted successfully');
      setShowDeleteConfirm(false);
      navigation.goBack();
    } catch (error: any) {
      console.error('Error deleting category:', error);
      Alert.alert('Error', 'Failed to delete category. Please try again.');
      setShowDeleteConfirm(false);
    }
  };

  const cancelDelete = () => {
    setShowDeleteConfirm(false);
  };

  if (loading) {
    return (
      <Screen>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary[500]} />
          <Text style={styles.loadingText}>Loading category details...</Text>
        </View>
      </Screen>
    );
  }

  if (!category) {
    return (
      <Screen>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Category not found</Text>
          <Button
            title="Go Back"
            onPress={() => navigation.goBack()}
            variant="primary"
          />
        </View>
      </Screen>
    );
  }

  return (
    <Screen>
      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
      >
        {/* Category Header */}
        <View style={[styles.categoryHeader, { backgroundColor: theme.colors.gray[100] }]}>
          <View style={styles.iconContainer}>
            <Text style={styles.iconText}>📁</Text>
          </View>
          <Text style={styles.categoryText}>{category.name}</Text>
          <Text style={styles.periodText}>Current Period</Text>
        </View>

        {/* Category Info */}
        <Card style={styles.progressCard}>
          <View style={styles.categoryInfoContainer}>
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Type</Text>
              <Text style={styles.infoValue}>{category.type}</Text>
            </View>
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Status</Text>
              <Text style={[
                styles.infoValue,
                { color: category.status === 'active' ? theme.colors.success[500] : theme.colors.error[500] }
              ]}>
                {category.status}
              </Text>
            </View>
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Transactions</Text>
              <Text style={styles.infoValue}>{transactions.length}</Text>
            </View>
            <View style={styles.infoItem}>
              <Text style={styles.infoLabel}>Total Amount</Text>
              <Text style={styles.infoValue}>{formatCurrency(totalAmount)}</Text>
            </View>
          </View>
        </Card>

        {/* Recent Transactions */}
        <Card style={styles.transactionsCard}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Recent Transactions</Text>
            <TouchableOpacity onPress={() => Alert.alert('Info', 'View all transactions feature coming soon')}>
              <Text style={styles.viewAllText}>View All</Text>
            </TouchableOpacity>
          </View>

          {transactions.length === 0 ? (
            <View style={styles.emptyTransactions}>
              <Text style={styles.emptyText}>No transactions found for this category</Text>
            </View>
          ) : (
            transactions.slice(0, 5).map((transaction, index) => (
              <TouchableOpacity
                key={transaction.id}
                style={[
                  styles.transactionItem,
                  index === Math.min(transactions.length, 5) - 1 && { borderBottomWidth: 0 }
                ]}
                onPress={() => Alert.alert('Info', 'Transaction details feature coming soon')}
              >
                <View style={styles.transactionInfo}>
                  <Text style={styles.transactionDescription}>
                    {transaction.description || 'Transaction'}
                  </Text>
                  <Text style={styles.transactionDate}>
                    {new Date(transaction.transaction_date).toLocaleDateString('en-US')}
                  </Text>
                </View>
                <Text style={styles.transactionAmount}>
                  {formatCurrency(transaction.amount)}
                </Text>
              </TouchableOpacity>
            ))
          )}
        </Card>



        {/* Action Buttons */}
        <View style={styles.actionContainer}>
          <Button
            title="Edit Category"
            onPress={handleEditCategory}
            variant="primary"
            fullWidth
            style={styles.editButton}
          />

          <TouchableOpacity
            style={styles.deleteButton}
            onPress={handleDeleteCategory}
          >
            <Text style={styles.deleteButtonText}>Delete Category</Text>
          </TouchableOpacity>
        </View>

        {/* Delete Confirmation */}
        {showDeleteConfirm && (
          <View style={styles.confirmationOverlay}>
            <View style={styles.confirmationBox}>
              <Text style={styles.confirmationTitle}>Delete Category</Text>
              <Text style={styles.confirmationText}>
                Are you sure you want to delete this category? This action cannot be undone.
              </Text>
              <View style={styles.confirmationButtons}>
                <TouchableOpacity
                  style={styles.cancelButton}
                  onPress={cancelDelete}
                >
                  <Text style={styles.cancelButtonText}>Cancel</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.confirmButton}
                  onPress={confirmDelete}
                >
                  <Text style={styles.confirmButtonText}>Delete</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        )}
      </ScrollView>
    </Screen>
  );
};

const styles = StyleSheet.create({
  scrollContainer: {
    flexGrow: 1,
    paddingBottom: theme.spacing.xl,
  },
  categoryHeader: {
    padding: theme.spacing.xl,
    alignItems: 'center',
  },
  iconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: theme.colors.background.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  iconText: {
    fontSize: 32,
  },
  categoryText: {
    fontSize: theme.typography.fontSizes['2xl'],
    fontWeight: '700',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.sm,
  },
  periodText: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.secondary,
  },
  progressCard: {
    margin: theme.spacing.md,
  },
  progressTextContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  progressLabel: {
    fontSize: theme.typography.fontSizes.sm,
    fontWeight: '500',
    color: theme.colors.text.primary,
  },
  progressPercentage: {
    fontSize: theme.typography.fontSizes.sm,
    fontWeight: '600',
    color: theme.colors.primary[500],
  },
  progressBarContainer: {
    height: 12,
    backgroundColor: theme.colors.gray[100],
    borderRadius: theme.borderRadius.sm,
    marginBottom: theme.spacing.md,
    overflow: 'hidden',
  },
  progressBar: {
    height: 12,
    backgroundColor: theme.colors.primary[500],
    borderRadius: theme.borderRadius.sm,
  },
  overBudgetBar: {
    backgroundColor: theme.colors.error[500],
  },
  budgetStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statItem: {
    alignItems: 'center',
  },
  statLabel: {
    fontSize: theme.typography.fontSizes.xs,
    color: theme.colors.text.secondary,
    marginBottom: theme.spacing.xs,
  },
  statAmount: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '600',
    color: theme.colors.text.primary,
  },
  overBudgetText: {
    color: theme.colors.error[500],
  },
  remainingText: {
    color: theme.colors.success[500],
  },
  trendCard: {
    margin: theme.spacing.md,
    marginTop: 0,
  },
  sectionTitle: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '600',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.md,
  },
  chartContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    height: 200,
  },
  chartBar: {
    flex: 1,
    alignItems: 'center',
  },
  chartAmount: {
    fontSize: theme.typography.fontSizes.xs,
    color: theme.colors.text.secondary,
    marginBottom: theme.spacing.xs,
  },
  barContainer: {
    width: '50%',
    alignItems: 'center',
    justifyContent: 'flex-end',
    height: 150,
  },
  bar: {
    width: '100%',
    backgroundColor: theme.colors.primary[100],
    borderTopLeftRadius: theme.borderRadius.sm,
    borderTopRightRadius: theme.borderRadius.sm,
  },
  currentMonthBar: {
    backgroundColor: theme.colors.primary[500],
  },
  chartLabel: {
    fontSize: theme.typography.fontSizes.xs,
    color: theme.colors.text.secondary,
    marginTop: theme.spacing.sm,
  },
  timeFilterContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    paddingHorizontal: theme.spacing.md,
    marginBottom: theme.spacing.md,
  },
  filterButton: {
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.md,
    borderRadius: theme.borderRadius.full,
    marginHorizontal: theme.spacing.sm,
    backgroundColor: theme.colors.gray[100],
  },
  activeFilterButton: {
    backgroundColor: theme.colors.primary[100],
  },
  filterText: {
    fontSize: theme.typography.fontSizes.sm,
    fontWeight: '500',
    color: theme.colors.text.secondary,
  },
  activeFilterText: {
    color: theme.colors.primary[500],
  },
  transactionsCard: {
    margin: theme.spacing.md,
    marginTop: 0,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  viewAllText: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.primary[500],
    fontWeight: '500',
  },
  transactionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: theme.spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray[100],
  },
  transactionDescription: {
    fontSize: theme.typography.fontSizes.sm,
    fontWeight: '500',
    color: theme.colors.text.primary,
    marginBottom: 2,
  },
  transactionLocation: {
    fontSize: theme.typography.fontSizes.xs,
    color: theme.colors.text.secondary,
    marginBottom: 2,
  },
  transactionDate: {
    fontSize: theme.typography.fontSizes.xs,
    color: theme.colors.text.secondary,
  },
  transactionAmount: {
    fontSize: theme.typography.fontSizes.sm,
    fontWeight: '600',
    color: theme.colors.error[500],
    alignSelf: 'center',
  },
  addTransactionButton: {
    backgroundColor: theme.colors.primary[100],
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.sm,
    alignItems: 'center',
    marginTop: theme.spacing.md,
  },
  addTransactionText: {
    fontSize: theme.typography.fontSizes.sm,
    fontWeight: '500',
    color: theme.colors.primary[500],
  },
  actionContainer: {
    margin: theme.spacing.md,
    marginTop: theme.spacing.sm,
  },
  editButton: {
    marginBottom: theme.spacing.sm,
  },
  deleteButton: {
    backgroundColor: theme.colors.background.primary,
    borderWidth: 1,
    borderColor: theme.colors.error[500],
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.sm,
    alignItems: 'center',
  },
  deleteButtonText: {
    color: theme.colors.error[500],
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '600',
  },
  confirmationOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: theme.spacing.xl,
  },
  confirmationBox: {
    backgroundColor: theme.colors.background.primary,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.xl,
    width: '100%',
  },
  confirmationTitle: {
    fontSize: theme.typography.fontSizes.lg,
    fontWeight: '600',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.sm,
  },
  confirmationText: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
    marginBottom: theme.spacing.xl,
  },
  confirmationButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  cancelButton: {
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.md,
    marginRight: theme.spacing.sm,
  },
  cancelButtonText: {
    fontSize: theme.typography.fontSizes.sm,
    fontWeight: '500',
    color: theme.colors.text.secondary,
  },
  confirmButton: {
    backgroundColor: theme.colors.error[500],
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
  },
  confirmButtonText: {
    fontSize: theme.typography.fontSizes.sm,
    fontWeight: '500',
    color: theme.colors.background.primary,
  },
  // Loading and error states
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: theme.spacing.xl,
  },
  loadingText: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.secondary,
    marginTop: theme.spacing.md,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: theme.spacing.xl,
  },
  errorText: {
    fontSize: theme.typography.fontSizes.lg,
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.xl,
    textAlign: 'center',
  },
  // Category info styles
  categoryInfoContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  infoItem: {
    alignItems: 'center',
    flex: 1,
  },
  infoLabel: {
    fontSize: theme.typography.fontSizes.xs,
    color: theme.colors.text.secondary,
    marginBottom: theme.spacing.xs,
    textTransform: 'uppercase',
  },
  infoValue: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '600',
    color: theme.colors.text.primary,
    textTransform: 'capitalize',
  },
  // Empty states
  emptyTransactions: {
    padding: theme.spacing.xl,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
    textAlign: 'center',
  },
  // Transaction info
  transactionInfo: {
    flex: 1,
  },
});

export default CategoryDetailScreen;