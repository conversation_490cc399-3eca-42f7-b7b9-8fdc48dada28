import React from 'react';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { cssInterop } from 'nativewind';
import { ViewStyle, StyleProp } from 'react-native';

const StyledKeyboardAwareScrollView = cssInterop(KeyboardAwareScrollView, { className: 'style' });

interface KeyboardAwareViewProps {
  children: React.ReactNode;
  className?: string;
  contentContainerStyle?: StyleProp<ViewStyle>;
  showsVerticalScrollIndicator?: boolean;
  keyboardShouldPersistTaps?: 'always' | 'never' | 'handled';
  extraScrollHeight?: number;
  enableOnAndroid?: boolean;
}

const KeyboardAwareView: React.FC<KeyboardAwareViewProps> = ({
  children,
  className = '',
  contentContainerStyle,
  showsVerticalScrollIndicator = false,
  keyboardShouldPersistTaps = 'handled',
  extraScrollHeight = 120,
  enableOnAndroid = true,
}) => {
  return (
    <StyledKeyboardAwareScrollView
      className={`flex-1 ${className}`}
      contentContainerStyle={contentContainerStyle}
      showsVerticalScrollIndicator={showsVerticalScrollIndicator}
      keyboardShouldPersistTaps={keyboardShouldPersistTaps}
      extraScrollHeight={extraScrollHeight}
      enableOnAndroid={enableOnAndroid}
      enableResetScrollToCoords={false}
    >
      {children}
    </StyledKeyboardAwareScrollView>
  );
};

export default KeyboardAwareView; 