import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { <PERSON><PERSON><PERSON>, Pie<PERSON>hart } from 'react-native-chart-kit';
import theme from '../theme';
import Screen from '../components/common/Screen';
import DrawerHeader from '../components/common/DrawerHeader';
import Card from '../components/ui/Card';
import {
  StatisticsAPI,
  IncomeExpenseChartData,
  CategoryAnalysisData,
  FinancialTrendsData,
  ComparisonData,
} from '../../src/api/statistics.api';

const { width: screenWidth } = Dimensions.get('window');

const AnalyticsScreen = () => {
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'year'>('month');
  const [loading, setLoading] = useState(false);
  
  // Data states
  const [incomeExpenseData, setIncomeExpenseData] = useState<IncomeExpenseChartData | null>(null);
  const [categoryData, setCategoryData] = useState<CategoryAnalysisData | null>(null);
  const [trendsData, setTrendsData] = useState<FinancialTrendsData | null>(null);
  const [comparisonData, setComparisonData] = useState<ComparisonData | null>(null);
  const trendsScrollViewRef = useRef<ScrollView>(null);

  const chartConfig = {
    backgroundColor: '#ffffff',
    backgroundGradientFrom: '#ffffff',
    backgroundGradientTo: '#ffffff',
    decimalPlaces: 0,
    color: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
    labelColor: (opacity = 1) => `rgba(0, 0, 0, ${opacity})`,
    style: {
      borderRadius: 16,
    },
    propsForDots: {
      r: '6',
      strokeWidth: '2',
      stroke: '#0EA5E9',
    },
  };

  const periods = [
    { key: 'week' as const, label: 'Week' },
    { key: 'month' as const, label: 'Month' },
    { key: 'year' as const, label: 'Year' },
  ];

  // Load all analytics data
  const loadAnalyticsData = async () => {
    try {
      setLoading(true);
      console.log('📊 Loading analytics data for period:', selectedPeriod);

      // 1. Load Income Expense Chart
      const incomeExpenseResult = await StatisticsAPI.getIncomeExpenseChart({
        period: selectedPeriod,
      });
      setIncomeExpenseData(incomeExpenseResult);
      console.log('📈 Income Expense Data:', incomeExpenseResult);

      // 2. Load Category Analysis
      const categoryResult = await StatisticsAPI.getCategoryAnalysis({
        period: selectedPeriod === 'week' ? 'month' : selectedPeriod,
        type: 'expense', // Focus on expenses for pie chart
      });
      setCategoryData(categoryResult);
      console.log('🥧 Category Data:', categoryResult);

      // 3. Load Financial Trends
      const trendsResult = await StatisticsAPI.getFinancialTrends({
        period: selectedPeriod === 'week' ? 'month' : selectedPeriod,
        limit: selectedPeriod === 'year' ? 5 : 12,
      });
      setTrendsData(trendsResult);
      console.log('📊 Trends Data:', trendsResult);

      // 4. Load Comparison Data (last 3 periods)
      const currentDate = new Date();
      const periods: string[] = [];
      
      if (selectedPeriod === 'month') {
        for (let i = 2; i >= 0; i--) {
          const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
          periods.push(`${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`);
        }
      } else if (selectedPeriod === 'year') {
        for (let i = 2; i >= 0; i--) {
          periods.push((currentDate.getFullYear() - i).toString());
        }
      }

      if (periods.length > 0) {
        const comparisonResult = await StatisticsAPI.getComparison({
          periods,
          periodType: selectedPeriod === 'week' ? 'month' : selectedPeriod,
        });
        setComparisonData(comparisonResult);
        console.log('⚖️ Comparison Data:', comparisonResult);
      }

    } catch (error: any) {
      console.error('📊 Analytics error:', error);
      Alert.alert('Error', 'Failed to load analytics data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadAnalyticsData();
  }, [selectedPeriod]);

  // Auto scroll to current month in trends chart
  useEffect(() => {
    if (trendsData && trendsScrollViewRef.current) {
      const currentDate = new Date();
      const currentMonth = currentDate.getMonth() + 1;
      const currentYear = currentDate.getFullYear();

      // Find current month index in trends data
      const currentIndex = trendsData.trends.findIndex(trend => {
        // Handle different date formats
        let year, month;
        if (trend.period.includes('/')) {
          // Format: MM/YY
          const [monthStr, yearStr] = trend.period.split('/');
          month = parseInt(monthStr);
          year = parseInt('20' + yearStr); // Convert YY to YYYY
        } else if (trend.period.includes('-')) {
          // Format: YYYY-MM
          const [yearStr, monthStr] = trend.period.split('-');
          year = parseInt(yearStr);
          month = parseInt(monthStr);
        } else {
          return false;
        }

        return year === currentYear && month === currentMonth;
      });

      if (currentIndex >= 0) {
        // Calculate scroll position to center current month
        const chartWidth = Math.max(screenWidth - 40, trendsData.trends.length * 80);
        const itemWidth = chartWidth / trendsData.trends.length;
        const scrollX = Math.max(0, (currentIndex * itemWidth) - (screenWidth / 2) + (itemWidth / 2));

        setTimeout(() => {
          trendsScrollViewRef.current?.scrollTo({
            x: scrollX,
            animated: true,
          });
        }, 1000); // Increased delay to ensure chart is fully rendered
      }
    }
  }, [trendsData]);

  // Format currency
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  // Helper function to format Y-axis values
  const formatYAxisValue = (yLabel: string) => {
    const value = parseFloat(yLabel);
    if (value >= 1000000) {
      return `${(value / 1000000).toFixed(1)}M`;
    } else if (value >= 1000) {
      return `${(value / 1000).toFixed(0)}K`;
    }
    return value.toString();
  };

  // Prepare chart data for Income Bar Chart
  const getIncomeChartData = () => {
    if (!incomeExpenseData) return null;

    return {
      labels: incomeExpenseData.labels,
      datasets: [
        {
          data: incomeExpenseData.incomeData,
        },
      ],
    };
  };

  // Prepare chart data for Expense Bar Chart
  const getExpenseChartData = () => {
    if (!incomeExpenseData) return null;

    return {
      labels: incomeExpenseData.labels,
      datasets: [
        {
          data: incomeExpenseData.expenseData,
        },
      ],
    };
  };

  const getCategoryChartData = () => {
    if (!categoryData || !categoryData.categories.length) return [];

    return categoryData.categories.map((category) => ({
      name: category.categoryName,
      population: category.percentage,
      color: category.color,
      legendFontColor: '#7F7F7F',
      legendFontSize: 14,
    }));
  };

  const getTrendsChartData = () => {
    if (!trendsData) return null;

    return {
      labels: trendsData.trends.map(trend => {
        const [year, month] = trend.period.split('-');
        return month ? `${month}/${year.slice(-2)}` : year;
      }),
      datasets: [
        {
          data: trendsData.trends.map(trend => trend.balance),
        },
      ],
    };
  };

  return (
    <Screen>
      <DrawerHeader title="Analytics" />
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.title}>📊 Analytics</Text>
          <Text style={styles.subtitle}>Financial insights and trends</Text>

          {/* Period Selector */}
          <View style={styles.periodSelector}>
            {periods.map((period) => (
              <TouchableOpacity
                key={period.key}
                style={[
                  styles.periodButton,
                  selectedPeriod === period.key && styles.periodButtonActive,
                ]}
                onPress={() => setSelectedPeriod(period.key)}
              >
                <Text
                  style={[
                    styles.periodButtonText,
                    selectedPeriod === period.key && styles.periodButtonTextActive,
                  ]}
                >
                  {period.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.colors.primary[500]} />
            <Text style={styles.loadingText}>Loading analytics...</Text>
          </View>
        ) : (
          <>
            {/* 1. Income vs Expense Chart */}
            {incomeExpenseData && (
              <Card style={styles.chartCard}>
                <View style={styles.chartHeader}>
                  <Text style={styles.chartTitle}>📈 Income vs Expense</Text>
                  <Text style={styles.netAmountBadge}>
                    Net: {formatCurrency(incomeExpenseData.netAmount)}
                  </Text>
                </View>

                <View style={styles.chartContainer}>
                  {/* Income Chart */}
                  {getIncomeChartData() && (
                    <View style={styles.subChartContainer}>
                      <Text style={styles.subChartTitle}>Income</Text>
                      <BarChart
                        data={getIncomeChartData()!}
                        width={screenWidth - 80}
                        height={160}
                        yAxisLabel=""
                        yAxisSuffix=""
                        chartConfig={{
                          ...chartConfig,
                          barPercentage: 0.9,
                          color: (opacity = 1) => `rgba(34, 197, 94, ${opacity})`,
                          formatYLabel: formatYAxisValue,
                          propsForLabels: {
                            fontSize: 11,
                          },
                        }}
                        style={styles.subChart}
                        showValuesOnTopOfBars={true}
                        fromZero={true}
                      />
                    </View>
                  )}

                  {/* Expense Chart */}
                  {getExpenseChartData() && (
                    <View style={styles.subChartContainer}>
                      <Text style={styles.subChartTitle}>Expense</Text>
                      <BarChart
                        data={getExpenseChartData()!}
                        width={screenWidth - 80}
                        height={160}
                        yAxisLabel=""
                        yAxisSuffix=""
                        chartConfig={{
                          ...chartConfig,
                          barPercentage: 0.9,
                          color: (opacity = 1) => `rgba(239, 68, 68, ${opacity})`,
                          formatYLabel: formatYAxisValue,
                          propsForLabels: {
                            fontSize: 11,
                          },
                        }}
                        style={styles.subChart}
                        showValuesOnTopOfBars={true}
                        fromZero={true}
                      />
                    </View>
                  )}
                </View>

                {/* Legend */}
                <View style={styles.chartLegend}>
                  <View style={styles.legendItem}>
                    <View style={[styles.legendDot, { backgroundColor: 'rgba(34, 197, 94, 1)' }]} />
                    <Text style={styles.legendText}>Income</Text>
                  </View>
                  <View style={styles.legendItem}>
                    <View style={[styles.legendDot, { backgroundColor: 'rgba(239, 68, 68, 1)' }]} />
                    <Text style={styles.legendText}>Expense</Text>
                  </View>
                </View>
              </Card>
            )}

            {/* 2. Category Analysis */}
            {categoryData && getCategoryChartData().length > 0 && (
              <Card style={styles.chartCard}>
                <View style={styles.chartHeader}>
                  <Text style={styles.chartTitle}>🥧 Expense by Category</Text>
                  <Text style={styles.netAmountBadge}>
                    Total: {formatCurrency(categoryData.totalAmount)}
                  </Text>
                </View>

                <View style={styles.chartContainer}>
                  <PieChart
                    data={getCategoryChartData()}
                    width={screenWidth - 60}
                    height={250}
                    chartConfig={chartConfig}
                    accessor="population"
                    backgroundColor="transparent"
                    paddingLeft="15"
                    center={[10, 0]}
                    style={styles.chart}
                    hasLegend={true}
                  />
                </View>
              </Card>
            )}

            {/* 3. Financial Trends */}
            {trendsData && (
              <Card style={styles.chartCard}>
                <View style={styles.chartHeader}>
                  <Text style={styles.chartTitle}>📊 Balance Trends</Text>
                  <Text style={[
                    styles.netAmountBadge,
                    { backgroundColor: trendsData.overallGrowth >= 0 ? theme.colors.success[100] : theme.colors.error[100] },
                    { color: trendsData.overallGrowth >= 0 ? theme.colors.success[700] : theme.colors.error[700] }
                  ]}>
                    {trendsData.overallGrowth >= 0 ? '+' : ''}{trendsData.overallGrowth.toFixed(1)}%
                  </Text>
                </View>

                {getTrendsChartData() && (
                  <ScrollView
                    ref={trendsScrollViewRef}
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    style={styles.horizontalScrollContainer}
                  >
                    <BarChart
                      data={getTrendsChartData()!}
                      width={Math.max(screenWidth - 40, getTrendsChartData()!.labels.length * 80)}
                      height={220}
                      yAxisLabel=""
                      yAxisSuffix=""
                      chartConfig={{
                        ...chartConfig,
                        color: (opacity = 1) => `rgba(14, 165, 233, ${opacity})`,
                        formatYLabel: formatYAxisValue,
                        propsForLabels: {
                          fontSize: 10,
                        },
                        propsForBackgroundLines: {
                          strokeDasharray: "5,5",
                          stroke: "#e0e0e0",
                          strokeWidth: 1,
                        },
                        propsForVerticalLabels: {
                          fontSize: 10,
                          fill: "#666",
                        },
                        propsForHorizontalLabels: {
                          fontSize: 10,
                          fill: "#666",
                        },
                      }}
                      style={styles.chart}
                      showValuesOnTopOfBars={false}
                      fromZero={true}
                      withHorizontalLabels={true}
                      withVerticalLabels={true}
                      withInnerLines={true}
                    />
                  </ScrollView>
                )}
              </Card>
            )}

            {/* 4. Summary Stats */}
            {incomeExpenseData && (
              <Card style={styles.chartCard}>
                <Text style={styles.chartTitle}>💰 Financial Summary</Text>
                <View style={styles.statsContainer}>
                  <View style={styles.statCard}>
                    <Text style={[styles.statValue, { color: theme.colors.success[600] }]}>
                      {formatCurrency(incomeExpenseData.totalIncome)}
                    </Text>
                    <Text style={styles.statLabel}>Total Income</Text>
                  </View>
                  <View style={styles.statCard}>
                    <Text style={[styles.statValue, { color: theme.colors.error[600] }]}>
                      {formatCurrency(incomeExpenseData.totalExpense)}
                    </Text>
                    <Text style={styles.statLabel}>Total Expense</Text>
                  </View>
                  <View style={styles.statCard}>
                    <Text style={[
                      styles.statValue,
                      { color: incomeExpenseData.netAmount >= 0 ? theme.colors.success[600] : theme.colors.error[600] }
                    ]}>
                      {formatCurrency(incomeExpenseData.netAmount)}
                    </Text>
                    <Text style={styles.statLabel}>Net Amount</Text>
                  </View>
                </View>
              </Card>
            )}

            {/* 5. Comparison Summary */}
            {comparisonData && (
              <Card style={styles.chartCard}>
                <Text style={styles.chartTitle}>⚖️ Period Comparison</Text>
                <View style={styles.comparisonContainer}>
                  <View style={styles.comparisonItem}>
                    <View style={[styles.comparisonBadge, { backgroundColor: theme.colors.success[100] }]}>
                      <Text style={styles.comparisonLabel}>Best Period</Text>
                      <Text style={[styles.comparisonValue, { color: theme.colors.success[700] }]}>
                        {comparisonData.bestPeriod}
                      </Text>
                    </View>
                  </View>
                  <View style={styles.comparisonItem}>
                    <View style={[styles.comparisonBadge, { backgroundColor: theme.colors.error[100] }]}>
                      <Text style={styles.comparisonLabel}>Worst Period</Text>
                      <Text style={[styles.comparisonValue, { color: theme.colors.error[700] }]}>
                        {comparisonData.worstPeriod}
                      </Text>
                    </View>
                  </View>
                </View>
              </Card>
            )}
          </>
        )}
      </ScrollView>
    </Screen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background.primary,
  },
  header: {
    padding: theme.spacing.lg,
    paddingBottom: theme.spacing.md,
    backgroundColor: theme.colors.background.primary,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray[200],
  },
  title: {
    fontSize: 28,
    fontWeight: theme.typography.fontWeights.bold,
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.xs,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
    textAlign: 'center',
    marginBottom: theme.spacing.md,
  },
  periodSelector: {
    flexDirection: 'row',
    backgroundColor: theme.colors.gray[100],
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.xs,
    marginTop: theme.spacing.md,
  },
  periodButton: {
    flex: 1,
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
    alignItems: 'center',
  },
  periodButtonActive: {
    backgroundColor: theme.colors.primary[500],
  },
  periodButtonText: {
    fontSize: theme.typography.fontSizes.sm,
    fontWeight: theme.typography.fontWeights.medium,
    color: theme.colors.text.secondary,
  },
  periodButtonTextActive: {
    color: theme.colors.text.inverse,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: theme.spacing.xl,
  },
  loadingText: {
    marginTop: theme.spacing.md,
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
  },
  chartCard: {
    marginHorizontal: theme.spacing.lg,
    marginBottom: theme.spacing.lg,
    padding: theme.spacing.lg,
  },
  chartTitle: {
    fontSize: theme.typography.fontSizes.lg,
    fontWeight: theme.typography.fontWeights.semibold,
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.xs,
    textAlign: 'center',
  },
  chartSubtitle: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
    textAlign: 'center',
    marginBottom: theme.spacing.md,
  },
  chart: {
    borderRadius: theme.borderRadius.md,
  },
  statsContainer: {
    flexDirection: 'row',
    marginTop: theme.spacing.md,
    gap: theme.spacing.md,
  },
  statCard: {
    flex: 1,
    backgroundColor: theme.colors.gray[50],
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.colors.gray[200],
  },
  statValue: {
    fontSize: theme.typography.fontSizes.lg,
    fontWeight: theme.typography.fontWeights.bold,
    color: theme.colors.primary[600],
    marginBottom: theme.spacing.xs,
    textAlign: 'center',
  },
  statLabel: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
    textAlign: 'center',
  },
  comparisonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: theme.spacing.md,
  },
  comparisonItem: {
    alignItems: 'center',
  },
  comparisonLabel: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
    marginBottom: theme.spacing.xs,
  },
  comparisonValue: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: theme.typography.fontWeights.semibold,
    color: theme.colors.primary[600],
  },
  // New styles for improved layout
  chartHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  netAmountBadge: {
    backgroundColor: theme.colors.primary[100],
    color: theme.colors.primary[700],
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.md,
    fontSize: theme.typography.fontSizes.sm,
    fontWeight: theme.typography.fontWeights.medium,
  },
  chartContainer: {
    alignItems: 'center',
    marginVertical: theme.spacing.md,
  },
  chartLegend: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: theme.spacing.md,
    gap: theme.spacing.lg,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.xs,
  },
  legendDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  legendText: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
  },
  comparisonBadge: {
    padding: theme.spacing.lg,
    borderRadius: theme.borderRadius.lg,
    alignItems: 'center',
    minWidth: 120,
  },
  horizontalScrollContainer: {
    marginVertical: theme.spacing.md,
  },
  subChartContainer: {
    marginBottom: theme.spacing.md,
    alignItems: 'center',
  },
  subChartTitle: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: theme.typography.fontWeights.medium,
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.sm,
    textAlign: 'center',
  },
  subChart: {
    borderRadius: theme.borderRadius.md,
  },
});

export default AnalyticsScreen;
