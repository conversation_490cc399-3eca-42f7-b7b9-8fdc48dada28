import { Platform, Alert } from 'react-native';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';

export interface DownloadFileOptions {
  blob: Blob;
  filename: string;
  mimeType: string;
}

/**
 * Download and save file to device storage using Expo APIs
 * Works on both iOS and Android
 */
export const downloadFile = async (options: DownloadFileOptions): Promise<void> => {
  const { blob, filename, mimeType } = options;

  try {
    console.log('💾 Starting file download...');
    console.log('- Filename:', filename);
    console.log('- MIME type:', mimeType);
    console.log('- Blob size:', blob.size);

    // Convert blob to base64
    console.log('🔄 Converting blob to base64...');
    const base64Data = await blobToBase64(blob);
    console.log('✅ Base64 conversion complete, length:', base64Data.length);

    // Create file path in document directory
    const filePath = `${FileSystem.documentDirectory}${filename}`;
    console.log('📁 File path:', filePath);

    // Write file to storage
    console.log('💾 Writing file to storage...');
    await FileSystem.writeAsStringAsync(filePath, base64Data, {
      encoding: FileSystem.EncodingType.Base64,
    });
    console.log('✅ File written successfully');

    // Verify file exists
    const fileInfo = await FileSystem.getInfoAsync(filePath);
    console.log('📋 File info:', fileInfo);

    // Check if sharing is available
    const isAvailable = await Sharing.isAvailableAsync();
    console.log('📤 Sharing available:', isAvailable);

    if (isAvailable) {
      // Automatically open share dialog for better UX
      Alert.alert(
        'Export Complete',
        `File created successfully! (${fileInfo.size} bytes)\n\nTap "Save to Device" to save the file to your phone.`,
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Save to Device',
            style: 'default',
            onPress: () => shareFile(filePath, mimeType),
          },
        ]
      );
    } else {
      Alert.alert(
        'Export Complete',
        `File saved successfully!\nSize: ${fileInfo.size} bytes\n\nNote: File is saved in app documents folder.`
      );
    }

  } catch (error) {
    console.error('💥 Download error:', error);
    Alert.alert('Export Failed', `Unable to save file: ${error.message}`);
  }
};

/**
 * Share file using Expo Sharing API
 */
export const shareFile = async (filePath: string, mimeType: string): Promise<void> => {
  try {
    await Sharing.shareAsync(filePath, {
      mimeType,
      dialogTitle: 'Share Budget Report',
    });
  } catch (error) {
    console.error('Share error:', error);
    Alert.alert('Share Failed', 'Unable to share file');
  }
};

/**
 * Convert Blob to base64 string
 */
const blobToBase64 = (blob: Blob): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onloadend = () => {
      if (typeof reader.result === 'string') {
        // Remove data URL prefix (data:application/pdf;base64,)
        const base64 = reader.result.split(',')[1];
        resolve(base64);
      } else {
        reject(new Error('Failed to convert blob to base64'));
      }
    };
    reader.onerror = reject;
    reader.readAsDataURL(blob);
  });
};

/**
 * Get MIME type for file format
 */
export const getMimeType = (format: 'csv' | 'pdf'): string => {
  switch (format) {
    case 'csv':
      return 'text/csv';
    case 'pdf':
      return 'application/pdf';
    default:
      return 'application/octet-stream';
  }
};
