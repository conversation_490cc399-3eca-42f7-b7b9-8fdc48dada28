import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
  TouchableOpacity,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { StatusBar } from 'expo-status-bar';
import { Checkbox } from 'react-native-paper';
import { AuthStackParamList } from '../navigation/types';
import theme from '../theme';
import Screen from '../components/common/Screen';
import Button from '../components/ui/Button';
import TextInput from '../components/ui/TextInput';
import { AuthAPI, ResetPasswordDto } from '../../src/api/auth.api';

type ResetPasswordScreenNavigationProp = NativeStackNavigationProp<
  AuthStackParamList,
  'ResetPassword'
>;

const ResetPasswordScreen = () => {
  const navigation = useNavigation<ResetPasswordScreenNavigationProp>();
  const route = useRoute();
  const { email, otp } = (route.params as any) || {};

  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [isNewPasswordVisible, setIsNewPasswordVisible] = useState(false);
  const [isConfirmPasswordVisible, setIsConfirmPasswordVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [focusedInput, setFocusedInput] = useState<'new' | 'confirm' | null>(null);
  const [passwordError, setPasswordError] = useState('');
  const [confirmError, setConfirmError] = useState('');
  const [rememberPassword, setRememberPassword] = useState(false);

  const validateNewPassword = (password: string) => {
    if (!password) {
      setPasswordError('');
      return;
    }
    if (password.length < 6) {
      setPasswordError('Password must be at least 6 characters');
    } else if (password.length > 30) {
      setPasswordError('Password must be at most 30 characters');
    } else {
      setPasswordError('');
    }
  };

  const validateConfirmPassword = (confirm: string) => {
    if (!confirm) {
      setConfirmError('');
      return;
    }
    if (confirm !== newPassword) {
      setConfirmError('Passwords do not match');
    } else {
      setConfirmError('');
    }
  };

  const validatePasswords = () => {
    if (!newPassword.trim()) {
      Alert.alert('Error', 'Please enter a new password');
      return false;
    }

    if (newPassword.length < 6) {
      Alert.alert('Error', 'Password must be at least 6 characters');
      return false;
    }

    if (newPassword.length > 30) {
      Alert.alert('Error', 'Password must be at most 30 characters');
      return false;
    }

    if (!confirmPassword.trim()) {
      Alert.alert('Error', 'Please confirm your password');
      return false;
    }

    if (newPassword !== confirmPassword) {
      Alert.alert('Error', 'Passwords do not match');
      return false;
    }

    return true;
  };

  const handleResetPassword = async () => {
    if (!validatePasswords()) {
      return;
    }

    try {
      setLoading(true);
      const resetData: ResetPasswordDto = {
        email,
        otp,
        newPassword,
      };

      await AuthAPI.resetPassword(resetData);

      Alert.alert(
        'Success',
        'Password has been reset successfully!',
        [
          {
            text: 'OK',
            onPress: () => navigation.navigate('Login'),
          },
        ]
      );
    } catch (error: any) {
      let errorMessage = 'Failed to reset password. Please try again.';

      if (error.response?.status === 400) {
        errorMessage = error.response?.data?.message || 'Invalid request. Please check your information.';
      } else if (error.response?.status === 401) {
        errorMessage = 'OTP has expired or is invalid. Please request a new one.';
      } else if (error.response?.status >= 500) {
        errorMessage = 'Server error. Please try again later.';
      } else if (error.message) {
        errorMessage = error.message;
      }

      Alert.alert('Error', errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Screen>
      <StatusBar style="dark" />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.headerContainer}>
            <Text style={styles.title}>Reset Password</Text>
            <Text style={styles.subtitle}>
              Enter your new password below
            </Text>
          </View>

          <View style={styles.formContainer}>
            <View style={styles.inputGroup}>
              <Text style={styles.label}>New Password</Text>
              <View style={[
                styles.passwordContainer,
                focusedInput === 'new' && styles.passwordContainerFocused,
                passwordError && styles.passwordContainerError
              ]}>
                <View style={styles.inputWrapper}>
                  <TextInput
                    style={styles.passwordInput}
                    placeholder="Enter new password"
                    value={newPassword}
                    onChangeText={(text) => {
                      setNewPassword(text);
                      validateNewPassword(text);
                      if (confirmPassword) {
                        validateConfirmPassword(confirmPassword);
                      }
                    }}
                    secureTextEntry={!isNewPasswordVisible}
                    autoCapitalize="none"
                    onFocus={() => setFocusedInput('new')}
                    onBlur={() => setFocusedInput(null)}
                  />
                </View>
                <View style={styles.iconWrapper}>
                  <TouchableOpacity
                    onPress={() => setIsNewPasswordVisible(!isNewPasswordVisible)}
                    style={styles.visibilityToggle}
                  >
                    <Text style={styles.eyeIcon}>{isNewPasswordVisible ? '👁' : '👁️‍🗨️'}</Text>
                  </TouchableOpacity>
                </View>
              </View>
              {passwordError ? (
                <Text style={styles.errorText}>{passwordError}</Text>
              ) : null}
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Confirm New Password</Text>
              <View style={[
                styles.passwordContainer,
                focusedInput === 'confirm' && styles.passwordContainerFocused,
                confirmError && styles.passwordContainerError
              ]}>
                <View style={styles.inputWrapper}>
                  <TextInput
                    style={styles.passwordInput}
                    placeholder="Confirm new password"
                    value={confirmPassword}
                    onChangeText={(text) => {
                      setConfirmPassword(text);
                      validateConfirmPassword(text);
                    }}
                    secureTextEntry={!isConfirmPasswordVisible}
                    autoCapitalize="none"
                    onFocus={() => setFocusedInput('confirm')}
                    onBlur={() => setFocusedInput(null)}
                  />
                </View>
                <View style={styles.iconWrapper}>
                  <TouchableOpacity
                    onPress={() => setIsConfirmPasswordVisible(!isConfirmPasswordVisible)}
                    style={styles.visibilityToggle}
                  >
                    <Text style={styles.eyeIcon}>{isConfirmPasswordVisible ? '👁' : '👁️‍🗨️'}</Text>
                  </TouchableOpacity>
                </View>
              </View>
              {confirmError ? (
                <Text style={styles.errorText}>{confirmError}</Text>
              ) : null}
            </View>

            <View style={styles.rememberContainer}>
              <Checkbox
                status={rememberPassword ? 'checked' : 'unchecked'}
                onPress={() => setRememberPassword(!rememberPassword)}
                color={theme.colors.primary[500]}
              />
            </View>

            <Button
              title="Reset Password"
              onPress={handleResetPassword}
              variant="primary"
              loading={loading}
              disabled={loading || !!passwordError || !!confirmError || !newPassword || !confirmPassword}
              style={styles.resetButton}
            />
          </View>

          <View style={styles.backToLoginContainer}>
            <Text style={styles.backToLoginText}>Remember your password? </Text>
            <TouchableOpacity onPress={() => navigation.navigate('Login')}>
              <Text style={styles.backToLoginLink}>Back to Login</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </Screen>
  );
};

const styles = StyleSheet.create({
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.xl,
    backgroundColor: theme.colors.background.secondary,
  },
  headerContainer: {
    alignItems: 'center',
    marginBottom: theme.spacing.xl,
  },
  title: {
    fontSize: 26,
    fontWeight: '700',
    color: theme.colors.primary[600],
    marginBottom: theme.spacing.xs,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: theme.colors.text.secondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  formContainer: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: theme.spacing.lg,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  inputGroup: {
    marginBottom: theme.spacing.lg,
  },
  label: {
    fontSize: 15,
    fontWeight: '600',
    color: theme.colors.text.primary,
    marginBottom: 6,
  },
  passwordContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1.5,
    borderColor: theme.colors.gray[200],
    borderRadius: 12,
    backgroundColor: '#F9FAFB',
    height: 48,
    paddingHorizontal: theme.spacing.sm,
  },
  passwordContainerFocused: {
    borderColor: theme.colors.primary[500],
    backgroundColor: '#fff',
    elevation: 2,
  },
  passwordContainerError: {
    borderColor: theme.colors.error[400],
  },
  inputWrapper: {
    flex: 1,
    height: '100%',
    justifyContent: 'center',
  },
  passwordInput: {
    fontSize: 15,
    color: theme.colors.text.primary,
    padding: 0,
  },
  iconWrapper: {
    paddingHorizontal: theme.spacing.sm,
    justifyContent: 'center',
    alignItems: 'center',
  },
  visibilityToggle: {
    padding: 4,
  },
  eyeIcon: {
    fontSize: 20,
    color: theme.colors.gray[500],
  },
  errorText: {
    fontSize: 13,
    color: theme.colors.error[500],
    marginTop: 4,
    marginLeft: 4,
  },
  rememberContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.lg,
  },
  rememberText: {
    fontSize: 14,
    color: theme.colors.text.secondary,
  },
  resetButton: {
    marginTop: theme.spacing.md,
    borderRadius: 12,
  },
  backToLoginContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: theme.spacing.lg,
  },
  backToLoginText: {
    fontSize: 14,
    color: theme.colors.text.secondary,
  },
  backToLoginLink: {
    fontSize: 14,
    color: theme.colors.primary[600],
    fontWeight: '600',
  },
});


export default ResetPasswordScreen;
