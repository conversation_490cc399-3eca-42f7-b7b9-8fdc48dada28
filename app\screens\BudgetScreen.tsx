import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  Alert,
  RefreshControl,
} from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation/types';
import theme from '../theme';
import Screen from '../components/common/Screen';
import DrawerHeader from '../components/common/DrawerHeader';
import Button from '../components/ui/Button';
import Card from '../components/ui/Card';
import { BudgetAPI, BudgetOverviewDto } from '../../src/api/budget.api';
import { StorageService } from '../services/storage.service';

type BudgetScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;

const BudgetScreen = () => {
  const navigation = useNavigation<BudgetScreenNavigationProp>();

  // State management
  const [budgetOverview, setBudgetOverview] = useState<BudgetOverviewDto[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());

  // Calculate totals from budget overview (with safe guard)
  const safebudgetOverview = budgetOverview || [];
  const totalBudget = Array.isArray(safebudgetOverview) ? safebudgetOverview.reduce((sum, budget) => sum + (budget?.budgetAmount || 0), 0) : 0;
  const totalSpent = Array.isArray(safebudgetOverview) ? safebudgetOverview.reduce((sum, budget) => sum + (budget?.spentAmount || 0), 0) : 0;
  const totalRemaining = totalBudget - totalSpent;

  // Load budget overview data
  const loadBudgetData = async () => {
    try {
      setLoading(true);

      // Check authentication
      const token = await StorageService.getAccessToken();
      if (!token) {
        console.error('No access token found');
        Alert.alert('Error', 'Please login again');
        return;
      }

      // Test debug endpoint first
      try {
        await BudgetAPI.debugRoutes();
      } catch (debugError) {
        console.error('Budget debug endpoint failed:', debugError);
      }

      const overview = await BudgetAPI.getOverview(selectedMonth, selectedYear);

      // Ensure overview is an array
      if (Array.isArray(overview)) {
        setBudgetOverview(overview);
      } else {
        console.warn('Budget overview is not an array:', overview);
        setBudgetOverview([]);
      }
    } catch (error: any) {
      console.error('Budget API Error:', error);

      let errorMessage = 'Failed to load budget data. Please try again.';

      if (error.response?.status === 401) {
        errorMessage = 'Authentication failed. Please login again.';
      } else if (error.response?.status >= 500) {
        errorMessage = 'Server error. Please try again later.';
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      Alert.alert('Error', errorMessage);
      setBudgetOverview([]); // Set empty array on error
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Refresh data
  const handleRefresh = () => {
    setRefreshing(true);
    loadBudgetData();
  };

  // Load data on component mount and when screen is focused
  useEffect(() => {
    loadBudgetData();
  }, [selectedMonth, selectedYear]);

  useFocusEffect(
    React.useCallback(() => {
      loadBudgetData();
    }, [selectedMonth, selectedYear])
  );

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const calculateProgress = (spent: number, total: number) => {
    if (total === 0) return 0;
    const progress = (spent / total) * 100;
    return Math.min(progress, 100); // Cap at 100%
  };

  const getMonthYearText = () => {
    const monthNames = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return `${monthNames[selectedMonth - 1]} ${selectedYear}`;
  };

  const renderBudgetItem = ({ item }: { item: BudgetOverviewDto }) => {
    const isOverBudget = item.progressPercent > 100;

    return (
      <TouchableOpacity
        style={styles.budgetCard}
        onPress={() => navigation.navigate('BudgetDetail', {
          id: item.categoryId,
          month: selectedMonth,
          year: selectedYear
        })}
      >
        <View style={styles.budgetHeader}>
          <View style={styles.budgetIconContainer}>
            <Text style={styles.budgetIcon}>💰</Text>
          </View>
          <View style={styles.budgetTitleContainer}>
            <Text style={styles.budgetCategory}>{item.categoryName}</Text>
            <Text style={styles.budgetPeriod}>{getMonthYearText()}</Text>
          </View>
          {item.warning && (
            <View style={styles.warningBadge}>
              <Text style={styles.warningText}>⚠️ Warning</Text>
            </View>
          )}
          {isOverBudget && (
            <View style={styles.overBudgetBadge}>
              <Text style={styles.overBudgetText}>Over Budget</Text>
            </View>
          )}
        </View>

        <View style={styles.progressContainer}>
          <View
            style={[
              styles.progressBar,
              { width: `${Math.min(item.progressPercent, 100)}%` },
              isOverBudget && styles.overBudgetBar,
              item.warning && !isOverBudget && styles.warningBar,
            ]}
          />
        </View>

        <View style={styles.budgetDetails}>
          <View>
            <Text style={styles.detailLabel}>Spent</Text>
            <Text style={[
              styles.spentAmount,
              isOverBudget && styles.overBudgetText
            ]}>
              {formatCurrency(item.spentAmount)}
            </Text>
          </View>
          <View style={{ alignItems: 'center' }}>
            <Text style={styles.detailLabel}>Remaining</Text>
            <Text style={item.remainingAmount < 0 ? styles.overBudgetText : styles.remainingAmount}>
              {formatCurrency(Math.abs(item.remainingAmount))}
              {item.remainingAmount < 0 ? ' over' : ''}
            </Text>
          </View>
          <View style={{ alignItems: 'flex-end' }}>
            <Text style={styles.detailLabel}>Budget</Text>
            <Text style={styles.totalBudgetAmount}>{formatCurrency(item.budgetAmount)}</Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  // Show loading state
  if (loading && safebudgetOverview.length === 0) {
    return (
      <Screen>
        <DrawerHeader title="Budget" />
        <View style={[styles.container, { justifyContent: 'center', alignItems: 'center' }]}>
          <ActivityIndicator size="large" color={theme.colors.primary[500]} />
          <Text style={{ marginTop: theme.spacing.md, color: theme.colors.text.secondary }}>
            Loading budget data...
          </Text>
        </View>
      </Screen>
    );
  }

  return (
    <Screen>
      <DrawerHeader title="Budget" />
      <ScrollView
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
      >
        <View style={styles.header}>
          <Text style={styles.title}>Budget Management</Text>
          <Text style={styles.subtitle}>Track your spending within planned limits</Text>
        </View>

        {/* Month/Year Selector */}
        <View style={styles.periodSelector}>
          <TouchableOpacity
            style={styles.periodButton}
            onPress={() => {
              if (selectedMonth > 1) {
                setSelectedMonth(selectedMonth - 1);
              } else {
                setSelectedMonth(12);
                setSelectedYear(selectedYear - 1);
              }
            }}
          >
            <Text style={styles.periodButtonText}>←</Text>
          </TouchableOpacity>

          <Text style={styles.periodText}>{getMonthYearText()}</Text>

          <TouchableOpacity
            style={styles.periodButton}
            onPress={() => {
              if (selectedMonth < 12) {
                setSelectedMonth(selectedMonth + 1);
              } else {
                setSelectedMonth(1);
                setSelectedYear(selectedYear + 1);
              }
            }}
          >
            <Text style={styles.periodButtonText}>→</Text>
          </TouchableOpacity>
        </View>

        {/* Overall Budget Summary */}
        {totalBudget > 0 && (
          <Card style={styles.summaryCard}>
            <View style={styles.summaryHeader}>
              <Text style={styles.summaryTitle}>Total Budget - {getMonthYearText()}</Text>
              <Text style={styles.summaryAmount}>{formatCurrency(totalBudget)}</Text>
            </View>
            <View style={styles.summaryProgressContainer}>
              <View
                style={[
                  styles.summaryProgressBar,
                  { width: `${calculateProgress(totalSpent, totalBudget)}%` },
                  totalSpent > totalBudget && styles.overBudgetBar,
                ]}
              />
            </View>
            <View style={styles.summaryDetails}>
              <View>
                <Text style={styles.detailLabel}>Spent</Text>
                <Text style={styles.spentAmount}>{formatCurrency(totalSpent)}</Text>
              </View>
              <View style={{ alignItems: 'flex-end' }}>
                <Text style={styles.detailLabel}>Remaining</Text>
                <Text
                  style={totalRemaining < 0 ? styles.overBudgetText : styles.remainingAmount}
                >
                  {formatCurrency(Math.abs(totalRemaining))}
                  {totalRemaining < 0 ? ' over' : ''}
                </Text>
              </View>
            </View>
          </Card>
        )}

        {/* Budget Categories */}
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Budget by Category</Text>
          <TouchableOpacity onPress={() => navigation.navigate('BudgetReport', { month: selectedMonth, year: selectedYear })}>
            <Text style={styles.viewReportText}>View Report</Text>
          </TouchableOpacity>
        </View>

        {safebudgetOverview.length > 0 ? (
          <FlatList
            data={safebudgetOverview}
            renderItem={renderBudgetItem}
            keyExtractor={(item) => item.categoryId}
            contentContainerStyle={styles.listContainer}
            scrollEnabled={false} // Let the parent ScrollView handle scrolling
          />
        ) : (
          <View style={styles.emptyState}>
            <Text style={styles.emptyStateText}>No budgets set for {getMonthYearText()}</Text>
            <Text style={styles.emptyStateSubtext}>Create your first budget to start tracking expenses</Text>
          </View>
        )}

        {/* Add Budget Button */}
        <Button
          title="+ Create New Budget"
          onPress={() => navigation.navigate('CreateBudget')}
          variant="primary"
          style={styles.addButton}
        />
      </ScrollView>
    </Screen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: theme.spacing.md,
    paddingTop: theme.spacing.md,
    paddingBottom: theme.spacing.md,
  },
  title: {
    fontSize: theme.typography.fontSizes['2xl'],
    fontWeight: '700',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.xs,
  },
  subtitle: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.secondary,
  },
  periodSelector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginHorizontal: theme.spacing.md,
    marginBottom: theme.spacing.lg,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    backgroundColor: theme.colors.background.primary,
    borderRadius: theme.borderRadius.lg,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  periodButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.primary[100],
    justifyContent: 'center',
    alignItems: 'center',
  },
  periodButtonText: {
    fontSize: theme.typography.fontSizes.lg,
    fontWeight: '600',
    color: theme.colors.primary[600],
  },
  periodText: {
    fontSize: theme.typography.fontSizes.lg,
    fontWeight: '600',
    color: theme.colors.text.primary,
  },
  summaryCard: {
    marginHorizontal: theme.spacing.md,
    marginBottom: theme.spacing.xl,
  },
  summaryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  summaryTitle: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '600',
    color: theme.colors.text.primary,
  },
  summaryAmount: {
    fontSize: theme.typography.fontSizes.xl,
    fontWeight: '700',
    color: theme.colors.text.primary,
  },
  summaryProgressContainer: {
    height: 8,
    backgroundColor: theme.colors.gray[100],
    borderRadius: theme.borderRadius.full,
    marginBottom: theme.spacing.md,
    overflow: 'hidden',
  },
  summaryProgressBar: {
    height: '100%',
    backgroundColor: theme.colors.primary[500],
  },
  summaryDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  detailLabel: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
    marginBottom: theme.spacing.xs / 2,
  },
  spentAmount: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '600',
    color: theme.colors.primary[600],
  },
  remainingAmount: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '600',
    color: theme.colors.success,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.md,
    marginBottom: theme.spacing.md,
  },
  sectionTitle: {
    fontSize: theme.typography.fontSizes.lg,
    fontWeight: '600',
    color: theme.colors.text.primary,
  },
  viewReportText: {
    fontSize: theme.typography.fontSizes.sm,
    fontWeight: '500',
    color: theme.colors.primary[600],
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: theme.spacing.xl * 2,
    paddingHorizontal: theme.spacing.md,
  },
  emptyStateText: {
    fontSize: theme.typography.fontSizes.lg,
    fontWeight: '600',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.sm,
    textAlign: 'center',
  },
  emptyStateSubtext: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.secondary,
    textAlign: 'center',
  },
  listContainer: {
    paddingHorizontal: theme.spacing.md,
  },
  budgetCard: {
    backgroundColor: theme.colors.background.primary,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.md,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
  },
  budgetHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  budgetIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: theme.colors.gray[100],
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: theme.spacing.sm,
  },
  budgetIcon: {
    fontSize: theme.typography.fontSizes.xl,
  },
  budgetTitleContainer: {
    flex: 1,
  },
  budgetCategory: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '600',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.xs / 2,
  },
  budgetPeriod: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
  },
  overBudgetBadge: {
    backgroundColor: theme.colors.error,
    paddingVertical: theme.spacing.xs / 2,
    paddingHorizontal: theme.spacing.sm,
    borderRadius: theme.borderRadius.full,
  },
  overBudgetText: {
    fontSize: theme.typography.fontSizes.sm,
    fontWeight: '500',
    color: theme.colors.background.primary,
  },
  warningBadge: {
    backgroundColor: '#FFA500',
    paddingVertical: theme.spacing.xs / 2,
    paddingHorizontal: theme.spacing.sm,
    borderRadius: theme.borderRadius.full,
    marginRight: theme.spacing.xs,
  },
  warningText: {
    fontSize: theme.typography.fontSizes.xs,
    fontWeight: '500',
    color: theme.colors.background.primary,
  },
  progressContainer: {
    height: 8,
    backgroundColor: theme.colors.gray[100],
    borderRadius: theme.borderRadius.full,
    marginBottom: theme.spacing.md,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    backgroundColor: theme.colors.primary[500],
  },
  overBudgetBar: {
    backgroundColor: theme.colors.error,
  },
  warningBar: {
    backgroundColor: '#FFA500',
  },
  budgetDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  totalBudgetAmount: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '600',
    color: theme.colors.text.primary,
  },
  addButton: {
    margin: theme.spacing.md,
    marginBottom: theme.spacing.xl,
  },
});

export default BudgetScreen;