import { Platform } from 'react-native';

// Get the appropriate base URL based on the platform and environment
const getBaseUrl = () => {
    if (__DEV__) {
        // For Expo Go on physical devices, use the LAN IP
        const LAN_IP = '*************';

        if (Platform.OS === 'android') {
            // Với thiết bị Android thật, dùng IP của máy tính
            return `http://${LAN_IP}:8000/api/v1`;
        }
        // Với iOS simulator hoặc thiết bị iOS, dùng localhost
        return `http://${LAN_IP}:8000/api/v1`;
    }
    // Production URL - Deployed backend
    return 'https://vuquangduy.online/api/v1';
};

export const API_CONFIG = {
    BASE_URL: getBaseUrl(),
    TEMP_USER_ID: '123e4567-e89b-12d3-a456-426614174000',
    HEADERS: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
    },
};