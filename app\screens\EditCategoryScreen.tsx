import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useNavigation, useRoute } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation/types';
import theme from '../theme';
import Screen from '../components/common/Screen';
import Button from '../components/ui/Button';
import TextInput from '../components/ui/TextInput';
import Card from '../components/ui/Card';
import { CategoryAPI, CategoryType, UpdateCategoryDto, ICategory } from '../../src/api/category.api';

type EditCategoryScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'EditCategory'
>;

type EditCategoryScreenRouteProp = {
  params: {
    categoryId: string;
  };
};

// Available category icons
const categoryIcons = [
  { id: 'food', icon: '🍲', name: 'Food & Dining' },
  { id: 'transport', icon: '🚕', name: 'Transportation' },
  { id: 'shopping', icon: '🛒', name: 'Shopping' },
  { id: 'entertainment', icon: '🎬', name: 'Entertainment' },
  { id: 'housing', icon: '🏠', name: 'Housing' },
  { id: 'utilities', icon: '💡', name: 'Utilities' },
  { id: 'health', icon: '💊', name: 'Healthcare' },
  { id: 'education', icon: '📚', name: 'Education' },
  { id: 'personal', icon: '👤', name: 'Personal Care' },
  { id: 'pets', icon: '🐾', name: 'Pets' },
  { id: 'gifts', icon: '🎁', name: 'Gifts' },
  { id: 'income', icon: '💰', name: 'Income' },
  { id: 'savings', icon: '💵', name: 'Savings' },
  { id: 'investment', icon: '📈', name: 'Investment' },
  { id: 'other', icon: '✨', name: 'Other' },
];

// Available category colors
const categoryColors = [
  '#E0F2FE', // Light blue
  '#FEF3C7', // Light yellow
  '#D1FAE5', // Light green
  '#EDE9FE', // Light purple
  '#FCE7F3', // Light pink
  '#FEE2E2', // Light red
  '#DBEAFE', // Sky blue
  '#E8E8E8', // Light gray
];

const EditCategoryScreen = () => {
  const navigation = useNavigation<EditCategoryScreenNavigationProp>();
  const route = useRoute<EditCategoryScreenRouteProp>();
  const { categoryId } = route.params;

  const [category, setCategory] = useState<ICategory | null>(null);
  const [categoryType, setCategoryType] = useState<'expense' | 'income'>('expense');
  const [name, setName] = useState('');
  const [selectedIconId, setSelectedIconId] = useState<string | null>(null);
  const [selectedColorIndex, setSelectedColorIndex] = useState<number | null>(0);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  // Load category data
  useEffect(() => {
    loadCategory();
  }, [categoryId]);

  const loadCategory = async () => {
    try {
      setLoading(true);
      const response: any = await CategoryAPI.getOne(categoryId);
      const categoryData = response.data ? response.data : response;

      setCategory(categoryData);
      setName(categoryData.name);
      setCategoryType(categoryData.type === 'expense' ? 'expense' : 'income');

      // Set default icon and color if not found
      setSelectedIconId('other');
      setSelectedColorIndex(0);
    } catch (error: any) {
      console.error('Error loading category:', error);
      Alert.alert('Error', 'Failed to load category details. Please try again.');
      navigation.goBack();
    } finally {
      setLoading(false);
    }
  };

  const handleSaveCategory = async () => {
    if (!name.trim()) {
      Alert.alert('Error', 'Please enter category name');
      return;
    }

    if (!selectedIconId) {
      Alert.alert('Error', 'Please select an icon for the category');
      return;
    }

    if (selectedColorIndex === null) {
      Alert.alert('Error', 'Please select a color for the category');
      return;
    }

    setSaving(true);
    try {
      const updateData: UpdateCategoryDto = {
        name: name.trim(),
        type: categoryType === 'expense' ? CategoryType.EXPENSE : CategoryType.INCOME,
      };

      await CategoryAPI.update(categoryId, updateData);

      Alert.alert(
        'Success',
        'Category updated successfully',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (error: any) {
      console.error('Error updating category:', error);
      Alert.alert(
        'Error',
        error.response?.data?.message || 'Failed to update category. Please try again.'
      );
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <Screen>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading category...</Text>
        </View>
      </Screen>
    );
  }

  if (!category) {
    return (
      <Screen>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Category not found</Text>
          <Button
            title="Go Back"
            onPress={() => navigation.goBack()}
            variant="primary"
          />
        </View>
      </Screen>
    );
  }

  return (
    <Screen>
      <KeyboardAwareScrollView
        style={styles.container}
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
        enableOnAndroid={true}
        enableAutomaticScroll={true}
        extraScrollHeight={20}
        extraHeight={100}
      >
          <Text style={styles.headerTitle}>Edit Category</Text>

          {/* Category Type Selector */}
          <View style={styles.typeSelector}>
            <TouchableOpacity
              style={[
                styles.typeButton,
                categoryType === 'expense' && styles.activeTypeButton,
              ]}
              onPress={() => setCategoryType('expense')}
            >
              <Text
                style={[
                  styles.typeButtonText,
                  categoryType === 'expense' && styles.activeTypeButtonText,
                ]}
              >
                Expense
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.typeButton,
                categoryType === 'income' && styles.activeTypeButton,
              ]}
              onPress={() => setCategoryType('income')}
            >
              <Text
                style={[
                  styles.typeButtonText,
                  categoryType === 'income' && styles.activeTypeButtonText,
                ]}
              >
                Income
              </Text>
            </TouchableOpacity>
          </View>

          {/* Name Input */}
          <View style={styles.inputSection}>
            <TextInput
              label="Category Name"
              placeholder="Enter category name"
              value={name}
              onChangeText={setName}
            />
          </View>

          {/* Icon Selection */}
          <View style={styles.inputSection}>
            <Text style={styles.inputLabel}>Select Icon</Text>
            <View style={styles.iconsContainer}>
              {categoryIcons.map((item) => (
                <TouchableOpacity
                  key={item.id}
                  style={[
                    styles.iconItem,
                    selectedIconId === item.id && styles.selectedIconItem,
                  ]}
                  onPress={() => setSelectedIconId(item.id)}
                >
                  <Text style={styles.iconText}>{item.icon}</Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Color Selection */}
          <View style={styles.inputSection}>
            <Text style={styles.inputLabel}>Select Color</Text>
            <View style={styles.colorsContainer}>
              {categoryColors.map((color, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.colorItem,
                    { backgroundColor: color },
                    selectedColorIndex === index && styles.selectedColorItem,
                  ]}
                  onPress={() => setSelectedColorIndex(index)}
                />
              ))}
            </View>
          </View>

          {/* Preview */}
          <View style={styles.previewSection}>
            <Text style={styles.previewTitle}>Preview</Text>
            <Card
              style={[
                styles.previewBox,
                { backgroundColor: selectedColorIndex !== null ? categoryColors[selectedColorIndex] : theme.colors.gray[100] }
              ]}
            >
              <Text style={styles.previewIcon}>
                {selectedIconId ? categoryIcons.find(i => i.id === selectedIconId)?.icon : '?'}
              </Text>
              <Text style={styles.previewName}>{name || 'Category Name'}</Text>
            </Card>
          </View>

          {/* Save Button */}
          <Button
            title="Update Category"
            onPress={handleSaveCategory}
            variant="primary"
            fullWidth
            loading={saving}
            disabled={saving || !name || !selectedIconId || selectedColorIndex === null}
          />
      </KeyboardAwareScrollView>
    </Screen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.xl,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: theme.spacing.xl,
  },
  loadingText: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.secondary,
    marginTop: theme.spacing.md,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: theme.spacing.xl,
  },
  errorText: {
    fontSize: theme.typography.fontSizes.lg,
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.xl,
    textAlign: 'center',
  },
  headerTitle: {
    fontSize: theme.typography.fontSizes['2xl'],
    fontWeight: '700',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.xl,
  },
  typeSelector: {
    flexDirection: 'row',
    backgroundColor: theme.colors.gray[100],
    borderRadius: theme.borderRadius.md,
    marginBottom: theme.spacing.xl,
  },
  typeButton: {
    flex: 1,
    paddingVertical: theme.spacing.sm,
    alignItems: 'center',
    borderRadius: theme.borderRadius.md,
  },
  activeTypeButton: {
    backgroundColor: theme.colors.primary[500],
  },
  typeButtonText: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '500',
    color: theme.colors.text.secondary,
  },
  activeTypeButtonText: {
    color: theme.colors.background.primary,
  },
  inputSection: {
    marginBottom: theme.spacing.xl,
  },
  inputLabel: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '500',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.sm,
  },
  iconsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -theme.spacing.sm,
  },
  iconItem: {
    width: 60,
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
    margin: theme.spacing.sm,
    borderRadius: theme.borderRadius.lg,
    backgroundColor: theme.colors.gray[100],
  },
  selectedIconItem: {
    backgroundColor: theme.colors.primary[100],
    borderWidth: 2,
    borderColor: theme.colors.primary[500],
  },
  iconText: {
    fontSize: theme.typography.fontSizes['2xl'],
  },
  colorsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -theme.spacing.sm,
  },
  colorItem: {
    width: 50,
    height: 50,
    borderRadius: 25,
    margin: theme.spacing.sm,
  },
  selectedColorItem: {
    borderWidth: 3,
    borderColor: theme.colors.primary[500],
  },
  previewSection: {
    marginBottom: theme.spacing['2xl'],
  },
  previewTitle: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '500',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.md,
  },
  previewBox: {
    padding: theme.spacing.xl,
    borderRadius: theme.borderRadius.lg,
    alignItems: 'center',
  },
  previewIcon: {
    fontSize: 40,
    marginBottom: theme.spacing.sm,
  },
  previewName: {
    fontSize: theme.typography.fontSizes.lg,
    fontWeight: '600',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.sm,
  },
});

export default EditCategoryScreen;
