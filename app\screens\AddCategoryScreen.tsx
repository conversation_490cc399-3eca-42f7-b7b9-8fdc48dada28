import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation/types';
import theme from '../theme';
import Screen from '../components/common/Screen';
import Button from '../components/ui/Button';
import TextInput from '../components/ui/TextInput';
import Card from '../components/ui/Card';
import { CategoryAPI, CategoryType, CreateCategoryDto } from '../../src/api/category.api';

type AddCategoryScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'AddCategory'
>;

// Available category icons
const categoryIcons = [
  { id: 'food', icon: '🍲', name: 'Food & Dining' },
  { id: 'transport', icon: '🚕', name: 'Transportation' },
  { id: 'shopping', icon: '🛒', name: 'Shopping' },
  { id: 'entertainment', icon: '🎬', name: 'Entertainment' },
  { id: 'housing', icon: '🏠', name: 'Housing' },
  { id: 'utilities', icon: '💡', name: 'Utilities' },
  { id: 'health', icon: '💊', name: 'Healthcare' },
  { id: 'education', icon: '📚', name: 'Education' },
  { id: 'personal', icon: '👤', name: 'Personal Care' },
  { id: 'pets', icon: '🐾', name: 'Pets' },
  { id: 'gifts', icon: '🎁', name: 'Gifts' },
  { id: 'income', icon: '💰', name: 'Income' },
  { id: 'savings', icon: '💵', name: 'Savings' },
  { id: 'investment', icon: '📈', name: 'Investment' },
  { id: 'other', icon: '✨', name: 'Other' },
];

// Available category colors
const categoryColors = [
  '#E0F2FE', // Light blue
  '#FEF3C7', // Light yellow
  '#D1FAE5', // Light green
  '#EDE9FE', // Light purple
  '#FCE7F3', // Light pink
  '#FEE2E2', // Light red
  '#DBEAFE', // Sky blue
  '#E8E8E8', // Light gray
];

const AddCategoryScreen = () => {
  const navigation = useNavigation<AddCategoryScreenNavigationProp>();

  const [categoryType, setCategoryType] = useState<'expense' | 'income'>('expense');
  const [name, setName] = useState('');
  const [selectedIconId, setSelectedIconId] = useState<string | null>(null);
  const [selectedColorIndex, setSelectedColorIndex] = useState<number | null>(0);
  const [initialBudget, setInitialBudget] = useState('');
  const [loading, setLoading] = useState(false);

  // Format amount with commas
  const formatAmount = (text: string) => {
    // Remove non-numeric characters
    const numericValue = text.replace(/[^0-9]/g, '');

    // Format with commas
    if (numericValue) {
      const formattedValue = parseInt(numericValue, 10).toLocaleString('vi-VN');
      setInitialBudget(formattedValue);
    } else {
      setInitialBudget('');
    }
  };

  const handleSaveCategory = async () => {
    if (!name.trim()) {
      Alert.alert('Error', 'Please enter category name');
      return;
    }

    if (!selectedIconId) {
      Alert.alert('Error', 'Please select an icon for the category');
      return;
    }

    if (selectedColorIndex === null) {
      Alert.alert('Error', 'Please select a color for the category');
      return;
    }

    setLoading(true);
    try {
      const createData: CreateCategoryDto = {
        name: name.trim(),
        type: categoryType === 'expense' ? CategoryType.EXPENSE : CategoryType.INCOME,
      };

      await CategoryAPI.create(createData);

      Alert.alert(
        'Success',
        'Category created successfully',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (error: any) {
      console.error('Error creating category:', error);
      Alert.alert(
        'Error',
        error.response?.data?.message || 'Failed to create category. Please try again.'
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <Screen>
      <KeyboardAwareScrollView
        style={styles.container}
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
        enableOnAndroid={true}
        enableAutomaticScroll={true}
        extraScrollHeight={20}
        extraHeight={100}
      >
          <Text style={styles.headerTitle}>Add New Category</Text>

          {/* Category Type Selector */}
          <View style={styles.typeSelector}>
            <TouchableOpacity
              style={[
                styles.typeButton,
                categoryType === 'expense' && styles.activeTypeButton,
              ]}
              onPress={() => setCategoryType('expense')}
            >
              <Text
                style={[
                  styles.typeButtonText,
                  categoryType === 'expense' && styles.activeTypeButtonText,
                ]}
              >
                Expense
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.typeButton,
                categoryType === 'income' && styles.activeTypeButton,
              ]}
              onPress={() => setCategoryType('income')}
            >
              <Text
                style={[
                  styles.typeButtonText,
                  categoryType === 'income' && styles.activeTypeButtonText,
                ]}
              >
                Income
              </Text>
            </TouchableOpacity>
          </View>

          {/* Name Input */}
          <View style={styles.inputSection}>
            <TextInput
              label="Category Name"
              placeholder="Enter category name"
              value={name}
              onChangeText={setName}
            />
          </View>

          {/* Icon Selection */}
          <View style={styles.inputSection}>
            <Text style={styles.inputLabel}>Select Icon</Text>
            <View style={styles.iconsContainer}>
              {categoryIcons.map((item) => (
                <TouchableOpacity
                  key={item.id}
                  style={[
                    styles.iconItem,
                    selectedIconId === item.id && styles.selectedIconItem,
                  ]}
                  onPress={() => setSelectedIconId(item.id)}
                >
                  <Text style={styles.iconText}>{item.icon}</Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Color Selection */}
          <View style={styles.inputSection}>
            <Text style={styles.inputLabel}>Select Color</Text>
            <View style={styles.colorsContainer}>
              {categoryColors.map((color, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.colorItem,
                    { backgroundColor: color },
                    selectedColorIndex === index && styles.selectedColorItem,
                  ]}
                  onPress={() => setSelectedColorIndex(index)}
                />
              ))}
            </View>
          </View>

          {/* Initial Budget (for expense categories) */}
          {categoryType === 'expense' && (
            <View style={styles.inputSection}>
              <Text style={styles.inputLabel}>Initial Budget (Optional)</Text>
              <View style={styles.amountInputContainer}>
                <Text style={styles.currencySymbol}>₫</Text>
                <TextInput
                  placeholder="1,000,000"
                  keyboardType="numeric"
                  value={initialBudget}
                  onChangeText={formatAmount}
                  containerStyle={styles.budgetInputContainer}
                  inputStyle={styles.amountInput}
                />
              </View>
            </View>
          )}

          {/* Preview */}
          <View style={styles.previewSection}>
            <Text style={styles.previewTitle}>Preview</Text>
            <Card
              style={[
                styles.previewBox,
                { backgroundColor: selectedColorIndex !== null ? categoryColors[selectedColorIndex] : theme.colors.gray[100] }
              ]}
            >
              <Text style={styles.previewIcon}>
                {selectedIconId ? categoryIcons.find(i => i.id === selectedIconId)?.icon : '?'}
              </Text>
              <Text style={styles.previewName}>{name || 'Category Name'}</Text>
              {categoryType === 'expense' && initialBudget && (
                <Text style={styles.previewBudget}>₫ {initialBudget}</Text>
              )}
            </Card>
          </View>

          {/* Save Button */}
          <Button
            title="Save Category"
            onPress={handleSaveCategory}
            variant="primary"
            fullWidth
            loading={loading}
            disabled={loading || !name || !selectedIconId || selectedColorIndex === null}
          />
      </KeyboardAwareScrollView>
    </Screen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.xl,
  },
  headerTitle: {
    fontSize: theme.typography.fontSizes['2xl'],
    fontWeight: '700',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.xl,
  },
  typeSelector: {
    flexDirection: 'row',
    backgroundColor: theme.colors.gray[100],
    borderRadius: theme.borderRadius.md,
    marginBottom: theme.spacing.xl,
  },
  typeButton: {
    flex: 1,
    paddingVertical: theme.spacing.sm,
    alignItems: 'center',
    borderRadius: theme.borderRadius.md,
  },
  activeTypeButton: {
    backgroundColor: theme.colors.primary[500],
  },
  typeButtonText: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '500',
    color: theme.colors.text.secondary,
  },
  activeTypeButtonText: {
    color: theme.colors.background.primary,
  },
  inputSection: {
    marginBottom: theme.spacing.xl,
  },
  inputLabel: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '500',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.sm,
  },
  iconsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -theme.spacing.sm,
  },
  iconItem: {
    width: 60,
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
    margin: theme.spacing.sm,
    borderRadius: theme.borderRadius.lg,
    backgroundColor: theme.colors.gray[100],
  },
  selectedIconItem: {
    backgroundColor: theme.colors.primary[100],
    borderWidth: 2,
    borderColor: theme.colors.primary[500],
  },
  iconText: {
    fontSize: theme.typography.fontSizes['2xl'],
  },
  colorsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -theme.spacing.sm,
  },
  colorItem: {
    width: 50,
    height: 50,
    borderRadius: 25,
    margin: theme.spacing.sm,
  },
  selectedColorItem: {
    borderWidth: 3,
    borderColor: theme.colors.primary[500],
  },
  amountInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  budgetInputContainer: {
    flex: 1,
    marginBottom: 0,
  },
  currencySymbol: {
    fontSize: theme.typography.fontSizes.lg,
    color: theme.colors.text.secondary,
    marginRight: theme.spacing.sm,
    marginTop: theme.spacing.sm,
  },
  amountInput: {
    fontSize: theme.typography.fontSizes.lg,
  },
  previewSection: {
    marginBottom: theme.spacing['2xl'],
  },
  previewTitle: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '500',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.md,
  },
  previewBox: {
    padding: theme.spacing.xl,
    borderRadius: theme.borderRadius.lg,
    alignItems: 'center',
  },
  previewIcon: {
    fontSize: 40,
    marginBottom: theme.spacing.sm,
  },
  previewName: {
    fontSize: theme.typography.fontSizes.lg,
    fontWeight: '600',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.sm,
  },
  previewBudget: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.primary,
  },
});

export default AddCategoryScreen;