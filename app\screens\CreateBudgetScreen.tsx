import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  TouchableOpacity,
  FlatList,
} from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation/types';
import theme from '../theme';
import Screen from '../components/common/Screen';
import Button from '../components/ui/Button';
import Card from '../components/ui/Card';
import TextInput from '../components/ui/TextInput';
import { BudgetAPI, CreateBudgetDto } from '../../src/api/budget.api';
import { CategoryAPI, ICategory, CategoryType, CategoryStatus } from '../../src/api/category.api';

type CreateBudgetScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;

const CreateBudgetScreen = () => {
  const navigation = useNavigation<CreateBudgetScreenNavigationProp>();

  // State management
  const [categories, setCategories] = useState<ICategory[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<ICategory | null>(null);
  const [amount, setAmount] = useState('');
  const [month, setMonth] = useState(new Date().getMonth() + 1);
  const [year, setYear] = useState(new Date().getFullYear());
  const [loading, setLoading] = useState(false);
  const [loadingCategories, setLoadingCategories] = useState(true);

  // Get category icon (same logic as CategoryCard)
  const getCategoryIcon = (categoryName: string, type: CategoryType): string => {
    if (!categoryName) return type === CategoryType.INCOME ? '💰' : '💳';

    const name = categoryName.toLowerCase();

    // Expense categories
    if (type === CategoryType.EXPENSE) {
      if (name.includes('food') || name.includes('ăn') || name.includes('dining')) return '🍽️';
      if (name.includes('transport') || name.includes('xe') || name.includes('gas')) return '🚗';
      if (name.includes('shop') || name.includes('mua') || name.includes('shopping')) return '🛒';
      if (name.includes('health') || name.includes('sức khỏe') || name.includes('medical')) return '🏥';
      if (name.includes('entertainment') || name.includes('giải trí')) return '🎬';
      if (name.includes('education') || name.includes('học')) return '📚';
      if (name.includes('home') || name.includes('nhà')) return '🏠';
      if (name.includes('utility') || name.includes('tiện ích')) return '💡';
      return '💳';
    }

    // Income categories
    return '💰';
  };

  // Load expense categories
  const loadCategories = async () => {
    try {
      setLoadingCategories(true);

      // Use CategoryAPI to get expense categories
      const allCategories = await CategoryAPI.getExpenseCategories(1, 100);

      // Ensure allCategories is an array
      if (Array.isArray(allCategories) && allCategories.length > 0) {
        setCategories(allCategories);
      } else {
        console.warn('No categories found, using fallback categories');
        // Fallback categories for testing (if no categories from API)
        const fallbackCategories: ICategory[] = [
          {
            id: 'fallback-1',
            name: 'Food & Dining',
            type: CategoryType.EXPENSE, // 'expense'
            status: CategoryStatus.ACTIVE, // 'active'
            user_id: 'temp',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          },
          {
            id: 'fallback-2',
            name: 'Transportation',
            type: CategoryType.EXPENSE, // 'expense'
            status: CategoryStatus.ACTIVE, // 'active'
            user_id: 'temp',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          },
          {
            id: 'fallback-3',
            name: 'Shopping',
            type: CategoryType.EXPENSE, // 'expense'
            status: CategoryStatus.ACTIVE, // 'active'
            user_id: 'temp',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          },
        ];
        setCategories(fallbackCategories);
      }
    } catch (error: any) {
      console.error('Categories API Error:', error);
      Alert.alert('Error', 'Failed to load categories. Please try again.');
      setCategories([]);
    } finally {
      setLoadingCategories(false);
    }
  };

  useEffect(() => {
    loadCategories();
  }, []);

  const formatCurrency = (value: string) => {
    // Remove non-numeric characters
    const numericValue = value.replace(/[^0-9]/g, '');

    // Format with thousand separators
    if (numericValue) {
      return parseInt(numericValue).toLocaleString('vi-VN');
    }
    return '';
  };

  const handleAmountChange = (value: string) => {
    const formatted = formatCurrency(value);
    setAmount(formatted);
  };

  const getNumericAmount = () => {
    return parseInt(amount.replace(/[^0-9]/g, '') || '0');
  };

  const handleCreateBudget = async () => {
    if (!selectedCategory) {
      Alert.alert('Error', 'Please select a category');
      return;
    }

    const numericAmount = getNumericAmount();
    if (numericAmount <= 0) {
      Alert.alert('Error', 'Please enter a valid amount');
      return;
    }

    try {
      setLoading(true);

      const createData: CreateBudgetDto = {
        categoryId: selectedCategory.id,
        month,
        year,
        amount: numericAmount,
      };

      await BudgetAPI.create(createData);

      Alert.alert(
        'Success',
        'Budget created successfully!',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (error: any) {
      let errorMessage = 'Failed to create budget. Please try again.';

      if (error.response?.status === 409) {
        errorMessage = 'Budget already exists for this category and period.';
      } else if (error.response?.status === 400) {
        errorMessage = error.response.data?.message || 'Invalid budget data.';
      } else if (error.response?.status === 401) {
        errorMessage = 'Authentication failed. Please login again.';
      } else if (error.message) {
        errorMessage = error.message;
      }

      Alert.alert('Error', errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const renderCategoryItem = ({ item }: { item: ICategory }) => (
    <TouchableOpacity
      style={[
        styles.categoryItem,
        selectedCategory?.id === item.id && styles.selectedCategoryItem,
      ]}
      onPress={() => setSelectedCategory(item)}
    >
      <View style={styles.categoryIcon}>
        <Text style={styles.categoryIconText}>
          {getCategoryIcon(item.name, item.type)}
        </Text>
      </View>
      <Text
        style={[
          styles.categoryName,
          selectedCategory?.id === item.id && styles.selectedCategoryName,
        ]}
      >
        {item.name}
      </Text>
    </TouchableOpacity>
  );

  const getMonthName = (monthNum: number) => {
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return months[monthNum - 1];
  };

  return (
    <Screen>
      <KeyboardAwareScrollView
        style={styles.container}
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
        enableOnAndroid={true}
        enableAutomaticScroll={true}
        extraScrollHeight={20}
        extraHeight={100}
      >
        <View style={styles.content}>
            <Text style={styles.title}>Create New Budget</Text>
            <Text style={styles.subtitle}>Set spending limits for your categories</Text>

          {/* Period Selection */}
          <Card style={styles.card}>
            <Text style={styles.sectionTitle}>Budget Period</Text>
            <View style={styles.periodSelector}>
              <TouchableOpacity
                style={styles.periodButton}
                onPress={() => {
                  if (month > 1) {
                    setMonth(month - 1);
                  } else {
                    setMonth(12);
                    setYear(year - 1);
                  }
                }}
              >
                <Text style={styles.periodButtonText}>←</Text>
              </TouchableOpacity>

              <Text style={styles.periodText}>
                {getMonthName(month)} {year}
              </Text>

              <TouchableOpacity
                style={styles.periodButton}
                onPress={() => {
                  if (month < 12) {
                    setMonth(month + 1);
                  } else {
                    setMonth(1);
                    setYear(year + 1);
                  }
                }}
              >
                <Text style={styles.periodButtonText}>→</Text>
              </TouchableOpacity>
            </View>
          </Card>

          {/* Category Selection */}
          <Card style={styles.card}>
            <Text style={styles.sectionTitle}>Select Category</Text>
            {loadingCategories ? (
              <Text style={styles.loadingText}>Loading categories...</Text>
            ) : categories.length > 0 ? (
              <FlatList
                data={categories}
                renderItem={renderCategoryItem}
                keyExtractor={(item) => item.id}
                numColumns={2}
                scrollEnabled={false}
                contentContainerStyle={styles.categoriesGrid}
              />
            ) : (
              <Text style={styles.emptyText}>No expense categories found</Text>
            )}
          </Card>

          {/* Amount Input */}
          <Card style={styles.card}>
            <Text style={styles.sectionTitle}>Budget Amount</Text>
            <TextInput
              label="Amount (VND)"
              value={amount}
              onChangeText={handleAmountChange}
              placeholder="Enter budget amount"
              keyboardType="numeric"
              style={styles.amountInput}
            />
            {amount && (
              <Text style={styles.amountPreview}>
                Budget: {amount} VND
              </Text>
            )}
          </Card>

          {/* Create Button */}
          <Button
            title={loading ? "Creating..." : "Create Budget"}
            onPress={handleCreateBudget}
            variant="primary"
            disabled={loading || !selectedCategory || !amount}
            style={styles.createButton}
          />
        </View>
      </KeyboardAwareScrollView>
    </Screen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    paddingBottom: theme.spacing.xl,
  },
  content: {
    padding: theme.spacing.md,
  },
  title: {
    fontSize: theme.typography.fontSizes['2xl'],
    fontWeight: '700',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.xs,
  },
  subtitle: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.secondary,
    marginBottom: theme.spacing.lg,
  },
  card: {
    marginBottom: theme.spacing.lg,
  },
  sectionTitle: {
    fontSize: theme.typography.fontSizes.lg,
    fontWeight: '600',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.md,
  },
  periodSelector: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.md,
  },
  periodButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.primary[100],
    justifyContent: 'center',
    alignItems: 'center',
  },
  periodButtonText: {
    fontSize: theme.typography.fontSizes.lg,
    fontWeight: '600',
    color: theme.colors.primary[600],
  },
  periodText: {
    fontSize: theme.typography.fontSizes.lg,
    fontWeight: '600',
    color: theme.colors.text.primary,
  },
  categoriesGrid: {
    gap: theme.spacing.sm,
  },
  categoryItem: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    padding: theme.spacing.md,
    margin: theme.spacing.xs,
    backgroundColor: theme.colors.gray[50],
    borderRadius: theme.borderRadius.lg,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedCategoryItem: {
    backgroundColor: theme.colors.primary[50],
    borderColor: theme.colors.primary[500],
  },
  categoryIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: theme.colors.background.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: theme.spacing.sm,
  },
  categoryIconText: {
    fontSize: theme.typography.fontSizes.md,
  },
  categoryName: {
    fontSize: theme.typography.fontSizes.sm,
    fontWeight: '500',
    color: theme.colors.text.primary,
    flex: 1,
  },
  selectedCategoryName: {
    color: theme.colors.primary[600],
    fontWeight: '600',
  },
  amountInput: {
    marginBottom: theme.spacing.sm,
    fontSize: theme.typography.fontSizes.lg,
    height: 60, // Tăng chiều cao
    paddingHorizontal: theme.spacing.md,
  },
  amountPreview: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '600',
    color: theme.colors.primary[600],
    textAlign: 'center',
  },
  createButton: {
    marginTop: theme.spacing.md,
  },
  loadingText: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.secondary,
    textAlign: 'center',
    paddingVertical: theme.spacing.lg,
  },
  emptyText: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.secondary,
    textAlign: 'center',
    paddingVertical: theme.spacing.lg,
  },
});

export default CreateBudgetScreen;
