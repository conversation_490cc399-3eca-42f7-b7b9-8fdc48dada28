import React from 'react';
import { View, StyleSheet, ViewProps } from 'react-native';
import theme from '../../theme';

interface CardProps extends ViewProps {
  children: React.ReactNode;
  variant?: 'primary' | 'flat';
  style?: any;
}

const Card: React.FC<CardProps> = ({
  children,
  variant = 'primary',
  style,
  ...props
}) => {
  const cardStyle = variant === 'flat' 
    ? styles.flatCard 
    : styles.card;

  return (
    <View style={[cardStyle, style]} {...props}>
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: theme.cards.primary.backgroundColor,
    borderRadius: theme.cards.primary.borderRadius,
    padding: theme.cards.primary.padding,
    shadowColor: theme.cards.primary.shadowColor,
    shadowOffset: theme.cards.primary.shadowOffset,
    shadowOpacity: theme.cards.primary.shadowOpacity,
    shadowRadius: theme.cards.primary.shadowRadius,
    elevation: theme.cards.primary.elevation,
    marginBottom: theme.spacing.md,
  },
  flatCard: {
    backgroundColor: theme.colors.background.primary,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.md,
  }
});

export default Card; 