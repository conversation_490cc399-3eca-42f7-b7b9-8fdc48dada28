import React from 'react';
import { TouchableOpacity, Text, ActivityIndicator, View, StyleSheet } from 'react-native';
import theme from '../../theme';

type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'danger';
type ButtonSize = 'sm' | 'md' | 'lg';

interface ButtonProps {
  onPress: () => void;
  title: string;
  variant?: ButtonVariant;
  size?: ButtonSize;
  disabled?: boolean;
  loading?: boolean;
  fullWidth?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  style?: any;
}

const Button: React.FC<ButtonProps> = ({
  onPress,
  title,
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  fullWidth = false,
  leftIcon,
  rightIcon,
  style,
}) => {
  // Get size styles
  const getSizeStyles = () => {
    switch (size) {
      case 'sm':
        return {
          paddingVertical: theme.spacing.xs,
          paddingHorizontal: theme.spacing.sm,
          fontSize: theme.typography.fontSizes.xs,
        };
      case 'lg':
        return {
          paddingVertical: theme.spacing.lg,
          paddingHorizontal: theme.spacing.xl,
          fontSize: theme.typography.fontSizes.lg,
        };
      default: // md
        return {
          paddingVertical: theme.buttons[variant].paddingVertical,
          paddingHorizontal: theme.buttons[variant].paddingHorizontal,
          fontSize: theme.typography.fontSizes.md,
        };
    }
  };

  // Get container styles based on variant
  const getContainerStyle = () => {
    // Lấy các giá trị từ getSizeStyles
    const sizeStyles = getSizeStyles();
    
    // Tạo style cơ bản
    const baseStyle: any = {
      backgroundColor: theme.buttons[variant].backgroundColor,
      borderRadius: theme.buttons[variant].borderRadius,
      opacity: disabled ? 0.5 : 1,
      width: fullWidth ? '100%' : 'auto',
      paddingVertical: sizeStyles.paddingVertical,
      paddingHorizontal: sizeStyles.paddingHorizontal,
    };

    // Thêm border cho variant outline
    if (variant === 'outline') {
      baseStyle.borderWidth = theme.buttons.outline.borderWidth;
      baseStyle.borderColor = theme.buttons.outline.borderColor;
    }

    return baseStyle;
  };

  // Get text color based on variant
  const getTextColor = () => {
    return theme.buttons[variant].textColor;
  };

  return (
    <TouchableOpacity
      style={[styles.button, getContainerStyle(), style]}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.7}
    >
      {loading ? (
        <ActivityIndicator 
          color={getTextColor()} 
          size="small" 
        />
      ) : (
        <View style={styles.contentContainer}>
          {leftIcon && <View style={styles.leftIcon}>{leftIcon}</View>}
          <Text style={[
            styles.text, 
            { color: getTextColor(), fontSize: getSizeStyles().fontSize }
          ]}>
            {title}
          </Text>
          {rightIcon && <View style={styles.rightIcon}>{rightIcon}</View>}
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  contentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  text: {
    fontWeight: '600', // Sửa lỗi TypeScript bằng cách sử dụng giá trị cụ thể
    textAlign: 'center',
  },
  leftIcon: {
    marginRight: theme.spacing.xs,
  },
  rightIcon: {
    marginLeft: theme.spacing.xs,
  },
});

export default Button; 