import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import DateTimePicker from '@react-native-community/datetimepicker';
import { RootStackParamList } from '../navigation/types';
import { TransactionStatistics, CategoryStatistics } from '../types/transaction';
import { TransactionAPI } from '../../src/api/transaction.api';
import theme from '../theme';
import Screen from '../components/common/Screen';
import Button from '../components/ui/Button';
import Card from '../components/ui/Card';

type TransactionStatisticsScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'TransactionStatistics'
>;

const { width: screenWidth } = Dimensions.get('window');

const TransactionStatisticsScreen = () => {
  const navigation = useNavigation<TransactionStatisticsScreenNavigationProp>();

  const [statistics, setStatistics] = useState<TransactionStatistics | null>(null);
  const [loading, setLoading] = useState(false);
  const [startDate, setStartDate] = useState(() => {
    const date = new Date();
    date.setDate(1); // First day of current month
    return date;
  });
  const [endDate, setEndDate] = useState(new Date());
  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);

  useEffect(() => {
    loadStatistics();
  }, [startDate, endDate]);

  const loadStatistics = async () => {
    try {
      setLoading(true);
      const stats = await TransactionAPI.getStatistics(startDate, endDate);
      setStatistics(stats);
    } catch (error) {
      console.error('Error loading statistics:', error);
      Alert.alert('Lỗi', 'Không thể tải thống kê');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return amount.toLocaleString('vi-VN') + ' ₫';
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('vi-VN');
  };

  const getQuickDateRanges = () => [
    {
      label: 'Tháng này',
      startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
      endDate: new Date(),
    },
    {
      label: 'Tháng trước',
      startDate: new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1),
      endDate: new Date(new Date().getFullYear(), new Date().getMonth(), 0),
    },
    {
      label: '3 tháng qua',
      startDate: new Date(new Date().setMonth(new Date().getMonth() - 3)),
      endDate: new Date(),
    },
    {
      label: 'Năm này',
      startDate: new Date(new Date().getFullYear(), 0, 1),
      endDate: new Date(),
    },
  ];

  const handleQuickDateRange = (range: { startDate: Date; endDate: Date }) => {
    setStartDate(range.startDate);
    setEndDate(range.endDate);
  };

  const renderDateSelector = () => (
    <Card style={styles.sectionCard}>
      <Text style={styles.sectionTitle}>Khoảng thời gian</Text>
      
      {/* Quick Date Range Buttons */}
      <View style={styles.quickRangeContainer}>
        {getQuickDateRanges().map((range, index) => (
          <TouchableOpacity
            key={index}
            style={styles.quickRangeButton}
            onPress={() => handleQuickDateRange(range)}
          >
            <Text style={styles.quickRangeText}>{range.label}</Text>
          </TouchableOpacity>
        ))}
      </View>

      {/* Custom Date Range */}
      <View style={styles.dateRangeContainer}>
        <TouchableOpacity
          style={styles.dateButton}
          onPress={() => setShowStartDatePicker(true)}
        >
          <Text style={styles.dateButtonText}>Từ: {formatDate(startDate)}</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={styles.dateButton}
          onPress={() => setShowEndDatePicker(true)}
        >
          <Text style={styles.dateButtonText}>Đến: {formatDate(endDate)}</Text>
        </TouchableOpacity>
      </View>
    </Card>
  );

  const renderOverviewSection = () => {
    if (!statistics) return null;

    const netAmount = statistics.net_amount;
    const isPositive = netAmount >= 0;

    return (
      <Card style={styles.sectionCard}>
        <Text style={styles.sectionTitle}>Tổng quan</Text>
        
        <View style={styles.overviewGrid}>
          <View style={styles.overviewItem}>
            <Text style={styles.overviewLabel}>Thu nhập</Text>
            <Text style={[styles.overviewValue, styles.incomeValue]}>
              {formatCurrency(statistics.total_income)}
            </Text>
          </View>
          
          <View style={styles.overviewItem}>
            <Text style={styles.overviewLabel}>Chi tiêu</Text>
            <Text style={[styles.overviewValue, styles.expenseValue]}>
              {formatCurrency(statistics.total_expense)}
            </Text>
          </View>
          
          <View style={styles.overviewItem}>
            <Text style={styles.overviewLabel}>Số dư ròng</Text>
            <Text style={[
              styles.overviewValue,
              isPositive ? styles.incomeValue : styles.expenseValue
            ]}>
              {isPositive ? '+' : ''}{formatCurrency(netAmount)}
            </Text>
          </View>
          
          <View style={styles.overviewItem}>
            <Text style={styles.overviewLabel}>Số giao dịch</Text>
            <Text style={styles.overviewValue}>
              {statistics.transaction_count}
            </Text>
          </View>
          
          <View style={styles.overviewItem}>
            <Text style={styles.overviewLabel}>Trung bình/giao dịch</Text>
            <Text style={styles.overviewValue}>
              {formatCurrency(statistics.average_amount)}
            </Text>
          </View>
        </View>
      </Card>
    );
  };

  const renderCategoryChart = () => {
    if (!statistics || statistics.categories.length === 0) return null;

    const totalAmount = statistics.total_income + statistics.total_expense;
    const chartSize = screenWidth - (theme.spacing.md * 4);
    const centerX = chartSize / 2;
    const centerY = chartSize / 2;
    const radius = Math.min(centerX, centerY) - 20;

    let currentAngle = -Math.PI / 2; // Start from top

    // Predefined colors array to avoid dynamic indexing
    const chartColors = [
      theme.colors.primary[500],
      theme.colors.primary[400],
      theme.colors.primary[300],
      theme.colors.primary[600],
      theme.colors.primary[700],
    ];

    return (
      <Card style={styles.sectionCard}>
        <Text style={styles.sectionTitle}>Phân bố theo danh mục</Text>
        
        {/* Simple Chart Representation */}
        <View style={styles.chartContainer}>
          <View style={[styles.chartCircle, { width: chartSize, height: chartSize }]}>
            {statistics.categories.slice(0, 5).map((category, index) => {
              const percentage = category.percentage;
              const color = chartColors[index] || theme.colors.gray[500];
              
              return (
                <View key={category.category_id} style={styles.chartSegment}>
                  <View style={[styles.chartColor, { backgroundColor: color }]} />
                  <Text style={styles.chartPercentage}>{percentage.toFixed(1)}%</Text>
                </View>
              );
            })}
          </View>
        </View>
        
        {/* Category Legend */}
        <View style={styles.categoryLegend}>
          {statistics.categories.slice(0, 5).map((category, index) => {
            const color = chartColors[index] || theme.colors.gray[500];
            
            return (
              <View key={category.category_id} style={styles.legendItem}>
                <View style={[styles.legendColor, { backgroundColor: color }]} />
                <View style={styles.legendContent}>
                  <Text style={styles.legendName}>{category.category_name}</Text>
                  <Text style={styles.legendValue}>
                    {formatCurrency(category.total_amount)} ({category.percentage.toFixed(1)}%)
                  </Text>
                  <Text style={styles.legendCount}>
                    {category.transaction_count} giao dịch
                  </Text>
                </View>
              </View>
            );
          })}
          
          {statistics.categories.length > 5 && (
            <Text style={styles.moreCategories}>
              Và {statistics.categories.length - 5} danh mục khác...
            </Text>
          )}
        </View>
      </Card>
    );
  };

  const renderTopCategories = () => {
    if (!statistics || statistics.categories.length === 0) return null;

    const sortedCategories = [...statistics.categories]
      .sort((a, b) => b.total_amount - a.total_amount)
      .slice(0, 10);

    return (
      <Card style={styles.sectionCard}>
        <Text style={styles.sectionTitle}>Top danh mục chi tiêu</Text>
        
        {sortedCategories.map((category, index) => (
          <View key={category.category_id} style={styles.categoryItem}>
            <View style={styles.categoryRank}>
              <Text style={styles.rankNumber}>{index + 1}</Text>
            </View>
            
            <View style={styles.categoryInfo}>
              <Text style={styles.categoryName}>{category.category_name}</Text>
              <Text style={styles.categoryCount}>
                {category.transaction_count} giao dịch
              </Text>
            </View>
            
            <View style={styles.categoryAmount}>
              <Text style={styles.categoryValue}>
                {formatCurrency(category.total_amount)}
              </Text>
              <Text style={styles.categoryPercentage}>
                {category.percentage.toFixed(1)}%
              </Text>
            </View>
          </View>
        ))}
      </Card>
    );
  };

  const renderEmptyState = () => (
    <Card style={styles.sectionCard}>
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyIcon}>📊</Text>
        <Text style={styles.emptyTitle}>Chưa có dữ liệu thống kê</Text>
        <Text style={styles.emptySubtitle}>
          Không có giao dịch nào trong khoảng thời gian đã chọn
        </Text>
        <Button
          title="Thêm giao dịch"
          onPress={() => navigation.navigate('CreateTransaction')}
          style={styles.emptyButton}
        />
      </View>
    </Card>
  );

  return (
    <Screen>
      <ScrollView 
        style={styles.container}
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
      >
        <Text style={styles.headerTitle}>Thống kê giao dịch</Text>
        
        {renderDateSelector()}
        
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.colors.primary[500]} />
            <Text style={styles.loadingText}>Đang tải thống kê...</Text>
          </View>
        ) : statistics && statistics.transaction_count > 0 ? (
          <>
            {renderOverviewSection()}
            {renderCategoryChart()}
            {renderTopCategories()}
          </>
        ) : (
          renderEmptyState()
        )}
      </ScrollView>

      {/* Date Pickers */}
      {showStartDatePicker && (
        <DateTimePicker
          value={startDate}
          mode="date"
          display="default"
          maximumDate={endDate}
          onChange={(event, selectedDate) => {
            setShowStartDatePicker(false);
            if (selectedDate) {
              setStartDate(selectedDate);
            }
          }}
        />
      )}

      {showEndDatePicker && (
        <DateTimePicker
          value={endDate}
          mode="date"
          display="default"
          minimumDate={startDate}
          maximumDate={new Date()}
          onChange={(event, selectedDate) => {
            setShowEndDatePicker(false);
            if (selectedDate) {
              setEndDate(selectedDate);
            }
          }}
        />
      )}
    </Screen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background.secondary,
  },
  scrollContainer: {
    padding: theme.spacing.md,
  },
  headerTitle: {
    fontSize: theme.typography.fontSizes['2xl'],
    fontWeight: theme.typography.fontWeights.bold,
    color: theme.colors.text.primary,
    textAlign: 'center',
    marginBottom: theme.spacing.xl,
  },
  sectionCard: {
    marginBottom: theme.spacing.md,
    padding: theme.spacing.md,
  },
  sectionTitle: {
    fontSize: theme.typography.fontSizes.lg,
    fontWeight: theme.typography.fontWeights.semibold,
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.md,
  },
  
  // Date Selector
  quickRangeContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: theme.spacing.sm,
    marginBottom: theme.spacing.md,
  },
  quickRangeButton: {
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.md,
    backgroundColor: theme.colors.gray[100],
    borderRadius: theme.borderRadius.md,
  },
  quickRangeText: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.primary,
  },
  dateRangeContainer: {
    flexDirection: 'row',
    gap: theme.spacing.sm,
  },
  dateButton: {
    flex: 1,
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.md,
    backgroundColor: theme.colors.primary[500],
    borderRadius: theme.borderRadius.md,
    alignItems: 'center',
  },
  dateButtonText: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.inverse,
    fontWeight: '500',
  },
  
  // Overview Section
  overviewGrid: {
    gap: theme.spacing.md,
  },
  overviewItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: theme.spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray[100],
  },
  overviewLabel: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.secondary,
  },
  overviewValue: {
    fontSize: theme.typography.fontSizes.lg,
    fontWeight: theme.typography.fontWeights.semibold,
    color: theme.colors.text.primary,
  },
  incomeValue: {
    color: theme.colors.success,
  },
  expenseValue: {
    color: theme.colors.error,
  },
  
  // Chart Section
  chartContainer: {
    alignItems: 'center',
    marginBottom: theme.spacing.lg,
  },
  chartCircle: {
    backgroundColor: theme.colors.gray[100],
    borderRadius: 1000,
    justifyContent: 'center',
    alignItems: 'center',
  },
  chartSegment: {
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
  },
  chartColor: {
    width: 20,
    height: 20,
    borderRadius: 10,
    marginBottom: theme.spacing.xs,
  },
  chartPercentage: {
    fontSize: theme.typography.fontSizes.xs,
    fontWeight: 'bold',
    color: theme.colors.text.primary,
  },
  
  // Category Legend
  categoryLegend: {
    gap: theme.spacing.sm,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: theme.spacing.sm,
    paddingVertical: theme.spacing.sm,
  },
  legendColor: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginTop: 2,
  },
  legendContent: {
    flex: 1,
  },
  legendName: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '500',
    color: theme.colors.text.primary,
  },
  legendValue: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
    marginTop: 2,
  },
  legendCount: {
    fontSize: theme.typography.fontSizes.xs,
    color: theme.colors.text.secondary,
    marginTop: 2,
  },
  moreCategories: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
    fontStyle: 'italic',
    textAlign: 'center',
    marginTop: theme.spacing.sm,
  },
  
  // Top Categories
  categoryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: theme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray[100],
  },
  categoryRank: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: theme.colors.primary[500],
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: theme.spacing.md,
  },
  rankNumber: {
    fontSize: theme.typography.fontSizes.sm,
    fontWeight: 'bold',
    color: theme.colors.text.inverse,
  },
  categoryInfo: {
    flex: 1,
  },
  categoryName: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '500',
    color: theme.colors.text.primary,
  },
  categoryCount: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
    marginTop: 2,
  },
  categoryAmount: {
    alignItems: 'flex-end',
  },
  categoryValue: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: theme.typography.fontWeights.semibold,
    color: theme.colors.text.primary,
  },
  categoryPercentage: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
    marginTop: 2,
  },
  
  // Loading State
  loadingContainer: {
    alignItems: 'center',
    paddingVertical: theme.spacing.xl,
  },
  loadingText: {
    marginTop: theme.spacing.sm,
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.secondary,
  },
  
  // Empty State
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: theme.spacing.xl,
  },
  emptyIcon: {
    fontSize: 64,
    marginBottom: theme.spacing.lg,
  },
  emptyTitle: {
    fontSize: theme.typography.fontSizes.xl,
    fontWeight: theme.typography.fontWeights.semibold,
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.sm,
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.secondary,
    textAlign: 'center',
    marginBottom: theme.spacing.xl,
    lineHeight: 24,
  },
  emptyButton: {
    paddingHorizontal: theme.spacing.xl,
  },
});

export default TransactionStatisticsScreen; 