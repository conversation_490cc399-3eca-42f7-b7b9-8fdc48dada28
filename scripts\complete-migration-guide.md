# Hướng Dẫn <PERSON>yển Đổi <PERSON>àn Chỉnh <PERSON><PERSON><PERSON> Theme System

## Màn hình đã chuyển đổi

Các màn hình sau đã được chuyển đổi sang sử dụng theme:

1. `HomeScreen.tsx`
2. `LoginScreen.tsx`
3. `DetailsScreen.tsx`
4. `GoalsScreen.tsx`
5. `TransactionsScreen.tsx`
6. `BudgetScreen.tsx`
7. `TabNavigator.tsx` (không phải màn hình nhưng đã được chuyển đổi)

## Màn hình cần chuyển đổi

Các màn hình còn lại cần được chuyển đổi:

1. `MoreScreen.tsx`
2. `AnalyticsScreen.tsx`
3. `ProfileScreen.tsx`
4. `RegisterScreen.tsx`
5. `ForgotPasswordScreen.tsx`
6. `GoalDetailScre<PERSON>.tsx`
7. `TransactionDetailScreen.tsx`
8. `BudgetDetailScreen.tsx`
9. `InitialBalanceScreen.tsx`
10. `SetupBudgetScreen.tsx`
11. `AddTransactionScreen.tsx`
12. `AddGoalScreen.tsx`
13. `AddCategoryScreen.tsx`
14. `CategoryDetailScreen.tsx`
15. `ChartDetailScreen.tsx`
16. `FormScreen.tsx`
17. `GoalSetupScreen.tsx`

## Quy trình chuyển đổi

Thực hiện theo các bước sau cho từng màn hình:

### 1. Thay đổi cơ bản

```typescript
// Thêm các import cần thiết
import theme from '../theme';
import Screen from '../components/common/Screen';
import Button from '../components/ui/Button';
import Card from '../components/ui/Card';
import TextInput from '../components/ui/TextInput';

// Xóa import không cần thiết
// - import { SafeAreaView } from 'react-native';  
// - import { StatusBar } from 'expo-status-bar';
// - import { cssInterop } from 'nativewind'; // nếu có

// Thay đổi component gốc
// TỪ:
<SafeAreaView style={styles.container}>
  <StatusBar style="dark" />
  {/* content */}
</SafeAreaView>

// SANG:
<Screen>
  {/* content */}
</Screen>
```

### 2. Chuyển đổi components UI phổ biến

#### Button

```typescript
// TỪ:
<TouchableOpacity style={styles.button} onPress={onPress}>
  <Text style={styles.buttonText}>Button Text</Text>
</TouchableOpacity>

// SANG:
<Button
  title="Button Text"
  onPress={onPress}
  variant="primary" // hoặc: "secondary", "outline", "danger"
  size="md" // hoặc: "sm", "lg"
  fullWidth={true} // tùy chọn
/>
```

#### TextInput

```typescript
// TỪ:
<View>
  <Text style={styles.label}>Nhập email</Text>
  <TextInput
    style={styles.input}
    placeholder="Email"
    value={email}
    onChangeText={setEmail}
  />
</View>

// SANG:
<TextInput
  label="Nhập email"
  placeholder="Email"
  value={email}
  onChangeText={setEmail}
  error={emailError} // tùy chọn
/>
```

#### Card

```typescript
// TỪ:
<View style={styles.card}>
  {/* content */}
</View>

// SANG:
<Card style={customStyle}> // tùy chọn thêm custom style
  {/* content */}
</Card>
```

### 3. Chuyển đổi StyleSheet

```typescript
// TỪ:
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  title: {
    fontSize: 28,
    fontWeight: '700',
    color: '#111827',
    marginBottom: 8,
  },
  /* ... */
});

// SANG:
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background.primary,
  },
  title: {
    fontSize: theme.typography.fontSizes['2xl'],
    fontWeight: '700',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.xs,
  },
  /* ... */
});
```

## Bảng chuyển đổi giá trị cụ thể

### Màu sắc

| Giá trị cũ | Giá trị theme |
|------------|---------------|
| '#FFFFFF' | theme.colors.background.primary |
| '#F9FAFB' | theme.colors.background.secondary |
| '#F3F4F6' | theme.colors.gray[100] |
| '#E5E7EB' | theme.colors.gray[200] |
| '#D1D5DB' | theme.colors.gray[300] |
| '#9CA3AF' | theme.colors.gray[400] |
| '#6B7280' | theme.colors.text.secondary |
| '#4B5563' | theme.colors.gray[600] |
| '#374151' | theme.colors.gray[700] |
| '#1F2937' | theme.colors.gray[800] |
| '#111827' | theme.colors.text.primary |
| '#0EA5E9' | theme.colors.primary[500] |
| '#0284C7' | theme.colors.primary[600] |
| '#0369A1' | theme.colors.primary[700] |
| '#E0F2FE' | theme.colors.primary[100] |
| '#EF4444' | theme.colors.error |
| '#10B981' | theme.colors.success |

### Spacing

| Giá trị cũ | Giá trị theme |
|------------|---------------|
| 2 | theme.spacing.xs / 2 |
| 4 | theme.spacing.xs |
| 8 | theme.spacing.sm |
| 12 | theme.spacing.md / 2 |
| 16 | theme.spacing.md |
| 20 | theme.spacing.lg |
| 24 | theme.spacing.xl |
| 32 | theme.spacing['2xl'] |
| 48 | theme.spacing['3xl'] |
| 64 | theme.spacing['4xl'] |

### Typography

| Giá trị cũ | Giá trị theme |
|------------|---------------|
| 12 | theme.typography.fontSizes.xs |
| 14 | theme.typography.fontSizes.sm |
| 16 | theme.typography.fontSizes.md |
| 18 | theme.typography.fontSizes.lg |
| 20 | theme.typography.fontSizes.xl |
| 24 | theme.typography.fontSizes['2xl'] |
| 28-30 | theme.typography.fontSizes['3xl'] |
| 36 | theme.typography.fontSizes['4xl'] |
| '400' | '400' hoặc 'normal' |
| '500' | '500' hoặc 'medium' |
| '600' | '600' hoặc 'semibold' |
| '700' | '700' hoặc 'bold' |

### Border Radius

| Giá trị cũ | Giá trị theme |
|------------|---------------|
| 2 | theme.borderRadius.xs |
| 4 | theme.borderRadius.sm |
| 8 | theme.borderRadius.md |
| 12 | theme.borderRadius.lg |
| 16 | theme.borderRadius.xl |
| 24-999 | theme.borderRadius.full |

## Lưu ý quan trọng khi chuyển đổi

1. **Kiểm tra kỹ các thuộc tính typography**: Đảm bảo các thuộc tính `fontWeight` được đặt là giá trị cụ thể như `'400'`, `'500'`, `'600'` thay vì `normal`, `medium`, `semibold` để tránh lỗi TypeScript.

2. **Xử lý màu sắc đặc biệt**: Một số màu đặc biệt như `theme.colors.white` hoặc `theme.colors.shadow` có thể không tồn tại. Thay vào đó, sử dụng:
   - `theme.colors.background.primary` thay cho `theme.colors.white`
   - `'#000'` thay cho `theme.colors.shadow`
   - `theme.colors.gray[400]` thay cho `theme.colors.text.tertiary`

3. **Xử lý fontWeight**: Nếu gặp lỗi TypeScript liên quan đến fontWeight, hãy sử dụng giá trị cụ thể:
   ```typescript
   // Thay vì
   fontWeight: theme.typography.fontWeights.bold
   // Sử dụng
   fontWeight: '700'
   ```

4. **Kiểm tra tương thích responsive**: Sau khi chuyển đổi, hãy kiểm tra lại giao diện trên các thiết bị có kích thước khác nhau để đảm bảo tính nhất quán.

## Kết luận

Việc chuyển đổi sang theme system sẽ giúp dự án dễ bảo trì hơn và có tính nhất quán cao. Mặc dù có thể mất thời gian ban đầu, nhưng lợi ích lâu dài sẽ rất đáng giá khi bạn có thể dễ dàng thay đổi giao diện toàn bộ ứng dụng chỉ bằng cách chỉnh sửa file theme thay vì phải cập nhật từng màn hình riêng biệt. 