import React from 'react';
import { View, Text, Image, ScrollView, StyleSheet } from 'react-native';
import { RouteProp, useRoute } from '@react-navigation/native';

import theme from '../theme';
import Screen from '../components/common/Screen';
import Card from '../components/ui/Card';
import Button from '../components/ui/Button';
import { RootStackParamList } from '../navigation/types';

type DetailsScreenRouteProp = RouteProp<RootStackParamList, 'Details'>;

const DetailsScreen = () => {
  const route = useRoute<DetailsScreenRouteProp>();
  const { id, title } = route.params;

  // Mock data for demonstration
  const product = {
    id,
    name: title,
    price: 39.99,
    image: 'https://via.placeholder.com/400',
    description: 'This is a detailed description about the product. It would normally include information about the features, specifications, and other important details that a customer might want to know.',
    specifications: [
      { label: 'Brand', value: 'Brand Name' },
      { label: 'Model', value: 'Model X2000' },
      { label: 'Year', value: '2023' },
      { label: 'Color', value: 'Midnight Blue' },
    ],
  };

  return (
    <Screen>
      <ScrollView style={styles.container}>
        <View>
          <Image
            source={{ uri: product.image }}
            style={styles.image}
            resizeMode="cover"
          />
        </View>

        <View style={styles.contentContainer}>
          <Text style={styles.productName}>{product.name}</Text>
          <Text style={styles.productPrice}>
            ${product.price.toFixed(2)}
          </Text>

          <View style={styles.sectionContainer}>
            <Text style={styles.sectionTitle}>Description</Text>
            <Text style={styles.description}>
              {product.description}
            </Text>
          </View>

          <View style={styles.sectionContainer}>
            <Text style={styles.sectionTitle}>Specifications</Text>
            <Card variant="flat">
              {product.specifications.map((spec, index) => (
                <View 
                  key={index} 
                  style={[
                    styles.specRow,
                    index < product.specifications.length - 1 && styles.specDivider
                  ]}
                >
                  <Text style={styles.specLabel}>{spec.label}</Text>
                  <Text style={styles.specValue}>{spec.value}</Text>
                </View>
              ))}
            </Card>
          </View>

          <View style={styles.buttonContainer}>
            <Button
              title="Add to Cart"
              onPress={() => alert(`Added ${product.name} to cart!`)}
              variant="primary"
              fullWidth
            />
          </View>

          <View style={styles.footer}>
            <Text style={styles.footerText}>
              Product ID: {product.id}
            </Text>
          </View>
        </View>
      </ScrollView>
    </Screen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  image: {
    width: '100%',
    height: 256,
  },
  contentContainer: {
    padding: theme.spacing.md,
  },
  productName: {
    fontSize: theme.typography.fontSizes['2xl'],
    fontWeight: '700',
    color: theme.colors.text.primary,
  },
  productPrice: {
    fontSize: theme.typography.fontSizes.xl,
    fontWeight: '600',
    color: theme.colors.primary[600],
    marginTop: theme.spacing.xs,
  },
  sectionContainer: {
    marginTop: theme.spacing.lg,
  },
  sectionTitle: {
    fontSize: theme.typography.fontSizes.lg,
    fontWeight: '600',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.sm,
  },
  description: {
    color: theme.colors.text.secondary,
    marginTop: theme.spacing.sm,
  },
  specRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.md,
  },
  specDivider: {
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray[100],
  },
  specLabel: {
    color: theme.colors.text.secondary,
  },
  specValue: {
    fontWeight: '500',
    color: theme.colors.text.primary,
  },
  buttonContainer: {
    flexDirection: 'row',
    marginTop: theme.spacing['2xl'],
    marginBottom: theme.spacing.lg,
  },
  footer: {
    marginBottom: theme.spacing['2xl'],
  },
  footerText: {
    textAlign: 'center',
    color: theme.colors.text.secondary,
  }
});

export default DetailsScreen; 