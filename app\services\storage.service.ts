import AsyncStorage from '@react-native-async-storage/async-storage';

const STORAGE_KEYS = {
    ACCESS_TOKEN: 'access_token',
    REFRESH_TOKEN: 'refresh_token',
};

export const StorageService = {
    // Save access token
    setAccessToken: async (token: string) => {
        try {
            await AsyncStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, token);
        } catch (error) {
            // Don't log to console to avoid showing error overlay
            throw error;
        }
    },

    // Get access token
    getAccessToken: async () => {
        try {
            return await AsyncStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);
        } catch (error) {
            // Don't log to console to avoid showing error overlay
            throw error;
        }
    },

    // Save refresh token
    setRefreshToken: async (token: string) => {
        try {
            await AsyncStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, token);
        } catch (error) {
            // Don't log to console to avoid showing error overlay
            throw error;
        }
    },

    // Get refresh token
    getRefreshToken: async () => {
        try {
            return await AsyncStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);
        } catch (error) {
            // Don't log to console to avoid showing error overlay
            throw error;
        }
    },

    // Remove access token
    removeAccessToken: async () => {
        try {
            await AsyncStorage.removeItem(STORAGE_KEYS.ACCESS_TOKEN);
        } catch (error) {
            // Don't log to console to avoid showing error overlay
            throw error;
        }
    },

    // Remove refresh token
    removeRefreshToken: async () => {
        try {
            await AsyncStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
        } catch (error) {
            // Don't log to console to avoid showing error overlay
            throw error;
        }
    },

    // Clear all storage on logout
    clearStorage: async () => {
        try {
            await AsyncStorage.clear();
        } catch (error) {
            // Don't log to console to avoid showing error overlay
            throw error;
        }
    },
};