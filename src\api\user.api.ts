import axios from '../lib/axios';

const USER_ENDPOINT = '/users';

export interface SetInitialBalanceDto {
  initial_balance: number;
  currency?: string;
}

export interface InitialBalanceResponse {
  data: {
    initial_balance: number;
    currency: string;
    updated_at: string;
  };
}

export const UserAPI = {
  // Set initial balance for user
  setInitialBalance: async (balanceData: SetInitialBalanceDto): Promise<InitialBalanceResponse['data']> => {
    const response = await axios.post(`${USER_ENDPOINT}/initial-balance`, balanceData);
    return response.data.data; // Backend returns { data: { initial_balance, currency, updated_at } }
  },

  // Get user profile
  getProfile: async () => {
    const response = await axios.get(`${USER_ENDPOINT}/profile`);
    return response.data.data;
  },

  // Update user profile
  updateProfile: async (profileData: any) => {
    const response = await axios.patch(`${USER_ENDPOINT}/profile`, profileData);
    return response.data.data;
  },
};
