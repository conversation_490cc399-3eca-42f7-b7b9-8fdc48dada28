import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { IGoal, GoalStatus } from '../../../src/api/goals.api';
import theme from '../../theme';
import STRINGS from '../../constants/strings';

interface GoalCardProps {
  goal: IGoal;
  onPress?: () => void;
}

const GoalCard = ({ goal, onPress }: GoalCardProps) => {
  const calculateProgress = (current: number, target: number) => {
    return Math.min((current / target) * 100, 100);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(amount);
  };

  const getGoalIcon = (name: string) => {
    const iconMap: { [key: string]: string } = {
      [STRINGS.GOALS.BUY_HOUSE]: '🏠',
      [STRINGS.GOALS.BUY_CAR]: '🚗',
      [STRINGS.GOALS.TRAVEL]: '✈️',
      [STRINGS.GOALS.EDUCATION]: '🎓',
      [STRINGS.GOALS.WEDDING]: '💍',
      [STRINGS.GOALS.SAVINGS]: '💰',
      [STRINGS.GOALS.EMERGENCY_FUND]: '🚨',
      // Keep Vietnamese for backward compatibility
      'Mua nhà': '🏠',
      'Mua xe': '🚗',
      'Du lịch': '✈️',
      'Học tập': '🎓',
      'Đám cưới': '💍',
      'Tiết kiệm': '💰',
      'Quỹ khẩn cấp': '🚨',
    };
    return iconMap[name] || '✨';
  };

  const getStatusColor = (status: GoalStatus) => {
    const statusColors: { [key in GoalStatus]: string } = {
      [GoalStatus.ACTIVE]: theme.colors.primary[500],
      [GoalStatus.COMPLETED]: theme.colors.success,
      [GoalStatus.CANCELLED]: theme.colors.error,
      [GoalStatus.PAUSED]: theme.colors.warning || '#f59e0b',
      [GoalStatus.PENDING]: theme.colors.gray[500],
    };
    return statusColors[status] || theme.colors.gray[500];
  };

  const getStatusText = (status: GoalStatus) => {
    const statusTexts: { [key in GoalStatus]: string } = {
      [GoalStatus.ACTIVE]: STRINGS.GOALS.ACTIVE,
      [GoalStatus.COMPLETED]: STRINGS.GOALS.COMPLETED,
      [GoalStatus.CANCELLED]: STRINGS.GOALS.CANCELLED,
      [GoalStatus.PAUSED]: STRINGS.GOALS.PAUSED,
      [GoalStatus.PENDING]: STRINGS.GOALS.PENDING,
    };
    return statusTexts[status] || STRINGS.GOALS.UNKNOWN;
  };

  const progress = calculateProgress(goal.current_amount, goal.target_amount);

  return (
    <TouchableOpacity style={styles.container} onPress={onPress}>
      <View style={styles.header}>
        <View style={[styles.iconContainer, { backgroundColor: theme.colors.primary[100] }]}>
          <Text style={styles.icon}>{getGoalIcon(goal.name)}</Text>
        </View>
        <View style={styles.titleContainer}>
          <Text style={styles.title}>{goal.name}</Text>
          <View style={[styles.statusBadge, { backgroundColor: getStatusColor(goal.status) }]}>
            <Text style={styles.statusText}>{getStatusText(goal.status)}</Text>
          </View>
        </View>
      </View>

      <View style={styles.progressContainer}>
        <View style={[styles.progressBar, { width: `${progress}%` }]} />
      </View>

      <View style={styles.progressTextContainer}>
        <Text style={styles.progressPercentage}>{progress.toFixed(1)}%</Text>
        <Text style={styles.dueDate}>
          {STRINGS.DATE.DUE_ON} {new Date(goal.target_date).toLocaleDateString('en-US')}
        </Text>
      </View>

      <View style={styles.amountContainer}>
        <Text style={styles.currentAmount}>
          {formatCurrency(goal.current_amount)}
        </Text>
        <Text style={styles.targetAmount}>
          / {formatCurrency(goal.target_amount)}
        </Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: theme.colors.background.primary,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    shadowColor: theme.colors.shadow.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: theme.spacing.md,
  },
  icon: {
    fontSize: theme.typography.fontSizes.xl,
  },
  titleContainer: {
    flex: 1,
  },
  title: {
    fontSize: theme.typography.fontSizes.lg,
    fontWeight: '600',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.xs,
  },
  statusBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.full,
  },
  statusText: {
    fontSize: theme.typography.fontSizes.xs,
    color: theme.colors.background.primary,
    fontWeight: '500',
  },
  progressContainer: {
    height: 8,
    backgroundColor: theme.colors.gray[100],
    borderRadius: theme.borderRadius.sm,
    marginBottom: theme.spacing.xs,
  },
  progressBar: {
    height: 8,
    backgroundColor: theme.colors.primary[500],
    borderRadius: theme.borderRadius.sm,
  },
  progressTextContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: theme.spacing.md,
  },
  progressPercentage: {
    fontSize: theme.typography.fontSizes.sm,
    fontWeight: '600',
    color: theme.colors.primary[500],
  },
  dueDate: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
  },
  amountContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
  },
  currentAmount: {
    fontSize: theme.typography.fontSizes.lg,
    fontWeight: '600',
    color: theme.colors.text.primary,
    marginRight: theme.spacing.xs,
  },
  targetAmount: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.secondary,
  },
});

export default GoalCard;