{"name": "react-native-expo-nativewind-template", "version": "1.0.0", "description": "A cross-platform mobile app template built with React Native, Expo, NativeWind and TypeScript", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"@expo/metro-runtime": "~5.0.4", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/datetimepicker": "^8.3.0", "@react-navigation/bottom-tabs": "^7.3.13", "@react-navigation/drawer": "^7.3.12", "@react-navigation/native": "^7.1.9", "@react-navigation/native-stack": "^7.3.13", "@types/js-cookie": "^3.0.6", "@types/react-native": "^0.72.8", "axios": "^1.9.0", "date-fns": "^4.1.0", "expo": "~53.0.9", "expo-document-picker": "~13.1.5", "expo-file-system": "~18.1.10", "expo-font": "^13.3.1", "expo-image-picker": "^16.1.4", "expo-sharing": "~13.1.5", "expo-status-bar": "~2.2.3", "js-cookie": "^3.0.5", "nativewind": "^4.1.23", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.56.4", "react-native": "0.79.2", "react-native-chart-kit": "^6.12.0", "react-native-gesture-handler": "^2.24.0", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-paper": "^5.14.5", "react-native-reanimated": "^3.17.4", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "^4.10.0", "react-native-svg": "^15.11.2", "react-native-web": "^0.20.0", "tailwindcss": "^3.4.17"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}