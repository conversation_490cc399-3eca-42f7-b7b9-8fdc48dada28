import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  SafeAreaView,
  Alert,
  Modal,
  Platform,
} from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { StatusBar } from 'expo-status-bar';
import { RootStackParamList, AuthStackParamList } from '../navigation/types';
import theme from '../theme';
import Screen from '../components/common/Screen';
import DateTimePicker from '@react-native-community/datetimepicker';
import axios from 'axios';
import { registerUser } from '../../src/api/auth.api';
import { UserGender } from '../../src/types/user.types';

type RegisterScreenNavigationProp = NativeStackNavigationProp<
  AuthStackParamList & RootStackParamList,
  'Register'
>;

export type FormData = {
  username: string;
  email: string;
  phone: string;
  password: string;
  confirmPassword: string;
  firstName: string;
  lastName: string;
  birthDate: string;
  gender: UserGender | '';
};

const RegisterScreen = () => {
  const navigation = useNavigation<RegisterScreenNavigationProp>();
  const [step, setStep] = useState(1);
  const [formData, setFormData] = useState<FormData>({
    username: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: '',
    birthDate: '',
    gender: '',
  });
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);
  const [isConfirmPasswordVisible, setIsConfirmPasswordVisible] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [birthDate, setBirthDate] = useState(new Date());

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData({ ...formData, [field]: value });
  };

  const handleGenderSelect = (gender: UserGender) => {
    setFormData({ ...formData, gender });
  };

  const validateStep1 = () => {
    const { username, email, phone, password, confirmPassword } = formData;

    if (!username.trim()) {
      Alert.alert('Error', 'Please enter a username');
      return false;
    }

    if (username.length < 3) {
      Alert.alert('Error', 'Username must be at least 3 characters');
      return false;
    }

    if (!email) {
      Alert.alert('Error', 'Please enter an email');
      return false;
    }

    const emailRegex = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;
    if (!emailRegex.test(email)) {
      Alert.alert('Error', 'Invalid email format');
      return false;
    }

    if (!phone) {
      Alert.alert('Error', 'Please enter a phone number');
      return false;
    }

    if (!/^\d{10}$/.test(phone)) {
      Alert.alert('Error', 'Phone number must be 10 digits');
      return false;
    }

    if (!password) {
      Alert.alert('Error', 'Please enter a password');
      return false;
    }

    if (password.length < 6) {
      Alert.alert('Error', 'Password must be at least 6 characters');
      return false;
    }

    if (password !== confirmPassword) {
      Alert.alert('Error', 'Passwords do not match');
      return false;
    }

    return true;
  };

  const validateStep2 = () => {
    const { firstName, lastName, birthDate, gender } = formData;

    if (!firstName || !firstName.trim()) {
      Alert.alert('Error', 'Please enter your first name');
      return false;
    }

    if (!lastName || !lastName.trim()) {
      Alert.alert('Error', 'Please enter your last name');
      return false;
    }

    if (!birthDate) {
      Alert.alert('Error', 'Please select your birth date');
      return false;
    }

    if (!gender) {
      Alert.alert('Error', 'Please select your gender');
      return false;
    }

    return true;
  };

  const handleDateChange = (event: any, selectedDate?: Date) => {
    if (Platform.OS === 'android') {
      setShowDatePicker(false);
    }

    if (selectedDate) {
      setBirthDate(selectedDate);
      // Format date as YYYY-MM-DD for API, avoiding timezone issues
      const year = selectedDate.getFullYear();
      const month = String(selectedDate.getMonth() + 1).padStart(2, '0');
      const day = String(selectedDate.getDate()).padStart(2, '0');
      const formattedDate = `${year}-${month}-${day}`;
      handleInputChange('birthDate', formattedDate);
    }
  };

  const handleRegister = async () => {
    try {
      const result = await registerUser(formData);

      if (result.success) {
        Alert.alert('Registration Successful', 'Your account has been created successfully!', [
          {
            text: 'Continue',
            onPress: () => navigation.navigate('InitialGoalSetup'),
          },
        ]);
      } else {
        // Handle case where result.success is false
        Alert.alert(
          'Registration Failed',
          'Registration failed. Please change your username or email.',
          [{ text: 'OK', style: 'default' }]
        );
      }

    } catch (error: any) {
      // Determine error message based on error type
      let errorMessage = 'Registration failed. Please try again.';

      if (error.code === 'NETWORK_ERROR' || error.message?.includes('timeout')) {
        errorMessage = 'Network connection failed. Please check your internet connection and try again.';
      } else if (error.response?.status === 400) {
        errorMessage = 'Invalid registration data. Please check your information and try again.';
      } else if (error.response?.status === 409) {
        errorMessage = 'An account with this email or username already exists. Please use different credentials.';
      } else if (error.response?.status >= 500) {
        errorMessage = 'Server error. Please try again later.';
      } else if (error.message) {
        errorMessage = error.message;
      }

      Alert.alert(
        'Registration Failed',
        errorMessage,
        [
          {
            text: 'OK',
            style: 'default'
          }
        ]
      );
    }
  };

  const handleNextStep = () => {
    if (step === 1 && validateStep1()) {
      setStep(2);
    } else if (step === 2 && validateStep2()) {
      handleRegister();
    }
  };

  const renderStep1 = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Create an Account</Text>
      <Text style={styles.stepDescription}>Enter your account details to get started</Text>

      <TextInput
        style={styles.input}
        placeholder="Username"
        value={formData.username}
        onChangeText={(text) => handleInputChange('username', text)}
        autoCapitalize="none"
      />

      <TextInput
        style={styles.input}
        placeholder="Email"
        value={formData.email}
        onChangeText={(text) => handleInputChange('email', text)}
        keyboardType="email-address"
        autoCapitalize="none"
      />

      <TextInput
        style={styles.input}
        placeholder="Phone number"
        value={formData.phone}
        onChangeText={(text) => handleInputChange('phone', text)}
        keyboardType="phone-pad"
      />

      <View style={styles.passwordContainer}>
        <TextInput
          style={styles.passwordInput}
          placeholder="Password"
          value={formData.password}
          onChangeText={(text) => handleInputChange('password', text)}
          secureTextEntry={!isPasswordVisible}
        />
        <TouchableOpacity
          onPress={() => setIsPasswordVisible(!isPasswordVisible)}
          style={styles.visibilityToggle}
        >
          <Text>{isPasswordVisible ? '👁️' : '👁️‍🗨️'}</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.passwordContainer}>
        <TextInput
          style={styles.passwordInput}
          placeholder="Confirm password"
          value={formData.confirmPassword}
          onChangeText={(text) => handleInputChange('confirmPassword', text)}
          secureTextEntry={!isConfirmPasswordVisible}
        />
        <TouchableOpacity
          onPress={() => setIsConfirmPasswordVisible(!isConfirmPasswordVisible)}
          style={styles.visibilityToggle}
        >
          <Text>{isConfirmPasswordVisible ? '👁️' : '👁️‍🗨️'}</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderStep2 = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.stepTitle}>Personal Information</Text>
      <Text style={styles.stepDescription}>Help us learn more about you</Text>

      <TextInput
        style={styles.input}
        placeholder="First Name"
        value={formData.firstName}
        onChangeText={(text) => handleInputChange('firstName', text)}
      />

      <TextInput
        style={styles.input}
        placeholder="Last Name"
        value={formData.lastName}
        onChangeText={(text) => handleInputChange('lastName', text)}
      />

      <TouchableOpacity
        style={styles.datePickerButton}
        onPress={() => setShowDatePicker(true)}
      >
        <Text
          style={[
            styles.datePickerButtonText,
            formData.birthDate ? styles.datePickerValueText : null
          ]}
        >
          {formData.birthDate ? new Date(formData.birthDate).toLocaleDateString() : "Birth Date"}
        </Text>
      </TouchableOpacity>

      {showDatePicker && (
        <>
          {Platform.OS === 'ios' && (
            <View style={styles.iosDatePickerContainer}>
              <View style={styles.iosDatePickerHeader}>
                <TouchableOpacity
                  style={styles.iosDatePickerButton}
                  onPress={() => setShowDatePicker(false)}
                >
                  <Text style={styles.iosDatePickerButtonText}>Cancel</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.iosDatePickerButton}
                  onPress={() => setShowDatePicker(false)}
                >
                  <Text style={[styles.iosDatePickerButtonText, { color: theme.colors.primary[500] }]}>Done</Text>
                </TouchableOpacity>
              </View>

              <DateTimePicker
                value={birthDate}
                mode="date"
                display="spinner"
                onChange={handleDateChange}
                maximumDate={new Date()}
                minimumDate={new Date(1900, 0, 1)}
                style={styles.iosDatePicker}
              />
            </View>
          )}

          {Platform.OS === 'android' && (
            <DateTimePicker
              value={birthDate}
              mode="date"
              display="default"
              onChange={handleDateChange}
              maximumDate={new Date()}
              minimumDate={new Date(1900, 0, 1)}
            />
          )}
        </>
      )}

      <Text style={styles.labelText}>Gender</Text>
      <View style={styles.genderContainer}>
        <TouchableOpacity
          style={[
            styles.genderOption,
            formData.gender === UserGender.MALE && styles.selectedGender,
          ]}
          onPress={() => handleGenderSelect(UserGender.MALE)}
        >
          <Text
            style={[
              styles.genderText,
              formData.gender === UserGender.MALE && styles.selectedGenderText,
            ]}
          >
            Male
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.genderOption,
            formData.gender === UserGender.FEMALE && styles.selectedGender,
          ]}
          onPress={() => handleGenderSelect(UserGender.FEMALE)}
        >
          <Text
            style={[
              styles.genderText,
              formData.gender === UserGender.FEMALE && styles.selectedGenderText,
            ]}
          >
            Female
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.genderOption,
            formData.gender === UserGender.OTHER && styles.selectedGender,
          ]}
          onPress={() => handleGenderSelect(UserGender.OTHER)}
        >
          <Text
            style={[
              styles.genderText,
              formData.gender === UserGender.OTHER && styles.selectedGenderText,
            ]}
          >
            Other
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <Screen>
      <StatusBar style="dark" />
      <KeyboardAwareScrollView
        style={styles.container}
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
        enableOnAndroid={true}
        enableAutomaticScroll={true}
        extraScrollHeight={20}
        extraHeight={100}
      >
          {/* Progress indicator */}
          <View style={styles.progressContainer}>
            <View style={styles.progressStepContainer}>
              <View
                style={[
                  styles.progressStep,
                  { backgroundColor: step >= 1 ? theme.colors.primary[500] : theme.colors.gray[200] },
                ]}
              >
                <Text style={styles.progressStepText}>1</Text>
              </View>
              <Text style={styles.progressLabel}>Account</Text>
            </View>

            <View style={styles.progressLine} />

            <View style={styles.progressStepContainer}>
              <View
                style={[
                  styles.progressStep,
                  { backgroundColor: step >= 2 ? theme.colors.primary[500] : theme.colors.gray[200] },
                ]}
              >
                <Text style={styles.progressStepText}>2</Text>
              </View>
              <Text style={styles.progressLabel}>Personal</Text>
            </View>
          </View>

          {step === 1 ? renderStep1() : renderStep2()}

          <View style={styles.buttonsContainer}>
            {step === 2 && (
              <TouchableOpacity
                style={styles.backButton}
                onPress={() => setStep(1)}
              >
                <Text style={styles.backButtonText}>Back</Text>
              </TouchableOpacity>
            )}

            <TouchableOpacity
              style={styles.nextButton}
              onPress={handleNextStep}
            >
              <Text style={styles.nextButtonText}>
                {step === 1 ? 'Continue' : 'Complete'}
              </Text>
            </TouchableOpacity>
          </View>

          <View style={styles.loginLinkContainer}>
            <Text style={styles.loginText}>Already have an account? </Text>
            <TouchableOpacity onPress={() => navigation.navigate('Login')}>
              <Text style={styles.loginLink}>Log in</Text>
            </TouchableOpacity>
          </View>
      </KeyboardAwareScrollView>
    </Screen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background.primary,
  },
  scrollContainer: {
    flexGrow: 1,
    paddingHorizontal: theme.spacing.xl,
    paddingBottom: theme.spacing.xl,
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: theme.spacing.xl,
    marginBottom: theme.spacing.xl * 1.5,
  },
  progressStepContainer: {
    alignItems: 'center',
  },
  progressStep: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  progressStepText: {
    color: theme.colors.background.primary,
    fontWeight: '600',
  },
  progressLabel: {
    marginTop: theme.spacing.sm,
    fontSize: theme.typography.fontSizes.xs,
    color: theme.colors.text.secondary,
  },
  progressLine: {
    flex: 1,
    height: 2,
    backgroundColor: theme.colors.gray[200],
    marginHorizontal: theme.spacing.md,
  },
  stepContainer: {
    marginBottom: theme.spacing.xl,
  },
  stepTitle: {
    fontSize: theme.typography.fontSizes['2xl'],
    fontWeight: '700',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.sm,
  },
  stepDescription: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
    marginBottom: theme.spacing.xl,
  },
  input: {
    backgroundColor: theme.colors.gray[100],
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.md,
    fontSize: theme.typography.fontSizes.md,
  },
  passwordContainer: {
    flexDirection: 'row',
    backgroundColor: theme.colors.gray[100],
    borderRadius: theme.borderRadius.md,
    marginBottom: theme.spacing.md,
  },
  passwordInput: {
    flex: 1,
    padding: theme.spacing.md,
    fontSize: theme.typography.fontSizes.md,
  },
  visibilityToggle: {
    justifyContent: 'center',
    paddingHorizontal: theme.spacing.md,
  },
  labelText: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '500',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.sm,
  },
  genderContainer: {
    flexDirection: 'row',
    marginBottom: theme.spacing.md,
  },
  genderOption: {
    flex: 1,
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.sm,
    borderWidth: 1,
    borderColor: theme.colors.gray[200],
    borderRadius: theme.borderRadius.md,
    marginRight: theme.spacing.sm,
    alignItems: 'center',
  },
  selectedGender: {
    backgroundColor: theme.colors.primary[100],
    borderColor: theme.colors.primary[500],
  },
  genderText: {
    color: theme.colors.text.secondary,
  },
  selectedGenderText: {
    color: theme.colors.primary[500],
    fontWeight: '500',
  },
  buttonsContainer: {
    flexDirection: 'row',
    marginBottom: theme.spacing.xl,
  },
  backButton: {
    flex: 1,
    backgroundColor: theme.colors.background.primary,
    borderWidth: 1,
    borderColor: theme.colors.gray[200],
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.md,
    alignItems: 'center',
    marginRight: theme.spacing.sm,
  },
  backButtonText: {
    color: theme.colors.text.secondary,
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '500',
  },
  nextButton: {
    flex: 1,
    backgroundColor: theme.colors.primary[500],
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.md,
    alignItems: 'center',
  },
  nextButtonText: {
    color: theme.colors.background.primary,
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '600',
  },
  loginLinkContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  loginText: {
    color: theme.colors.text.secondary,
    fontSize: theme.typography.fontSizes.sm,
  },
  loginLink: {
    color: theme.colors.primary[500],
    fontSize: theme.typography.fontSizes.sm,
    fontWeight: '500',
  },
  datePickerButton: {
    backgroundColor: theme.colors.gray[100],
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.md,
  },
  datePickerButtonText: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.secondary,
  },
  datePickerValueText: {
    color: theme.colors.text.primary,
  },
  iosDatePickerContainer: {
    backgroundColor: theme.colors.background.primary,
    borderRadius: theme.borderRadius.lg,
    marginBottom: theme.spacing.md,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: theme.colors.gray[200],
  },
  iosDatePickerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray[200],
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.md,
  },
  iosDatePickerButton: {
    padding: theme.spacing.sm,
  },
  iosDatePickerButtonText: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.primary,
  },
  iosDatePicker: {
    height: 200,
  },
});

export default RegisterScreen;