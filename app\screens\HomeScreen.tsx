import React, { useState, useEffect } from 'react';
import {
  ScrollView,
  Text,
  View,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  ActivityIndicator,
  Alert,
  RefreshControl,
} from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

import theme from '../theme';
import Screen from '../components/common/Screen';
import DrawerHeader from '../components/common/DrawerHeader';
import Card from '../components/ui/Card';
import Button from '../components/ui/Button';
import { RootStackParamList } from '../navigation/types';
import STRINGS from '../constants/strings';
import { UserAPI } from '../../src/api/user.api';
import { GoalsAPI, IGoal } from '../../src/api/goals.api';
import { UserProfile } from '../../src/types/user.types';
import { TransactionAPI } from '../../src/api/transaction.api';
import { Transaction, TransactionType } from '../types/transaction';

const { width } = Dimensions.get('window');

type HomeScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;

// Extended user profile interface with balance information
interface ExtendedUserProfile extends UserProfile {
  initial_balance?: number;
  currency?: string;
  isInitialBalance?: boolean;
}

// Helper function to get goal icon based on name
const getGoalIcon = (name: string) => {
  const iconMap: { [key: string]: string } = {
    'Buy House': '🏠',
    'Buy Car': '�',
    'Travel': '✈️',
    'Education': '🎓',
    'Wedding': '💍',
    'Emergency Fund': '�',
    'Savings': '💰',
  };
  return iconMap[name] || '🎯';
};

const HomeScreen = () => {
  const navigation = useNavigation<HomeScreenNavigationProp>();

  // State management
  const [userProfile, setUserProfile] = useState<ExtendedUserProfile | null>(null);
  const [goals, setGoals] = useState<IGoal[]>([]);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Calculate balance from user profile
  const balance = userProfile?.initial_balance || 0;
  const showSetupBalanceCard = !userProfile?.isInitialBalance;

  // Load user profile and goals
  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch user profile
      const profileResponse = await UserAPI.getProfile();
      setUserProfile(profileResponse);

      // Fetch goals (limit to 5 for home screen)
      const goalsResponse = await GoalsAPI.getGoals({ limit: 5, status: 'active' });
      setGoals(goalsResponse.data || []);

      // Fetch recent transactions (limit to 5 for home screen)
      try {
        const transactionsResponse = await TransactionAPI.getAll({
          page: 1,
          limit: 5,
          sort_by: 'transaction_date',
          sort_order: 'DESC'
        });
        setTransactions(transactionsResponse.data || []);
      } catch (transactionError) {
        console.error('Error loading transactions:', transactionError);
        // Don't fail the whole load if transactions fail
        setTransactions([]);
      }

    } catch (error: any) {
      let errorMessage = 'Failed to load data. Please try again.';

      if (error.code === 'NETWORK_ERROR' || error.message?.includes('timeout')) {
        errorMessage = 'Network connection failed. Please check your internet connection and try again.';
      } else if (error.response?.status === 401) {
        errorMessage = 'Authentication failed. Please login again.';
      } else if (error.response?.status >= 500) {
        errorMessage = 'Server error. Please try again later.';
      } else if (error.message) {
        errorMessage = error.message;
      }

      setError(errorMessage);
      Alert.alert('Error', errorMessage);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Refresh data
  const handleRefresh = () => {
    setRefreshing(true);
    loadData();
  };

  // Load data on component mount and when screen is focused
  useEffect(() => {
    loadData();
  }, []);

  useFocusEffect(
    React.useCallback(() => {
      // Reload data when screen comes into focus (e.g., after setting initial balance)
      loadData();
    }, [])
  );

  const handleAddTransaction = () => {
    navigation.navigate('CreateTransaction');
  };

  const handleViewGoal = (goalId: string) => {
    navigation.navigate('GoalDetail', { id: goalId });
  };



  const handleSetupBalance = () => {
    navigation.navigate('InitialBalance');
  };

  const dismissSetupBalanceCard = () => {
    // Update user profile to mark that setup balance card was dismissed
    if (userProfile) {
      setUserProfile({
        ...userProfile,
        isInitialBalance: true, // Mark as dismissed
      });
    }
  };

  const calculateProgress = (current: number, target: number) => {
    const progress = (current / target) * 100;
    return Math.min(progress, 100); // Cap at 100%
  };

  const formatCurrency = (amount: number) => {
    const currency = userProfile?.currency || 'VND';
    return new Intl.NumberFormat(
      currency === 'VND' ? 'vi-VN' : 'en-US',
      {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: currency === 'VND' ? 0 : 2,
      }
    ).format(amount);
  };

  // Show loading state
  if (loading && !userProfile) {
    return (
      <Screen>
        <DrawerHeader title="Dashboard" />
        <View style={[styles.container, { justifyContent: 'center', alignItems: 'center' }]}>
          <ActivityIndicator size="large" color={theme.colors.primary[500]} />
          <Text style={{ marginTop: theme.spacing.md, color: theme.colors.text.secondary }}>
            Loading your dashboard...
          </Text>
        </View>
      </Screen>
    );
  }

  return (
    <Screen>
      <DrawerHeader title="Dashboard" />
      <ScrollView
        style={styles.container}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
      >
        {/* Header Section */}
        <View style={styles.header}>
          <Text style={styles.greeting}>{STRINGS.HOME.HELLO}</Text>
          <Text style={styles.heading}>{STRINGS.HOME.DASHBOARD}</Text>
        </View>

        {/* Setup Balance Card (Optional) */}
        {showSetupBalanceCard && (
          <Card style={styles.setupBalanceCard}>
            <TouchableOpacity
              style={styles.dismissButton}
              onPress={dismissSetupBalanceCard}
            >
              <Text style={styles.dismissButtonText}>✕</Text>
            </TouchableOpacity>
            <Text style={styles.setupBalanceTitle}>
              Setup Initial Balance?
            </Text>
            <Text style={styles.setupBalanceDescription}>
              Set up your current balance to start tracking your finances effectively
            </Text>
            <Button
              title="Setup Now"
              onPress={handleSetupBalance}
              variant="primary"
            />
          </Card>
        )}

        {/* Balance Card */}
        {userProfile?.isInitialBalance && (
          <Card style={styles.balanceCard}>
            <View style={styles.balanceSummary}>
              <View style={styles.balanceItem}>
                <Text style={styles.balanceIcon}>💰</Text>
                <Text style={styles.balanceLabel}>Current Balance</Text>
                <Text style={styles.balanceAmount}>{formatCurrency(balance)}</Text>
              </View>
            </View>
          </Card>
        )}

        {/* Goals Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Your Goals</Text>
            <TouchableOpacity onPress={() => navigation.navigate('Goals')}>
              <Text style={styles.viewAllText}>View All</Text>
            </TouchableOpacity>
          </View>

          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.goalsContainer}
          >
            {goals.map((goal) => (
              <TouchableOpacity
                key={goal.id}
                style={styles.goalCard}
                onPress={() => handleViewGoal(goal.id)}
              >
                <Text style={styles.goalIcon}>{getGoalIcon(goal.name)}</Text>
                <Text style={styles.goalTitle} numberOfLines={1}>{goal.name}</Text>

                <View style={styles.goalAmounts}>
                  <Text style={styles.goalCurrentAmount}>
                    {formatCurrency(goal.current_amount)}
                  </Text>
                  <Text style={styles.goalTargetAmount}>
                    / {formatCurrency(goal.target_amount)}
                  </Text>
                </View>

                <View style={styles.goalProgressContainer}>
                  <View
                    style={[
                      styles.goalProgress,
                      {width: `${calculateProgress(goal.current_amount, goal.target_amount)}%`}
                    ]}
                  />
                </View>

                <Text style={styles.goalDate}>
                  Due: {new Date(goal.target_date).toLocaleDateString()}
                </Text>
              </TouchableOpacity>
            ))}

            <TouchableOpacity
              style={styles.addGoalCard}
              onPress={() => navigation.navigate('AddGoal')}
            >
              <View style={styles.addGoalButton}>
                <Text style={styles.addGoalIcon}>+</Text>
              </View>
              <Text style={styles.addGoalText}>Add New Goal</Text>
            </TouchableOpacity>
          </ScrollView>
        </View>

        {/* Recent Transactions Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Recent Transactions</Text>
            <TouchableOpacity onPress={() => navigation.navigate('Transactions')}>
              <Text style={styles.viewAllText}>View All</Text>
            </TouchableOpacity>
          </View>

          {transactions.length === 0 ? (
            <View style={styles.emptyTransactions}>
              <Text style={styles.emptyTransactionsText}>
                No recent transactions yet
              </Text>
              <Text style={styles.emptyTransactionsSubtext}>
                Start by adding your first transaction
              </Text>
            </View>
          ) : (
            <Card style={styles.transactionsCard}>
              {transactions.map((transaction, index) => (
                <TouchableOpacity
                  key={transaction.id}
                  style={[
                    styles.transactionItem,
                    index === transactions.length - 1 && { borderBottomWidth: 0 }
                  ]}
                  onPress={() => navigation.navigate('TransactionDetail', { id: transaction.id })}
                >
                  <View style={styles.transactionLeft}>
                    <View style={styles.transactionIcon}>
                      <Text style={styles.transactionIconText}>
                        {transaction.category?.icon || (transaction.type === TransactionType.INCOME ? '💰' : '💸')}
                      </Text>
                    </View>
                    <View style={styles.transactionInfo}>
                      <Text style={styles.transactionDescription}>
                        {transaction.description || transaction.category?.name || 'Transaction'}
                      </Text>
                      <Text style={styles.transactionDate}>
                        {new Date(transaction.transaction_date).toLocaleDateString('en-US')}
                      </Text>
                    </View>
                  </View>
                  <Text style={[
                    styles.transactionAmount,
                    transaction.type === TransactionType.INCOME ? styles.incomeAmount : styles.expenseAmount
                  ]}>
                    {transaction.type === TransactionType.INCOME ? '+' : '-'} {formatCurrency(transaction.amount)}
                  </Text>
                </TouchableOpacity>
              ))}
            </Card>
          )}

          <Button
            title="+ Add New Transaction"
            onPress={handleAddTransaction}
            variant="outline"
            fullWidth
            style={styles.addTransactionButton}
          />
        </View>
      </ScrollView>
    </Screen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: theme.spacing.md,
    paddingTop: theme.spacing.lg,
    paddingBottom: theme.spacing.sm,
  },
  greeting: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.secondary,
  },
  heading: {
    fontSize: theme.typography.fontSizes['2xl'],
    fontWeight: '700',
    color: theme.colors.text.primary,
    marginTop: theme.spacing.xs,
  },
  setupBalanceCard: {
    margin: theme.spacing.md,
    backgroundColor: theme.colors.primary[50],
    padding: theme.spacing.md,
  },
  dismissButton: {
    position: 'absolute',
    right: theme.spacing.sm,
    top: theme.spacing.sm,
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 1,
  },
  dismissButtonText: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.secondary,
  },
  setupBalanceTitle: {
    fontSize: theme.typography.fontSizes.lg,
    fontWeight: '600',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.xs,
  },
  setupBalanceDescription: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
    marginBottom: theme.spacing.md,
  },
  balanceCard: {
    margin: theme.spacing.md,
  },
  balanceSummary: {
    padding: theme.spacing.sm,
  },
  balanceItem: {
    alignItems: 'center',
  },
  balanceLabel: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
    marginBottom: theme.spacing.xs,
  },
  balanceIcon: {
    fontSize: theme.typography.fontSizes.xl,
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.xs,
  },
  balanceAmount: {
    fontSize: theme.typography.fontSizes.xl,
    fontWeight: '700',
    color: theme.colors.text.primary,
  },
  balanceDivider: {
    height: 1,
    backgroundColor: theme.colors.gray[200],
    marginVertical: theme.spacing.md,
  },
  balanceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  incomeText: {
    color: theme.colors.success,
  },
  expenseText: {
    color: theme.colors.error,
  },
  section: {
    marginVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.md,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  sectionTitle: {
    fontSize: theme.typography.fontSizes.lg,
    fontWeight: '600',
    color: theme.colors.text.primary,
  },
  viewAllText: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.primary[500],
  },
  goalsContainer: {
    paddingBottom: theme.spacing.sm,
  },
  goalCard: {
    backgroundColor: theme.colors.background.primary,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    marginRight: theme.spacing.md,
    width: width * 0.7,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  goalIcon: {
    fontSize: 28,
    marginBottom: theme.spacing.sm,
  },
  goalTitle: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '600',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.xs,
  },
  goalAmounts: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginBottom: theme.spacing.xs,
  },
  goalCurrentAmount: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '700',
    color: theme.colors.text.primary,
  },
  goalTargetAmount: {
    fontSize: theme.typography.fontSizes.xs,
    color: theme.colors.text.secondary,
    marginLeft: theme.spacing.xs,
  },
  goalProgressContainer: {
    height: 6,
    backgroundColor: theme.colors.gray[200],
    borderRadius: theme.borderRadius.full,
    marginBottom: theme.spacing.sm,
    overflow: 'hidden',
  },
  goalProgress: {
    height: '100%',
    backgroundColor: theme.colors.primary[500],
    borderRadius: theme.borderRadius.full,
  },
  goalDate: {
    fontSize: theme.typography.fontSizes.xs,
    color: theme.colors.text.secondary,
  },
  addGoalCard: {
    backgroundColor: theme.colors.background.primary,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    marginRight: theme.spacing.md,
    width: width * 0.4,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: theme.colors.gray[200],
    borderStyle: 'dashed',
  },
  addGoalButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: theme.colors.primary[100],
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: theme.spacing.sm,
  },
  addGoalIcon: {
    fontSize: 24,
    color: theme.colors.primary[500],
    fontWeight: '300',
  },
  addGoalText: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.primary[500],
    textAlign: 'center',
  },
  transactionsCard: {
    marginBottom: theme.spacing.md,
  },
  transactionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: theme.spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray[100],
  },
  transactionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  transactionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.gray[100],
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: theme.spacing.md,
  },
  transactionIconText: {
    fontSize: 20,
  },
  transactionInfo: {
    flex: 1,
  },
  transactionCategory: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '500',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.xs / 2,
  },
  transactionDescription: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
  },
  transactionAmount: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '600',
    textAlign: 'right',
  },
  incomeAmount: {
    color: theme.colors.success,
  },
  expenseAmount: {
    color: theme.colors.error,
  },
  transactionAmountText: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '600',
    marginBottom: theme.spacing.xs / 2,
  },
  transactionDate: {
    fontSize: theme.typography.fontSizes.xs,
    color: theme.colors.text.secondary,
  },
  addTransactionButton: {
    marginTop: theme.spacing.md,
  },
  emptyTransactions: {
    alignItems: 'center',
    paddingVertical: theme.spacing.xl,
    paddingHorizontal: theme.spacing.md,
  },
  emptyTransactionsText: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '500',
    color: theme.colors.text.secondary,
    marginBottom: theme.spacing.xs,
  },
  emptyTransactionsSubtext: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
    textAlign: 'center',
  },
});

export default HomeScreen;