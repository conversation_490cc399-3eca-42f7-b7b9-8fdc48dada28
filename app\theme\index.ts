import { Platform } from 'react-native';

/**
 * App color palette
 */
export const colors = {
  primary: {
    50: '#f0f9ff',
    100: '#e0f2fe',
    200: '#bae6fd',
    300: '#7dd3fc',
    400: '#38bdf8',
    500: '#0ea5e9',
    600: '#0284c7',
    700: '#0369a1',
    800: '#075985',
    900: '#0c4a6e',
  },
  secondary: {
    50: '#f5f3ff',
    100: '#ede9fe',
    200: '#ddd6fe',
    300: '#c4b5fd',
    400: '#a78bfa',
    500: '#8b5cf6',
    600: '#7c3aed',
    700: '#6d28d9',
    800: '#5b21b6',
    900: '#4c1d95',
  },
  gray: {
    50: '#f9fafb',
    100: '#f3f4f6',
    200: '#e5e7eb',
    300: '#d1d5db',
    400: '#9ca3af',
    500: '#6b7280',
    600: '#4b5563',
    700: '#374151',
    800: '#1f2937',
    900: '#111827',
  },
  success: '#10b981',
  warning: '#f59e0b',
  error: '#ef4444',
  background: {
    primary: '#ffffff',
    secondary: '#f9fafb',
    tertiary: '#f3f4f6',
  },
  text: {
    primary: '#111827',
    secondary: '#6B7280',
    inverse: '#ffffff',
    link: '#0ea5e9',
  },
  shadow: {
    primary: '#000000',
    secondary: '#000000',
    tertiary: '#000000',
  }
};

/**
 * Spacing system
 */
export const spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  '2xl': 48,
  '3xl': 64,
  '4xl': 120,
};

/**
 * Border radius styles
 */
export const borderRadius = {
  none: 0,
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  '2xl': 24,
  full: 9999,
  xs : 2,
};

/**
 * Typography system
 */
export const typography = {
  fonts: {
    primary: Platform.select({
      ios: 'System',
      android: 'Roboto',
      default: 'System',
    }),
    heading: Platform.select({
      ios: 'System',
      android: 'Roboto',
      default: 'System',
    })
  },
  fontSizes: {
    xs: 12,
    sm: 14,
    md: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
    '3xl': 30,
    '4xl': 36,
  },
  fontWeights: {
    // Use numeric values that React Native accepts
    normal: 'normal' as const,
    medium: 500 as const,
    semibold: 600 as const,
    bold: 'bold' as const,
  },
  lineHeights: {
    tight: 1.25,
    normal: 1.5,
    relaxed: 1.75,
  },
};

/**
 * Button theme styles
 */
export const buttons = {
  primary: {
    backgroundColor: colors.primary[500],
    textColor: '#ffffff',
    borderRadius: borderRadius.md,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
  },
  secondary: {
    backgroundColor: colors.gray[100],
    textColor: colors.gray[900],
    borderRadius: borderRadius.md,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
  },
  outline: {
    backgroundColor: 'transparent',
    borderColor: colors.primary[500],
    borderWidth: 1,
    textColor: colors.primary[500],
    borderRadius: borderRadius.md,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
  },
  danger: {
    backgroundColor: colors.error,
    textColor: '#ffffff',
    borderRadius: borderRadius.md,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
  }
};

/**
 * Card theme styles
 */
export const cards = {
  primary: {
    backgroundColor: colors.background.primary,
    borderRadius: borderRadius.lg,
    padding: spacing.md,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  }
};

/**
 * Input theme styles
 */
export const inputs = {
  default: {
    backgroundColor: colors.gray[100],
    borderRadius: borderRadius.md,
    padding: spacing.md,
    fontSize: typography.fontSizes.md,
    color: colors.text.primary,
  },
  focus: {
    borderColor: colors.primary[500],
    borderWidth: 1,
  }
};

/**
 * Icon theme settings
 */
export const icons = {
  size: {
    sm: 16,
    md: 24,
    lg: 32,
    xl: 48,
  },
  color: {
    primary: colors.primary[500],
    secondary: colors.gray[500],
    inactive: colors.gray[400],
    active: colors.primary[500],
  }
};

/**
 * Shadow styles
 */
export const shadows = {
  sm: {
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 2,
  },
  md: {
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  lg: {
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 6,
  },
};

/**
 * Complete theme object
 */
const theme = {
  colors,
  spacing,
  borderRadius,
  typography,
  buttons,
  cards,
  inputs,
  icons,
  shadows,
};

export default theme; 