# Tài Liệu Dự Án: Ứng Dụng Quản Lý Tài Chính Cá Nhân

## Tổng Quan

Đây là một ứng dụng quản lý tài chính cá nhân, đ<PERSON><PERSON><PERSON> phát triển với mục đích giúp người dùng theo dõi thu chi, quản lý ngân sách, đặt ra các mục tiêu tài chính và phân tích dữ liệu tài chính của họ.

## Công Nghệ Sử Dụng

### Framework
- **React Native**: Sử dụng để xây dựng ứng dụng đa nền tảng (iOS và Android)
- **Expo**: Một framework giúp đơn giản hóa quy trình phát triển React Native với các API xây dựng sẵn
- **TypeScript**: Ngôn ngữ lập trình mạnh mẽ hơn JavaScript với tính năng kiểm tra kiểu dữ liệu

### Thư Viện Ch<PERSON>h
- **React Navigation 7**: <PERSON>u<PERSON>n lý điều hướng giữa các màn hình với Bottom Tab Navigation và Stack Navigation
- **NativeWind**: Sử dụng Tailwind CSS trong React Native để styling
- **React Hook Form**: Quản lý và xác thực form
- **React Native Keyboard Aware Scroll View**: Xử lý bàn phím và scrolling

## Thiết Kế Hệ Thống

### Kiến Trúc
Dự án được tổ chức theo cấu trúc thư mục rõ ràng, phân tách các thành phần:
- `/app`: Thư mục chính chứa mã nguồn ứng dụng
  - `/components`: Các thành phần UI có thể tái sử dụng
    - `/common`: Các component cơ bản (Screen, KeyboardAwareView)
    - `/ui`: Các thành phần UI (Button, Card, TextInput, FormInput)
  - `/navigation`: Cấu hình điều hướng và định nghĩa các loại màn hình
  - `/screens`: Tất cả các màn hình của ứng dụng
  - `/theme`: Cấu hình giao diện, bao gồm màu sắc, kích thước và typography
  - `/utils`: Các hàm tiện ích
  - `/hooks`: Custom hooks

### Hệ Thống Màu Sắc
- **Màu chính (Primary)**: Các tông màu xanh lam (#0ea5e9)
- **Màu phụ (Secondary)**: Các tông màu tím (#8b5cf6)
- **Màu thành công**: #10b981
- **Màu cảnh báo**: #f59e0b
- **Màu lỗi**: #ef4444
- **Nền**: #ffffff

### Typography
- **Font size**: Từ xs (12px) đến 4xl (36px)
- **Font weight**: normal (400), medium (500), semibold (600), bold (700)
- **Line height**: tight (1.25), normal (1.5), relaxed (1.75)

## Layout Chung

### Cấu Trúc Điều Hướng

#### 1. Điều Hướng Gốc (RootNavigator)
- **Auth**: Stack điều hướng cho các màn hình xác thực người dùng
- **Main**: Bottom Tab điều hướng cho các màn hình chính
- Các màn hình chi tiết khác được thêm vào Stack chính

#### 2. Điều Hướng Xác Thực (AuthNavigator)
- **Login**: Màn hình đăng nhập
- **Register**: Màn hình đăng ký
- **ForgotPassword**: Màn hình quên mật khẩu

#### 3. Điều Hướng Tab (TabNavigator)
- **Home**: Màn hình trang chủ (Dashboard)
- **Goals**: Quản lý các mục tiêu tài chính
- **Transactions**: Quản lý các giao dịch thu chi
- **Budget**: Quản lý ngân sách
- **Analytics**: Phân tích dữ liệu tài chính
- **More**: Các tùy chọn khác

### Component Chung
- **Screen**: Component bọc cho màn hình, quản lý SafeAreaView và StatusBar
- **KeyboardAwareView**: Xử lý bàn phím khi điền form
- **Button**: Nút bấm với nhiều biến thể
- **Card**: Thành phần hiển thị nội dung dạng thẻ
- **TextInput**: Ô nhập liệu
- **FormInput**: TextInput với label và xác thực

## Màn Hình Hiện Có

### 1. Xác Thực
- **LoginScreen**: Đăng nhập vào ứng dụng
- **RegisterScreen**: Đăng ký tài khoản mới
- **ForgotPasswordScreen**: Lấy lại mật khẩu

### 2. Onboarding
- **InitialBalanceScreen**: Thiết lập số dư ban đầu
- **GoalSetupScreen**: Thiết lập mục tiêu tài chính ban đầu

### 3. Trang Chủ
- **HomeScreen**: Hiển thị tổng quan tài chính, số dư, mục tiêu và các giao dịch gần đây

### 4. Mục Tiêu Tài Chính
- **GoalsScreen**: Danh sách các mục tiêu tài chính
- **GoalDetailScreen**: Chi tiết một mục tiêu
- **AddGoalScreen**: Thêm mục tiêu mới

### 5. Giao Dịch
- **TransactionsScreen**: Danh sách các giao dịch thu chi
- **TransactionDetailScreen**: Chi tiết một giao dịch
- **AddTransactionScreen**: Thêm giao dịch mới

### 6. Ngân Sách
- **BudgetScreen**: Tổng quan ngân sách
- **BudgetDetailScreen**: Chi tiết ngân sách theo danh mục
- **SetupBudgetScreen**: Thiết lập ngân sách mới

### 7. Danh Mục
- **CategoryDetailScreen**: Chi tiết danh mục chi tiêu
- **AddCategoryScreen**: Thêm danh mục mới

### 8. Phân Tích
- **AnalyticsScreen**: Biểu đồ và phân tích chi tiêu
- **ChartDetailScreen**: Chi tiết biểu đồ phân tích

### 9. Khác
- **MoreScreen**: Các tùy chọn bổ sung, cài đặt
- **SettingsScreen**: Cài đặt ứng dụng
- **ProfileScreen**: Thông tin người dùng
- **FormScreen**: Mẫu form dùng chung

## Đặc Điểm Nổi Bật
- **Bottom Tab Navigation**: Thanh điều hướng ở dưới với các biểu tượng (emoji) dễ nhận biết
- **Thiết kế hiện đại**: Sử dụng màu sắc nhất quán, shadow, border-radius
- **Đa ngôn ngữ**: Giao diện hiện tại đang sử dụng tiếng Việt
- **Thông báo trạng thái**: Sử dụng các thanh trạng thái và hiệu ứng thị giác
- **Responsive**: Thiết kế đáp ứng cho nhiều kích thước màn hình khác nhau

## Cấu Hình Chung

### NativeWind/Tailwind
- Được cấu hình trong `tailwind.config.js`
- Mở rộng theme với các màu sắc, spacing, border-radius, và font

### Metro Bundler
- Metro được cấu hình trong `metro.config.js`

### Babel
- Babel được cấu hình trong `babel.config.js`

### Expo
- Cấu hình Expo trong `app.json` 