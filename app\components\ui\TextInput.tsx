import React, { useState } from 'react';
import { 
  View, 
  TextInput as RNTextInput, 
  Text, 
  StyleSheet, 
  TouchableOpacity,
  TextInputProps as RNTextInputProps
} from 'react-native';
import theme from '../../theme';

interface TextInputProps extends RNTextInputProps {
  label?: string;
  error?: string;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  secureTextEntry?: boolean;
  onIconPress?: () => void;
  containerStyle?: any;
  inputStyle?: any;
}

const TextInput: React.FC<TextInputProps> = ({
  label,
  error,
  icon,
  iconPosition = 'right',
  secureTextEntry,
  onIconPress,
  containerStyle,
  inputStyle,
  ...rest
}) => {
  const [isFocused, setIsFocused] = useState(false);

  return (
    <View style={[styles.container, containerStyle]}>
      {label && <Text style={styles.label}>{label}</Text>}
      
      <View style={[
        styles.inputContainer, 
        isFocused && styles.focused,
        error && styles.errorInput,
      ]}>
        {icon && iconPosition === 'left' && (
          <TouchableOpacity 
            onPress={onIconPress} 
            disabled={!onIconPress}
            style={styles.iconLeft}
          >
            {icon}
          </TouchableOpacity>
        )}
        
        <RNTextInput
          style={[
            styles.input,
            icon && iconPosition === 'left' && { paddingLeft: theme.spacing.md * 2 },
            icon && iconPosition === 'right' && { paddingRight: theme.spacing.md * 2 },
            inputStyle,
          ]}
          secureTextEntry={secureTextEntry}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          placeholderTextColor={theme.colors.gray[400]}
          {...rest}
        />
        
        {icon && iconPosition === 'right' && (
          <TouchableOpacity 
            onPress={onIconPress}
            disabled={!onIconPress}
            style={styles.iconRight}
          >
            {icon}
          </TouchableOpacity>
        )}
      </View>
      
      {error && <Text style={styles.errorText}>{error}</Text>}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: theme.spacing.md,
  },
  label: {
    marginBottom: theme.spacing.xs,
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.primary,
    fontWeight: '500',
  },
  inputContainer: {
    backgroundColor: theme.inputs.default.backgroundColor,
    borderRadius: theme.inputs.default.borderRadius,
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
    borderWidth: 1,
    borderColor: 'transparent',
  },
  input: {
    padding: theme.inputs.default.padding,
    fontSize: theme.inputs.default.fontSize,
    color: theme.inputs.default.color,
    flex: 1,
  },
  focused: {
    borderColor: theme.inputs.focus.borderColor,
    borderWidth: theme.inputs.focus.borderWidth,
  },
  errorInput: {
    borderColor: theme.colors.error,
    borderWidth: 1,
  },
  iconLeft: {
    position: 'absolute',
    left: theme.spacing.md,
    zIndex: 1,
  },
  iconRight: {
    position: 'absolute',
    right: theme.spacing.md,
    zIndex: 1,
  },
  errorText: {
    color: theme.colors.error,
    fontSize: theme.typography.fontSizes.xs,
    marginTop: theme.spacing.xs,
  }
});

export default TextInput; 