import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Image,
  Dimensions,
  Platform,
  KeyboardAvoidingView,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation/types';
import theme from '../theme';
import Screen from '../components/common/Screen';
import Button from '../components/ui/Button';
import Card from '../components/ui/Card';

const { width } = Dimensions.get('window');
const CARD_WIDTH = width * 0.7;

type GoalSetupScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'GoalSetup'
>;

interface Goal {
  id: string;
  title: string;
  icon: string;
  color: string;
}

const GoalSetupScreen = () => {
  const navigation = useNavigation<GoalSetupScreenNavigationProp>();
  const [selectedGoal, setSelectedGoal] = useState<Goal | null>(null);
  const [amount, setAmount] = useState('');
  const [months, setMonths] = useState('12');
  const [monthlySaving, setMonthlySaving] = useState('');
  // Predefined goals
  const goals: Goal[] = [
    {
      id: '1',
      title: 'Mua nhà',
      icon: '🏠',
      color: theme.colors.primary[100],
    },
    {
      id: '2',
      title: 'Mua xe',
      icon: '🚗',
      color: theme.colors.warning[100],
    },
    {
      id: '3',
      title: 'Du lịch',
      icon: '✈️',
      color: theme.colors.success[100],
    },
    {
      id: '4',
      title: 'Học tập',
      icon: '🎓',
      color: theme.colors.secondary[100],
    },
    {
      id: '5',
      title: 'Đám cưới',
      icon: '💍',
      color: theme.colors.error[50],
    },
    {
      id: '6',
      title: 'Tiết kiệm',
      icon: '💰',
      color: theme.colors.error[100],
    },
    {
      id: '7',
      title: 'Quỹ khẩn cấp',
      icon: '🚨',
      color: theme.colors.warning[100],
    },
    {
      id: '8',
      title: 'Khác',
      icon: '✨',
      color: theme.colors.gray[200],
    },
  ];

  // Calculate monthly saving amount
  useEffect(() => {
    if (amount && months) {
      const totalAmount = parseFloat(amount.replace(/,/g, ''));
      const monthsCount = parseInt(months, 10);
      
      if (!isNaN(totalAmount) && !isNaN(monthsCount) && monthsCount > 0) {
        const monthly = totalAmount / monthsCount;
        setMonthlySaving(monthly.toLocaleString('vi-VN'));
      } else {
        setMonthlySaving('');
      }
    } else {
      setMonthlySaving('');
    }
  }, [amount, months]);

  // Format amount with commas
  const formatAmount = (text: string) => {
    // Remove non-numeric characters
    const numericValue = text.replace(/[^0-9]/g, '');
    
    // Format with commas
    if (numericValue) {
      const formattedValue = parseInt(numericValue, 10).toLocaleString('vi-VN');
      setAmount(formattedValue);
    } else {
      setAmount('');
    }
  };

  const handleContinue = () => {
    if (selectedGoal && amount && months) {
      // In a real app, save the goal data to state/context/backend
      navigation.navigate('InitialBalance');
    }
  };

  return (
    <Screen>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        <ScrollView 
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.headerContainer}>
            <Text style={styles.headerTitle}>Bạn có mục tiêu là gì?</Text>
            <Text style={styles.headerSubtitle}>
              Thiết lập mục tiêu tài chính đầu tiên của bạn
            </Text>
          </View>

          {/* Goal selection carousel */}
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.carouselContainer}
            snapToInterval={CARD_WIDTH + 20}
            decelerationRate="fast"
            snapToAlignment="center"
          >
            {goals.map((goal) => (
              <TouchableOpacity
                key={goal.id}
                style={[
                  styles.goalCard,
                  { backgroundColor: goal.color },
                  selectedGoal?.id === goal.id && styles.selectedGoalCard,
                ]}
                onPress={() => setSelectedGoal(goal)}
                activeOpacity={0.8}
              >
                <Text style={styles.goalIcon}>{goal.icon}</Text>
                <Text style={styles.goalTitle}>{goal.title}</Text>
                {selectedGoal?.id === goal.id && (
                  <View style={styles.selectedIndicator}>
                    <Text style={styles.selectedIndicatorText}>✓</Text>
                  </View>
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>

          {/* Amount and time setup */}
          <Card style={styles.setupContainer}>
            <Text style={styles.sectionTitle}>Chi tiết mục tiêu</Text>
            
            <Text style={styles.inputLabel}>Số tiền mục tiêu</Text>
            <View style={styles.amountInputContainer}>
              <Text style={styles.currencySymbol}>₫</Text>
              <TextInput
                style={styles.amountInput}
                placeholder="10,000,000"
                keyboardType="numeric"
                value={amount}
                onChangeText={formatAmount}
              />
            </View>
            
            <Text style={styles.inputLabel}>Thời gian (tháng)</Text>
            <View style={styles.timeContainer}>
              <TouchableOpacity
                style={styles.timeButton}
                onPress={() => {
                  const currentMonths = parseInt(months, 10);
                  if (currentMonths > 1) {
                    setMonths((currentMonths - 1).toString());
                  }
                }}
              >
                <Text style={styles.timeButtonText}>−</Text>
              </TouchableOpacity>
              
              <TextInput
                style={styles.timeInput}
                keyboardType="numeric"
                value={months}
                onChangeText={setMonths}
              />
              
              <TouchableOpacity
                style={styles.timeButton}
                onPress={() => {
                  const currentMonths = parseInt(months, 10);
                  setMonths((currentMonths + 1).toString());
                }}
              >
                <Text style={styles.timeButtonText}>+</Text>
              </TouchableOpacity>
            </View>
            
            {/* Monthly savings calculation */}
            {monthlySaving ? (
              <View style={styles.resultContainer}>
                <Text style={styles.resultLabel}>Số tiền tiết kiệm mỗi tháng</Text>
                <Text style={styles.resultAmount}>₫ {monthlySaving}</Text>
              </View>
            ) : null}
          </Card>

          {/* Continue button */}
          <Button
            title="Tiếp tục"
            onPress={handleContinue}
            variant="primary"
            fullWidth
            disabled={!selectedGoal || !amount || !months}
            style={[
              styles.continueButton,
              (!selectedGoal || !amount || !months) && styles.disabledButton,
            ]}
          />
        </ScrollView>
      </KeyboardAvoidingView>
    </Screen>
  );
};

const styles = StyleSheet.create({
  scrollContainer: {
    flexGrow: 1,
    paddingBottom: theme.spacing.xl,
  },
  headerContainer: {
    paddingHorizontal: theme.spacing.xl,
    paddingTop: theme.spacing.xl,
    marginBottom: theme.spacing.xl,
  },
  headerTitle: {
    fontSize: theme.typography.fontSizes['2xl'],
    fontWeight: '700',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.sm,
  },
  headerSubtitle: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.secondary,
  },
  carouselContainer: {
    paddingHorizontal: theme.spacing.xl,
    paddingBottom: theme.spacing.xl,
  },
  goalCard: {
    width: CARD_WIDTH,
    height: 150,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    marginRight: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
  },
  selectedGoalCard: {
    borderColor: theme.colors.primary[500],
    borderWidth: 2,
    shadowColor: theme.colors.primary[500],
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  goalIcon: {
    fontSize: 48,
    marginBottom: theme.spacing.sm,
  },
  goalTitle: {
    fontSize: theme.typography.fontSizes.lg,
    fontWeight: '600',
    color: theme.colors.text.primary,
  },
  selectedIndicator: {
    position: 'absolute',
    top: theme.spacing.sm,
    right: theme.spacing.sm,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: theme.colors.primary[500],
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectedIndicatorText: {
    color: theme.colors.background.primary,
    fontWeight: '700',
  },
  setupContainer: {
    marginHorizontal: theme.spacing.xl,
    marginBottom: theme.spacing.xl,
    padding: theme.spacing.md,
  },
  sectionTitle: {
    fontSize: theme.typography.fontSizes.lg,
    fontWeight: '600',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.md,
  },
  inputLabel: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
    marginBottom: theme.spacing.sm,
  },
  amountInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.gray[100],
    borderRadius: theme.borderRadius.md,
    paddingHorizontal: theme.spacing.md,
    marginBottom: theme.spacing.md,
  },
  currencySymbol: {
    fontSize: theme.typography.fontSizes.lg,
    color: theme.colors.text.secondary,
    marginRight: theme.spacing.sm,
  },
  amountInput: {
    flex: 1,
    padding: theme.spacing.md,
    fontSize: theme.typography.fontSizes.lg,
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.xl,
  },
  timeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: theme.colors.gray[100],
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.colors.gray[200],
  },
  timeButtonText: {
    fontSize: theme.typography.fontSizes.lg,
    fontWeight: '600',
    color: theme.colors.text.primary,
  },
  timeInput: {
    flex: 1,
    textAlign: 'center',
    fontSize: theme.typography.fontSizes.lg,
    paddingVertical: theme.spacing.sm,
    marginHorizontal: theme.spacing.sm,
    backgroundColor: theme.colors.gray[100],
    borderRadius: theme.borderRadius.md,
  },
  resultContainer: {
    backgroundColor: theme.colors.primary[100],
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.md,
    alignItems: 'center',
  },
  resultLabel: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.primary[700],
    marginBottom: theme.spacing.sm,
  },
  resultAmount: {
    fontSize: theme.typography.fontSizes['2xl'],
    fontWeight: '700',
    color: theme.colors.primary[500],
  },
  continueButton: {
    marginHorizontal: theme.spacing.xl,
    marginTop: theme.spacing.md,
  },
  disabledButton: {
    opacity: 0.6,
  },
});

export default GoalSetupScreen; 