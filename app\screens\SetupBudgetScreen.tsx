import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  Alert,
  Dimensions,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { StatusBar } from 'expo-status-bar';
import { RootStackParamList } from '../navigation/types';
import theme from '../theme';
import Screen from '../components/common/Screen';

const { width } = Dimensions.get('window');
const ICON_SIZE = 50;

type SetupBudgetScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'SetupBudget'
>;

// Budget categories with icons
const budgetCategories = [
  { id: 'food', name: 'Ăn uống', icon: '🍲', color: theme.colors.primary[100] },
  { id: 'transport', name: '<PERSON>uyể<PERSON>', icon: '🚕', color: theme.colors.warning[100] },
  { id: 'shopping', name: '<PERSON><PERSON> sắm', icon: '🛒', color: theme.colors.success[100] },
  { id: 'entertainment', name: '<PERSON><PERSON>ải trí', icon: '🎬', color: theme.colors.secondary[100] },
  { id: 'housing', name: 'Nhà cửa', icon: '🏠', color: theme.colors.error[50] },
  { id: 'utilities', name: 'Tiện ích', icon: '💡', color: theme.colors.error[100] },
  { id: 'health', name: 'Sức khỏe', icon: '💊', color: theme.colors.primary[100] },
  { id: 'education', name: 'Học tập', icon: '📚', color: theme.colors.primary[50] },
  { id: 'personal', name: 'Cá nhân', icon: '👤', color: theme.colors.warning[100] },
  { id: 'pets', name: 'Thú cưng', icon: '🐾', color: theme.colors.success[100] },
  { id: 'gifts', name: 'Quà tặng', icon: '🎁', color: theme.colors.secondary[100] },
  { id: 'other', name: 'Khác', icon: '✨', color: theme.colors.gray[200] },
];

// Budget periods
const budgetPeriods = [
  { id: 'monthly', name: 'Hàng tháng' },
  { id: 'quarterly', name: 'Hàng quý' },
  { id: 'annual', name: 'Hàng năm' },
];

const SetupBudgetScreen = () => {
  const navigation = useNavigation<SetupBudgetScreenNavigationProp>();
  
  const [selectedCategoryId, setSelectedCategoryId] = useState<string | null>(null);
  const [amount, setAmount] = useState('');
  const [selectedPeriodId, setSelectedPeriodId] = useState('monthly');
  const [startDate, setStartDate] = useState(new Date().toLocaleDateString('vi-VN'));
  const [note, setNote] = useState('');
  
  // Format amount with commas
  const formatAmount = (text: string) => {
    // Remove non-numeric characters
    const numericValue = text.replace(/[^0-9]/g, '');
    
    // Format with commas
    if (numericValue) {
      const formattedValue = parseInt(numericValue, 10).toLocaleString('vi-VN');
      setAmount(formattedValue);
    } else {
      setAmount('');
    }
  };

  const getSelectedCategory = () => {
    return budgetCategories.find(cat => cat.id === selectedCategoryId);
  };

  const handleSaveBudget = () => {
    // Validation
    if (!selectedCategoryId) {
      Alert.alert('Lỗi', 'Vui lòng chọn danh mục ngân sách');
      return;
    }
    
    if (!amount) {
      Alert.alert('Lỗi', 'Vui lòng nhập số tiền ngân sách');
      return;
    }
    
    // In a real app, save the budget data to state/context/backend
    Alert.alert(
      'Thành công',
      'Ngân sách đã được thiết lập',
      [
        {
          text: 'OK',
          onPress: () => navigation.goBack(),
        },
      ]
    );
  };

  return (
    <Screen>
      <StatusBar style="dark" />
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
        >
          <Text style={styles.screenTitle}>Thiết lập ngân sách mới</Text>
          
          {/* Category Selection */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Chọn danh mục</Text>
            <View style={styles.categoriesContainer}>
              {budgetCategories.map((category) => (
                <TouchableOpacity
                  key={category.id}
                  style={[
                    styles.categoryItem,
                    { backgroundColor: category.color },
                    selectedCategoryId === category.id && styles.selectedCategoryItem,
                  ]}
                  onPress={() => setSelectedCategoryId(category.id)}
                >
                  <Text style={styles.categoryIcon}>{category.icon}</Text>
                  <Text 
                    style={[
                      styles.categoryName,
                      selectedCategoryId === category.id && styles.selectedCategoryName,
                    ]}
                  >
                    {category.name}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
          
          {/* Budget Amount */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Số tiền ngân sách</Text>
            <View style={styles.amountInputContainer}>
              <Text style={styles.currencySymbol}>₫</Text>
              <TextInput
                style={styles.amountInput}
                placeholder="2,000,000"
                keyboardType="numeric"
                value={amount}
                onChangeText={formatAmount}
              />
            </View>
          </View>
          
          {/* Budget Period */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Chu kỳ ngân sách</Text>
            <View style={styles.periodSelector}>
              {budgetPeriods.map((period) => (
                <TouchableOpacity
                  key={period.id}
                  style={[
                    styles.periodOption,
                    selectedPeriodId === period.id && styles.selectedPeriodOption,
                  ]}
                  onPress={() => setSelectedPeriodId(period.id)}
                >
                  <Text 
                    style={[
                      styles.periodText,
                      selectedPeriodId === period.id && styles.selectedPeriodText,
                    ]}
                  >
                    {period.name}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
          
          {/* Start Date */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Ngày bắt đầu</Text>
            <TouchableOpacity style={styles.dateInput}>
              <Text style={styles.dateText}>{startDate}</Text>
            </TouchableOpacity>
          </View>
          
          {/* Notes */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Ghi chú (tùy chọn)</Text>
            <TextInput
              style={styles.noteInput}
              placeholder="Nhập ghi chú cho ngân sách này"
              value={note}
              onChangeText={setNote}
              multiline
              numberOfLines={3}
              textAlignVertical="top"
            />
          </View>
          
          {/* Selected Category Preview */}
          {selectedCategoryId && (
            <View style={styles.previewContainer}>
              <View style={[styles.previewCard, { backgroundColor: getSelectedCategory()?.color }]}>
                <View style={styles.previewIconContainer}>
                  <Text style={styles.previewIcon}>{getSelectedCategory()?.icon}</Text>
                </View>
                <Text style={styles.previewCategory}>{getSelectedCategory()?.name}</Text>
                <Text style={styles.previewAmount}>{amount ? `₫ ${amount}` : '₫ 0'}</Text>
                <Text style={styles.previewPeriod}>
                  {budgetPeriods.find(p => p.id === selectedPeriodId)?.name}
                </Text>
              </View>
            </View>
          )}
          
          {/* Submit Button */}
          <TouchableOpacity
            style={[
              styles.saveButton,
              (!selectedCategoryId || !amount) && styles.disabledButton,
            ]}
            onPress={handleSaveBudget}
            disabled={!selectedCategoryId || !amount}
          >
            <Text style={styles.saveButtonText}>Lưu ngân sách</Text>
          </TouchableOpacity>
        </ScrollView>
      </KeyboardAvoidingView>
    </Screen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background.primary,
  },
  scrollContainer: {
    flexGrow: 1,
    paddingHorizontal: theme.spacing.xl,
    paddingVertical: theme.spacing.xl,
  },
  screenTitle: {
    fontSize: theme.typography.fontSizes['2xl'],
    fontWeight: '700',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.xl,
  },
  section: {
    marginBottom: theme.spacing.xl,
  },
  sectionTitle: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '600',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.md,
  },
  categoriesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -theme.spacing.sm, // Compensate for category item padding
  },
  categoryItem: {
    width: (width - 40) / 3, // 3 categories per row, 20px padding on each side
    height: 90,
    justifyContent: 'center',
    alignItems: 'center',
    padding: theme.spacing.sm,
    marginHorizontal: theme.spacing.sm,
    marginBottom: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
  },
  selectedCategoryItem: {
    borderWidth: 2,
    borderColor: theme.colors.primary[500],
  },
  categoryIcon: {
    fontSize: 24,
    marginBottom: theme.spacing.sm,
  },
  categoryName: {
    fontSize: theme.typography.fontSizes.xs,
    color: theme.colors.text.primary,
    textAlign: 'center',
  },
  selectedCategoryName: {
    fontWeight: '500',
    color: theme.colors.primary[500],
  },
  amountInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.gray[100],
    borderRadius: theme.borderRadius.md,
    paddingHorizontal: theme.spacing.md,
  },
  currencySymbol: {
    fontSize: theme.typography.fontSizes.lg,
    color: theme.colors.text.secondary,
    marginRight: theme.spacing.sm,
  },
  amountInput: {
    flex: 1,
    padding: theme.spacing.md,
    fontSize: theme.typography.fontSizes.lg,
  },
  periodSelector: {
    flexDirection: 'row',
    backgroundColor: theme.colors.gray[100],
    borderRadius: theme.borderRadius.md,
    overflow: 'hidden',
  },
  periodOption: {
    flex: 1,
    paddingVertical: theme.spacing.md,
    alignItems: 'center',
  },
  selectedPeriodOption: {
    backgroundColor: theme.colors.primary[500],
  },
  periodText: {
    fontSize: theme.typography.fontSizes.sm,
    fontWeight: '500',
    color: theme.colors.text.secondary,
  },
  selectedPeriodText: {
    color: theme.colors.background.primary,
  },
  dateInput: {
    backgroundColor: theme.colors.gray[100],
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.md,
  },
  dateText: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.primary,
  },
  noteInput: {
    backgroundColor: theme.colors.gray[100],
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.md,
    fontSize: theme.typography.fontSizes.md,
    minHeight: 100,
  },
  previewContainer: {
    alignItems: 'center',
    marginBottom: theme.spacing.xl,
  },
  previewCard: {
    width: width * 0.6,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
    alignItems: 'center',
  },
  previewIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  previewIcon: {
    fontSize: 24,
  },
  previewCategory: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '600',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.sm,
  },
  previewAmount: {
    fontSize: theme.typography.fontSizes.xl,
    fontWeight: '700',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.xs,
  },
  previewPeriod: {
    fontSize: theme.typography.fontSizes.xs,
    color: theme.colors.text.secondary,
  },
  saveButton: {
    backgroundColor: theme.colors.primary[500],
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.md,
    alignItems: 'center',
  },
  disabledButton: {
    backgroundColor: theme.colors.gray[400],
  },
  saveButtonText: {
    color: theme.colors.background.primary,
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '600',
  },
});

export default SetupBudgetScreen; 