import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  Alert,
  TouchableOpacity,
  FlatList,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation/types';
import theme from '../theme';
import Screen from '../components/common/Screen';
import Button from '../components/ui/Button';
import Card from '../components/ui/Card';
import { BudgetAPI, IBudget, TransactionSummaryDto } from '../../src/api/budget.api';

type BudgetDetailScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;

const BudgetDetailScreen = () => {
  const navigation = useNavigation<BudgetDetailScreenNavigationProp>();
  const route = useRoute();
  const { id, month, year } = route.params as { id: string; month?: number; year?: number };

  // State management
  const [budget, setBudget] = useState<IBudget | null>(null);
  const [transactions, setTransactions] = useState<TransactionSummaryDto[]>([]);
  const [loading, setLoading] = useState(true);
  const [spentAmount, setSpentAmount] = useState(0);

  // Load budget details
  const loadBudgetDetails = async () => {
    try {
      setLoading(true);

      // Use month/year from params, or fallback to current date
      const currentDate = new Date();
      const targetMonth = month || currentDate.getMonth() + 1;
      const targetYear = year || currentDate.getFullYear();

      // First, get all budgets and find the one for this category
      const allBudgets = await BudgetAPI.getAll({
        categoryId: id, // id is actually categoryId from navigation
        month: targetMonth,
        year: targetYear
      });

      if (allBudgets.length === 0) {
        throw new Error('Budget not found for this category and period');
      }

      const budgetData = allBudgets[0]; // Take the first (should be only one)
      setBudget(budgetData);

      // Get budget report to fetch transactions for this category
      const report = await BudgetAPI.getReport(budgetData.month, budgetData.year);
      const categoryReport = report.categories.find((cat: any) => cat.categoryId === id);

      if (categoryReport) {
        setTransactions(categoryReport.transactions);
        setSpentAmount(categoryReport.spentAmount);
      }
    } catch (error: any) {
      let errorMessage = 'Failed to load budget details. Please try again.';

      if (error.response?.status === 401) {
        errorMessage = 'Authentication failed. Please login again.';
      } else if (error.response?.status === 404) {
        errorMessage = 'Budget not found.';
      } else if (error.message) {
        errorMessage = error.message;
      }

      Alert.alert('Error', errorMessage);
      navigation.goBack();
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadBudgetDetails();
  }, [id]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const calculateProgress = () => {
    if (!budget || budget.amount === 0) return 0;
    return Math.min((spentAmount / budget.amount) * 100, 100);
  };

  const remainingAmount = budget ? budget.amount - spentAmount : 0;
  const isOverBudget = remainingAmount < 0;
  const progress = calculateProgress();

  const handleEditBudget = () => {
    if (budget) {
      navigation.navigate('EditBudget', { id: budget.id }); // Use actual budget ID
    }
  };

  const handleDeleteBudget = () => {
    Alert.alert(
      'Delete Budget',
      'Are you sure you want to delete this budget? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              if (budget) {
                await BudgetAPI.delete(budget.id); // Use actual budget ID
                Alert.alert('Success', 'Budget deleted successfully');
                navigation.goBack();
              }
            } catch (error: any) {
              Alert.alert('Error', 'Failed to delete budget. Please try again.');
            }
          },
        },
      ]
    );
  };

  const renderTransactionItem = ({ item }: { item: TransactionSummaryDto }) => (
    <View style={styles.transactionItem}>
      <View style={styles.transactionInfo}>
        <Text style={styles.transactionDescription}>
          {item.description || 'No description'}
        </Text>
        <Text style={styles.transactionDate}>{formatDate(item.transaction_date)}</Text>
      </View>
      <Text style={styles.transactionAmount}>{formatCurrency(item.amount)}</Text>
    </View>
  );

  if (loading) {
    return (
      <Screen>
        <View style={[styles.container, { justifyContent: 'center', alignItems: 'center' }]}>
          <ActivityIndicator size="large" color={theme.colors.primary[500]} />
          <Text style={{ marginTop: theme.spacing.md, color: theme.colors.text.secondary }}>
            Loading budget details...
          </Text>
        </View>
      </Screen>
    );
  }

  if (!budget) {
    return (
      <Screen>
        <View style={[styles.container, { justifyContent: 'center', alignItems: 'center' }]}>
          <Text style={styles.errorText}>Budget not found</Text>
          <Button
            title="Go Back"
            onPress={() => navigation.goBack()}
            variant="primary"
            style={{ marginTop: theme.spacing.md }}
          />
        </View>
      </Screen>
    );
  }

  return (
    <Screen>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Budget Overview Card */}
        <Card style={styles.overviewCard}>
          <View style={styles.categoryHeader}>
            <View style={styles.categoryIconContainer}>
              <Text style={styles.categoryIcon}>💰</Text>
            </View>
            <View style={styles.categoryInfo}>
              <Text style={styles.categoryName}>{budget.category?.name || 'Unknown Category'}</Text>
              <Text style={styles.budgetPeriod}>
                {new Date(budget.year, budget.month - 1).toLocaleDateString('en-US', {
                  month: 'long',
                  year: 'numeric',
                })}
              </Text>
            </View>
            {isOverBudget && (
              <View style={styles.overBudgetBadge}>
                <Text style={styles.overBudgetText}>Over Budget</Text>
              </View>
            )}
          </View>

          <View style={styles.progressContainer}>
            <View
              style={[
                styles.progressBar,
                { width: `${progress}%` },
                isOverBudget && styles.overBudgetBar,
              ]}
            />
          </View>

          <View style={styles.budgetStats}>
            <View style={styles.statItem}>
              <Text style={styles.statLabel}>Budget</Text>
              <Text style={styles.statValue}>{formatCurrency(budget.amount)}</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statLabel}>Spent</Text>
              <Text style={[styles.statValue, isOverBudget && styles.overBudgetText]}>
                {formatCurrency(spentAmount)}
              </Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statLabel}>Remaining</Text>
              <Text style={[styles.statValue, isOverBudget ? styles.overBudgetText : styles.remainingText]}>
                {formatCurrency(Math.abs(remainingAmount))}
                {isOverBudget ? ' over' : ''}
              </Text>
            </View>
          </View>
        </Card>

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <Button
            title="Edit Budget"
            onPress={handleEditBudget}
            variant="primary"
            style={[styles.actionButton, { flex: 1, marginRight: theme.spacing.sm }]}
          />
          <Button
            title="Delete"
            onPress={handleDeleteBudget}
            variant="secondary"
            style={[styles.actionButton, { flex: 1, marginLeft: theme.spacing.sm }]}
          />
        </View>

        {/* Transactions List */}
        <View style={styles.transactionsSection}>
          <Text style={styles.sectionTitle}>Recent Transactions</Text>
          {transactions.length > 0 ? (
            <FlatList
              data={transactions}
              renderItem={renderTransactionItem}
              keyExtractor={(item) => item.id}
              scrollEnabled={false}
              style={styles.transactionsList}
            />
          ) : (
            <View style={styles.emptyState}>
              <Text style={styles.emptyStateText}>No transactions yet</Text>
              <Text style={styles.emptyStateSubtext}>
                Transactions in this category will appear here
              </Text>
            </View>
          )}
        </View>
      </ScrollView>
    </Screen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  overviewCard: {
    margin: theme.spacing.md,
  },
  categoryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.lg,
  },
  categoryIconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: theme.colors.primary[100],
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: theme.spacing.md,
  },
  categoryIcon: {
    fontSize: theme.typography.fontSizes['2xl'],
  },
  categoryInfo: {
    flex: 1,
  },
  categoryName: {
    fontSize: theme.typography.fontSizes.xl,
    fontWeight: '700',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.xs,
  },
  budgetPeriod: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.secondary,
  },
  overBudgetBadge: {
    backgroundColor: theme.colors.error,
    paddingVertical: theme.spacing.xs,
    paddingHorizontal: theme.spacing.sm,
    borderRadius: theme.borderRadius.full,
  },
  overBudgetText: {
    fontSize: theme.typography.fontSizes.sm,
    fontWeight: '500',
    color: theme.colors.background.primary,
  },
  progressContainer: {
    height: 12,
    backgroundColor: theme.colors.gray[100],
    borderRadius: theme.borderRadius.full,
    marginBottom: theme.spacing.lg,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    backgroundColor: theme.colors.primary[500],
  },
  overBudgetBar: {
    backgroundColor: theme.colors.error,
  },
  budgetStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statItem: {
    alignItems: 'center',
  },
  statLabel: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
    marginBottom: theme.spacing.xs,
  },
  statValue: {
    fontSize: theme.typography.fontSizes.lg,
    fontWeight: '600',
    color: theme.colors.text.primary,
  },
  remainingText: {
    color: theme.colors.success,
  },
  actionButtons: {
    flexDirection: 'row',
    paddingHorizontal: theme.spacing.md,
    marginBottom: theme.spacing.lg,
  },
  actionButton: {
    marginVertical: 0,
  },
  transactionsSection: {
    paddingHorizontal: theme.spacing.md,
  },
  sectionTitle: {
    fontSize: theme.typography.fontSizes.lg,
    fontWeight: '600',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.md,
  },
  transactionsList: {
    backgroundColor: theme.colors.background.primary,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.sm,
  },
  chartContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    height: 200,
  },
  chartBar: {
    flex: 1,
    alignItems: 'center',
  },
  chartAmount: {
    fontSize: theme.typography.fontSizes.xs,
    color: theme.colors.text.secondary,
    marginBottom: theme.spacing.xs,
  },
  barContainer: {
    width: '50%',
    alignItems: 'center',
    justifyContent: 'flex-end',
    height: 150,
  },
  bar: {
    width: '100%',
    backgroundColor: theme.colors.primary[100],
    borderTopLeftRadius: theme.borderRadius.sm,
    borderTopRightRadius: theme.borderRadius.sm,
  },
  currentMonthBar: {
    backgroundColor: theme.colors.primary[500],
  },
  chartLabel: {
    fontSize: theme.typography.fontSizes.xs,
    color: theme.colors.text.secondary,
    marginTop: theme.spacing.sm,
  },
  transactionsCard: {
    margin: theme.spacing.md,
    marginTop: 0,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  viewAllText: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.primary[500],
    fontWeight: '500',
  },
  transactionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray[100],
  },
  transactionInfo: {
    flex: 1,
  },
  transactionDescription: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '500',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.xs / 2,
  },
  transactionDate: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
  },
  transactionAmount: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '600',
    color: theme.colors.error,
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: theme.spacing.xl,
  },
  emptyStateText: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '500',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.xs,
  },
  emptyStateSubtext: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
    textAlign: 'center',
  },
  errorText: {
    fontSize: theme.typography.fontSizes.lg,
    color: theme.colors.error,
    textAlign: 'center',
  },
  addTransactionButton: {
    backgroundColor: theme.colors.primary[100],
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.sm,
    alignItems: 'center',
    marginTop: theme.spacing.md,
  },
  addTransactionText: {
    fontSize: theme.typography.fontSizes.sm,
    fontWeight: '500',
    color: theme.colors.primary[500],
  },
  actionContainer: {
    margin: theme.spacing.md,
    marginTop: theme.spacing.sm,
  },
  editButton: {
    marginBottom: theme.spacing.sm,
  },
  deleteButton: {
    backgroundColor: theme.colors.background.primary,
    borderWidth: 1,
    borderColor: theme.colors.error[500],
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.sm,
    alignItems: 'center',
  },
  deleteButtonText: {
    color: theme.colors.error[500],
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '600',
  },
  confirmationOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: theme.spacing.xl,
  },
  confirmationBox: {
    backgroundColor: theme.colors.background.primary,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.xl,
    width: '100%',
  },
  confirmationTitle: {
    fontSize: theme.typography.fontSizes.lg,
    fontWeight: '600',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.sm,
  },
  confirmationText: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
    marginBottom: theme.spacing.xl,
  },
  confirmationButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  cancelButton: {
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.md,
    marginRight: theme.spacing.sm,
  },
  cancelButtonText: {
    fontSize: theme.typography.fontSizes.sm,
    fontWeight: '500',
    color: theme.colors.text.secondary,
  },
  confirmButton: {
    backgroundColor: theme.colors.error[500],
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
  },
  confirmButtonText: {
    fontSize: theme.typography.fontSizes.sm,
    fontWeight: '500',
    color: theme.colors.background.primary,
  },
});

export default BudgetDetailScreen;