import React from 'react';
import { createDrawerNavigator } from '@react-navigation/drawer';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { DrawerContentScrollView, DrawerContentComponentProps } from '@react-navigation/drawer';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

import theme from '../theme';
import { TabNavigatorParamList, RootStackParamList } from './types';

// Import screens
import HomeScreen from '../screens/HomeScreen';
import GoalsScreen from '../screens/GoalsScreen';
import TransactionListScreen from '../screens/TransactionListScreen';
import BudgetScreen from '../screens/BudgetScreen';
import AnalyticsScreen from '../screens/AnalyticsScreen';
import MoreScreen from '../screens/MoreScreen';
import CategoriesScreen from '../screens/CategoriesScreen';

const Drawer = createDrawerNavigator<TabNavigatorParamList>();

type NavigationProp = NativeStackNavigationProp<RootStackParamList>;

// Custom drawer content
const CustomDrawerContent = (props: DrawerContentComponentProps) => {
  const navigation = useNavigation<NavigationProp>();

  const menuItems = [
    { name: 'Home', label: 'Dashboard', icon: '🏠', screen: 'Home' as keyof TabNavigatorParamList },
    { name: 'Goals', label: 'Goals', icon: '🎯', screen: 'Goals' as keyof TabNavigatorParamList },
    { name: 'Transactions', label: 'Transactions', icon: '💸', screen: 'Transactions' as keyof TabNavigatorParamList },
    { name: 'Budget', label: 'Budget', icon: '💰', screen: 'Budget' as keyof TabNavigatorParamList },
    { name: 'Categories', label: 'Categories', icon: '🔖', screen: 'Categories' as keyof TabNavigatorParamList },
    { name: 'Analytics', label: 'Analytics', icon: '📊', screen: 'Analytics' as keyof TabNavigatorParamList },
    { name: 'More', label: 'More', icon: '⋯', screen: 'More' as keyof TabNavigatorParamList },
  ];

  const handleNavigate = (screenName: keyof TabNavigatorParamList) => {
    props.navigation.navigate(screenName);
  };

  return (
    <View style={styles.drawerContainer}>
      {/* Header */}
      <View style={styles.drawerHeader}>
        <Text style={styles.appName}>FinanceApp</Text>
        <Text style={styles.appSubtitle}>Smart Financial Management</Text>
      </View>

      {/* Menu Items */}
      <ScrollView style={styles.menuContainer} showsVerticalScrollIndicator={false}>
        {menuItems.map((item) => {
          const isActive = props.state.routeNames[props.state.index] === item.name;

          return (
            <TouchableOpacity
              key={item.name}
              style={[styles.menuItem, isActive && styles.activeMenuItem]}
              onPress={() => handleNavigate(item.screen)}
            >
              <Text style={styles.menuIcon}>{item.icon}</Text>
              <Text style={[styles.menuLabel, isActive && styles.activeMenuLabel]}>
                {item.label}
              </Text>
            </TouchableOpacity>
          );
        })}
      </ScrollView>

      {/* Footer */}
      <View style={styles.drawerFooter}>
        <TouchableOpacity
          style={styles.logoutButton}
          onPress={() => {
            // Handle logout
            navigation.navigate('Auth', { screen: 'Login' });
          }}
        >
          <Text style={styles.logoutIcon}>🚪</Text>
          <Text style={styles.logoutText}>Logout</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const DrawerNavigator = () => {
  return (
    <Drawer.Navigator
      drawerContent={(props) => <CustomDrawerContent {...props} />}
      screenOptions={{
        headerShown: false,
        drawerType: 'slide',
        drawerPosition: 'left',
        drawerStyle: {
          backgroundColor: theme.colors.background.primary,
          width: 280,
        },
        overlayColor: 'rgba(0, 0, 0, 0.5)',
        sceneContainerStyle: {
          backgroundColor: theme.colors.background.primary,
        },
      }}
    >
      <Drawer.Screen name="Home" component={HomeScreen} />
      <Drawer.Screen name="Goals" component={GoalsScreen} />
      <Drawer.Screen name="Categories" component={CategoriesScreen} />
      <Drawer.Screen name="Transactions" component={TransactionListScreen} />
      <Drawer.Screen name="Budget" component={BudgetScreen} />
      <Drawer.Screen name="Analytics" component={AnalyticsScreen} />
      <Drawer.Screen name="More" component={MoreScreen} />
    </Drawer.Navigator>
  );
};

const styles = StyleSheet.create({
  drawerContainer: {
    flex: 1,
    backgroundColor: theme.colors.background.primary,
  },
  drawerHeader: {
    padding: theme.spacing.xl,
    paddingTop: theme.spacing['2xl'],
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray[200],
    backgroundColor: theme.colors.primary[50],
  },
  appName: {
    fontSize: theme.typography.fontSizes.xl,
    fontWeight: '700',
    color: theme.colors.primary[600],
    marginBottom: theme.spacing.xs,
  },
  appSubtitle: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
  },
  menuContainer: {
    flex: 1,
    paddingTop: theme.spacing.md,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.xl,
    paddingVertical: theme.spacing.md,
    marginHorizontal: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
  },
  activeMenuItem: {
    backgroundColor: theme.colors.primary[100],
  },
  menuIcon: {
    fontSize: theme.typography.fontSizes.xl,
    marginRight: theme.spacing.md,
    width: 24,
    textAlign: 'center',
  },
  menuLabel: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '500',
    color: theme.colors.text.primary,
  },
  activeMenuLabel: {
    color: theme.colors.primary[600],
    fontWeight: '600',
  },
  drawerFooter: {
    borderTopWidth: 1,
    borderTopColor: theme.colors.gray[200],
    padding: theme.spacing.md,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
  },
  logoutIcon: {
    fontSize: theme.typography.fontSizes.lg,
    marginRight: theme.spacing.md,
  },
  logoutText: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '500',
    color: theme.colors.error[500],
  },
});

export default DrawerNavigator;
