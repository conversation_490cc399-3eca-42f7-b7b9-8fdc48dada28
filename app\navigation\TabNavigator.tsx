import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Text, View, StyleSheet } from 'react-native';
import { TabNavigatorParamList } from './types';
import theme from '../theme';

import HomeScreen from '../screens/HomeScreen';
import GoalsScreen from '../screens/GoalsScreen';
import TransactionsScreen from '../screens/TransactionsScreen';
import BudgetScreen from '../screens/BudgetScreen';
import AnalyticsScreen from '../screens/AnalyticsScreen';
import MoreScreen from '../screens/MoreScreen';

const Tab = createBottomTabNavigator<TabNavigatorParamList>();

// Tab icons
const HomeIcon = ({ focused }: { focused: boolean }) => (
  <View style={[styles.iconContainer, focused ? styles.focusedIconContainer : null]}>
    <Text style={[styles.iconText, focused ? styles.focusedIconText : null]}>🏠</Text>
  </View>
);

const GoalsIcon = ({ focused }: { focused: boolean }) => (
  <View style={[styles.iconContainer, focused ? styles.focusedIconContainer : null]}>
    <Text style={[styles.iconText, focused ? styles.focusedIconText : null]}>🎯</Text>
  </View>
);

const TransactionsIcon = ({ focused }: { focused: boolean }) => (
  <View style={[styles.iconContainer, focused ? styles.focusedIconContainer : null]}>
    <Text style={[styles.iconText, focused ? styles.focusedIconText : null]}>💸</Text>
  </View>
);

const BudgetIcon = ({ focused }: { focused: boolean }) => (
  <View style={[styles.iconContainer, focused ? styles.focusedIconContainer : null]}>
    <Text style={[styles.iconText, focused ? styles.focusedIconText : null]}>💰</Text>
  </View>
);

const AnalyticsIcon = ({ focused }: { focused: boolean }) => (
  <View style={[styles.iconContainer, focused ? styles.focusedIconContainer : null]}>
    <Text style={[styles.iconText, focused ? styles.focusedIconText : null]}>📊</Text>
  </View>
);

const MoreIcon = ({ focused }: { focused: boolean }) => (
  <View style={[styles.iconContainer, focused ? styles.focusedIconContainer : null]}>
    <Text style={[styles.iconText, focused ? styles.focusedIconText : null]}>⋯</Text>
  </View>
);

const TabNavigator = () => {
  return (
    <Tab.Navigator
      screenOptions={{
        headerShown: false,
        tabBarStyle: {
          backgroundColor: theme.colors.background.primary,
          borderTopWidth: 1,
          borderTopColor: theme.colors.gray[100],
          paddingTop: theme.spacing.sm,
          paddingBottom: theme.spacing['3xl'],
          height: theme.spacing['4xl'],
          shadowColor: theme.colors.shadow.primary,
          shadowOffset: { width: 0, height: -1 },
          shadowOpacity: 0.05,
          shadowRadius: 3,
          elevation: 5,
        },
        tabBarLabelStyle: {
          fontSize: theme.typography.fontSizes.xs,
          fontWeight: '500',
          marginBottom: theme.spacing.sm,
          marginTop: 0,
        },
        tabBarActiveTintColor: theme.colors.primary[500],
        tabBarInactiveTintColor: theme.colors.text.secondary,
      }}
    >
      <Tab.Screen
        name="Home"
        component={HomeScreen}
        options={{
          tabBarIcon: ({ focused }: { focused: boolean }) => <HomeIcon focused={focused} />,
        }}
      />
      <Tab.Screen
        name="Goals"
        component={GoalsScreen}
        options={{
          tabBarIcon: ({ focused }: { focused: boolean }) => <GoalsIcon focused={focused} />,
        }}
      />
      <Tab.Screen
        name="Transactions"
        component={TransactionsScreen}
        options={{
          tabBarIcon: ({ focused }: { focused: boolean }) => <TransactionsIcon focused={focused} />,
        }}
      />
      <Tab.Screen
        name="Budget"
        component={BudgetScreen}
        options={{
          tabBarIcon: ({ focused }: { focused: boolean }) => <BudgetIcon focused={focused} />,
        }}
      />
      <Tab.Screen
        name="Analytics"
        component={AnalyticsScreen}
        options={{
          tabBarIcon: ({ focused }: { focused: boolean }) => <AnalyticsIcon focused={focused} />,
        }}
      />
      <Tab.Screen
        name="More"
        component={MoreScreen}
        options={{
          tabBarIcon: ({ focused }: { focused: boolean }) => <MoreIcon focused={focused} />,
        }}
      />
    </Tab.Navigator>
  );
};

const styles = StyleSheet.create({
  iconContainer: {
    padding: theme.spacing.xs,
    borderRadius: theme.borderRadius.xl,
    marginTop: -theme.spacing.xs,
  },
  focusedIconContainer: {
    backgroundColor: theme.colors.primary[100],
  },
  iconText: {
    fontSize: theme.typography.fontSizes.xl,
    color: theme.colors.text.secondary,
  },
  focusedIconText: {
    color: theme.colors.primary[500],
  }
});

export default TabNavigator;