import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Dimensions,
  Image,
  FlatList,
  ActivityIndicator,
  Alert,
  Platform,
} from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation/types';
import theme from '../theme';
import Screen from '../components/common/Screen';
import Button from '../components/ui/Button';
import Card from '../components/ui/Card';
import { StatusBar } from 'expo-status-bar';
import { GoalsAPI, CreateGoalDto } from '../../src/api/goals.api';
import DateTimePicker from '@react-native-community/datetimepicker';
import STRINGS from '../constants/strings';

const { width } = Dimensions.get('window');
const CARD_WIDTH = width * 0.8;
const CARD_MARGIN = (width - CARD_WIDTH) / 2;

type InitialGoalSetupScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'InitialGoalSetup'
>;

interface GoalType {
  id: string;
  title: string;
  icon: string;
  color: string;
  description: string;
}

const goalTypes: GoalType[] = [
  {
    id: '1',
    title: STRINGS.GOALS.BUY_HOUSE,
    icon: '🏠',
    color: theme.colors.primary[100],
    description: STRINGS.GOALS.BUY_HOUSE_DESC
  },
  {
    id: '2',
    title: STRINGS.GOALS.BUY_CAR,
    icon: '🚗',
    color: theme.colors.warning[100],
    description: STRINGS.GOALS.BUY_CAR_DESC
  },
  {
    id: '3',
    title: STRINGS.GOALS.TRAVEL,
    icon: '✈️',
    color: theme.colors.success[100],
    description: STRINGS.GOALS.TRAVEL_DESC
  },
  {
    id: '4',
    title: STRINGS.GOALS.EDUCATION,
    icon: '🎓',
    color: theme.colors.secondary[100],
    description: STRINGS.GOALS.EDUCATION_DESC
  },
  {
    id: '5',
    title: STRINGS.GOALS.EMERGENCY_FUND,
    icon: '🚨',
    color: theme.colors.error[100],
    description: STRINGS.GOALS.EMERGENCY_FUND_DESC
  },
  {
    id: '6',
    title: STRINGS.GOALS.INVESTMENT,
    icon: '📈',
    color: theme.colors.primary[200],
    description: STRINGS.GOALS.INVESTMENT_DESC
  },
  {
    id: '7',
    title: STRINGS.GOALS.OTHER,
    icon: '✨',
    color: theme.colors.gray[200],
    description: STRINGS.GOALS.OTHER_DESC
  },
];

const InitialGoalSetupScreen = () => {
  const navigation = useNavigation<InitialGoalSetupScreenNavigationProp>();
  const [selectedGoalType, setSelectedGoalType] = useState<GoalType | null>(null);
  const [goalTitle, setGoalTitle] = useState('');
  const [description, setDescription] = useState('');
  const [targetAmount, setTargetAmount] = useState('');
  const [currentAmount, setCurrentAmount] = useState('');
  const [startDate, setStartDate] = useState(new Date());
  const [targetDate, setTargetDate] = useState(new Date());
  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showTargetDatePicker, setShowTargetDatePicker] = useState(false);
  const [step, setStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const flatListRef = useRef<FlatList>(null);
  const [currentIndex, setCurrentIndex] = useState(0);

  const formatCurrency = (text: string) => {
    const number = text.replace(/[^0-9]/g, '');
    return number.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  };

  const handleContinue = async () => {
    if (step === 1) {
      if (!selectedGoalType) {
        Alert.alert(STRINGS.COMMON.ERROR, 'Please select a goal type');
        return;
      }
      setStep(2);
      if (!goalTitle) {
        setGoalTitle(selectedGoalType.title);
      }
    } else {
      if (!goalTitle || !targetAmount || !currentAmount) {
        Alert.alert(STRINGS.COMMON.ERROR, STRINGS.GOALS.FILL_ALL_FIELDS);
        return;
      }

      if (startDate > targetDate) {
        Alert.alert(STRINGS.COMMON.ERROR, 'Start date must be before target date');
        return;
      }

      try {
        setLoading(true);
        const goalData: CreateGoalDto = {
          name: goalTitle,
          target_amount: parseFloat(targetAmount.replace(/,/g, '')),
          current_amount: parseFloat(currentAmount.replace(/,/g, '')),
          start_date: startDate.toISOString(),
          target_date: targetDate.toISOString(),
        };

        await GoalsAPI.createGoal(goalData);
        navigation.navigate('InitialBalance');
      } catch (error) {
        Alert.alert(STRINGS.COMMON.ERROR, STRINGS.GOALS.FAILED_TO_CREATE);
      } finally {
        setLoading(false);
      }
    }
  };

  const handleSkip = () => {
    navigation.navigate('InitialBalance');
  };

  const renderStepIndicator = () => (
    <View style={styles.stepIndicatorContainer}>
      <View style={[styles.stepIndicator, step >= 1 && styles.activeStepIndicator]}>
        <Text style={styles.stepIndicatorText}>1</Text>
      </View>
      <View style={[styles.stepConnector, step === 2 && { backgroundColor: theme.colors.primary[300] }]} />
      <View style={[styles.stepIndicator, step === 2 && styles.activeStepIndicator]}>
        <Text style={styles.stepIndicatorText}>2</Text>
      </View>
    </View>
  );

  const renderGoalItem = ({ item, index }: { item: GoalType; index: number }) => (
    <TouchableOpacity
      style={[
        styles.goalCard,
        { backgroundColor: item.color },
        selectedGoalType?.id === item.id && styles.selectedGoalCard,
      ]}
      onPress={() => {
        setSelectedGoalType(item);
        setCurrentIndex(index);
      }}
      activeOpacity={0.8}
    >
      <Text style={styles.goalIcon}>{item.icon}</Text>
      <Text style={styles.goalTitle}>{item.title}</Text>
      <Text style={styles.goalDescription}>{item.description}</Text>
      {selectedGoalType?.id === item.id && (
        <View style={styles.selectedIndicator}>
          <Text style={styles.selectedIndicatorText}>✓</Text>
        </View>
      )}
    </TouchableOpacity>
  );

  const renderStep1 = () => (
    <>
      <View style={styles.headerContainer}>
        <Text style={styles.headerTitle}>Choose Goal Type</Text>
        <Text style={styles.headerSubtitle}>
          Select the type of financial goal you want to achieve
        </Text>
      </View>

      <View style={styles.carouselWrapper}>
        <FlatList
          ref={flatListRef}
          data={goalTypes}
          renderItem={renderGoalItem}
          keyExtractor={(item) => item.id}
          horizontal
          showsHorizontalScrollIndicator={false}
          pagingEnabled
          snapToInterval={CARD_WIDTH + 20} // Card width + margin
          snapToAlignment="center"
          decelerationRate="fast"
          contentContainerStyle={styles.flatListContent}
          onMomentumScrollEnd={(e) => {
            const index = Math.round(e.nativeEvent.contentOffset.x / (CARD_WIDTH + 20));
            if (index >= 0 && index < goalTypes.length) {
              setCurrentIndex(index);
              setSelectedGoalType(goalTypes[index]);
            }
          }}
        />
      </View>

      {/* Page Indicator */}
      <View style={styles.pageIndicatorContainer}>
        {goalTypes.map((_, index) => (
          <View
            key={index}
            style={[
              styles.pageIndicator,
              currentIndex === index && styles.activePageIndicator,
            ]}
          />
        ))}
      </View>

      <Text style={styles.carouselHint}>Swipe left/right to see more</Text>
    </>
  );

  const renderStep2 = () => (
    <>
      <View style={styles.headerContainer}>
        <Text style={styles.headerTitle}>Goal Details</Text>
        <Text style={styles.headerSubtitle}>
          Fill in the details about your financial goal
        </Text>
      </View>

      <Card style={styles.setupContainer}>
        <Text style={styles.inputLabel}>{STRINGS.GOALS.GOAL_NAME}</Text>
        <TextInput
          style={styles.titleInput}
          value={goalTitle}
          onChangeText={setGoalTitle}
          placeholder={STRINGS.GOALS.ENTER_GOAL_NAME}
        />

        <Text style={styles.inputLabel}>{STRINGS.GOALS.DESCRIPTION} {STRINGS.GOALS.OPTIONAL}</Text>
        <TextInput
          style={[styles.titleInput, { height: 80, textAlignVertical: 'top' }]}
          value={description}
          onChangeText={setDescription}
          placeholder={STRINGS.GOALS.ENTER_DESCRIPTION}
          multiline
          numberOfLines={3}
        />

        <Text style={styles.inputLabel}>{STRINGS.GOALS.TARGET_AMOUNT}</Text>
        <View style={styles.amountInputContainer}>
          <Text style={styles.currencySymbol}>₫</Text>
          <TextInput
            style={styles.amountInput}
            value={targetAmount}
            onChangeText={(text) => setTargetAmount(formatCurrency(text))}
            placeholder="0"
            keyboardType="numeric"
          />
        </View>

        <Text style={styles.inputLabel}>{STRINGS.GOALS.CURRENT_AMOUNT}</Text>
        <View style={styles.amountInputContainer}>
          <Text style={styles.currencySymbol}>₫</Text>
          <TextInput
            style={styles.amountInput}
            value={currentAmount}
            onChangeText={(text) => setCurrentAmount(formatCurrency(text))}
            placeholder="0"
            keyboardType="numeric"
          />
        </View>

        <Text style={styles.inputLabel}>{STRINGS.GOALS.START_DATE}</Text>
        <Button
          title={startDate.toLocaleDateString('en-US')}
          onPress={() => setShowStartDatePicker(true)}
          variant="secondary"
          style={styles.dateButton}
        />

        <Text style={styles.inputLabel}>{STRINGS.GOALS.TARGET_DATE}</Text>
        <Button
          title={targetDate.toLocaleDateString('en-US')}
          onPress={() => setShowTargetDatePicker(true)}
          variant="secondary"
          style={styles.dateButton}
        />

        {showStartDatePicker && (
          <DateTimePicker
            value={startDate}
            mode="date"
            display={Platform.OS === 'ios' ? 'spinner' : 'default'}
            onChange={(event, selectedDate) => {
              if (Platform.OS === 'android') {
                setShowStartDatePicker(false);
              }
              if (event.type === 'set' && selectedDate) {
                setStartDate(selectedDate);
                if (Platform.OS === 'ios') {
                  setShowStartDatePicker(false);
                }
              } else if (event.type === 'dismissed') {
                setShowStartDatePicker(false);
              }
            }}
            minimumDate={new Date()}
          />
        )}

        {showTargetDatePicker && (
          <DateTimePicker
            value={targetDate}
            mode="date"
            display={Platform.OS === 'ios' ? 'spinner' : 'default'}
            onChange={(event, selectedDate) => {
              if (Platform.OS === 'android') {
                setShowTargetDatePicker(false);
              }
              if (event.type === 'set' && selectedDate) {
                setTargetDate(selectedDate);
                if (Platform.OS === 'ios') {
                  setShowTargetDatePicker(false);
                }
              } else if (event.type === 'dismissed') {
                setShowTargetDatePicker(false);
              }
            }}
            minimumDate={startDate}
          />
        )}
      </Card>
    </>
  );

  return (
    <Screen>
      <StatusBar style="dark" />
      <KeyboardAwareScrollView
        style={styles.container}
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
        enableOnAndroid={true}
        enableAutomaticScroll={true}
        extraScrollHeight={20}
        extraHeight={100}
      >
          {renderStepIndicator()}

          {step === 1 ? renderStep1() : renderStep2()}

          {step === 1 ? (
            <View style={styles.buttonContainer}>
              <Button
                title={STRINGS.COMMON.CONTINUE}
                onPress={handleContinue}
                variant="primary"
                fullWidth
              />
            </View>
          ) : (
            <View style={styles.buttonContainerStep2}>
              <TouchableOpacity
                style={styles.backButton}
                onPress={() => setStep(1)}
              >
                <Text style={styles.backButtonText}>{STRINGS.COMMON.BACK}</Text>
              </TouchableOpacity>

              <Button
                title={STRINGS.COMMON.COMPLETE}
                onPress={handleContinue}
                variant="primary"
                style={styles.continueButton}
                loading={loading}
              />
            </View>
          )}

          <TouchableOpacity
            style={styles.skipButton}
            onPress={handleSkip}
          >
            <Text style={styles.skipButtonText}>{STRINGS.COMMON.SKIP}</Text>
          </TouchableOpacity>
      </KeyboardAwareScrollView>
    </Screen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    paddingHorizontal: theme.spacing.lg,
    paddingBottom: theme.spacing.xl,
  },
  headerContainer: {
    alignItems: 'center',
    marginTop: theme.spacing.xl,
    marginBottom: theme.spacing.lg,
  },
  headerTitle: {
    fontSize: theme.typography.fontSizes['2xl'],
    fontWeight: '700',
    color: theme.colors.text.primary,
    textAlign: 'center',
    marginBottom: theme.spacing.sm,
  },
  headerSubtitle: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
    textAlign: 'center',
    paddingHorizontal: theme.spacing.lg,
  },
  stepIndicatorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: theme.spacing.xl,
    marginBottom: theme.spacing.lg,
  },
  stepIndicator: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: theme.colors.gray[200],
    justifyContent: 'center',
    alignItems: 'center',
  },
  activeStepIndicator: {
    backgroundColor: theme.colors.primary[500],
  },
  stepConnector: {
    flex: 0.2,
    height: 2,
    backgroundColor: theme.colors.gray[200],
  },
  stepIndicatorText: {
    color: theme.colors.background.primary,
    fontWeight: '700',
    fontSize: theme.typography.fontSizes.md,
  },
  carouselWrapper: {
    marginTop: theme.spacing.lg,
    marginBottom: theme.spacing.md,
    height: 240,
  },
  flatListContent: {
    alignItems: 'center',
    paddingHorizontal: 10,
  },
  pageIndicatorContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: theme.spacing.md,
    marginBottom: theme.spacing.sm,
  },
  pageIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: theme.colors.gray[300],
    marginHorizontal: 4,
  },
  activePageIndicator: {
    backgroundColor: theme.colors.primary[500],
    width: 12,
    height: 8,
    borderRadius: 4,
  },
  carouselHint: {
    textAlign: 'center',
    fontSize: theme.typography.fontSizes.xs,
    color: theme.colors.text.secondary,
    marginBottom: theme.spacing.xl,
  },
  goalCard: {
    width: CARD_WIDTH,
    height: 220,
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.lg,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 10,
    shadowColor: theme.colors.shadow.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  selectedGoalCard: {
    borderWidth: 2,
    borderColor: theme.colors.primary[500],
  },
  goalIcon: {
    fontSize: 48,
    marginBottom: theme.spacing.md,
  },
  goalTitle: {
    fontSize: theme.typography.fontSizes.xl,
    fontWeight: '700',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.sm,
    textAlign: 'center',
  },
  goalDescription: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
    textAlign: 'center',
  },
  selectedIndicator: {
    position: 'absolute',
    top: 10,
    right: 10,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: theme.colors.primary[500],
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectedIndicatorText: {
    color: theme.colors.background.primary,
    fontWeight: '700',
  },
  setupContainer: {
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.lg,
    marginTop: theme.spacing.sm,
  },
  inputLabel: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '500',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.sm,
    marginTop: theme.spacing.md,
  },
  titleInput: {
    backgroundColor: theme.colors.gray[100],
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.md,
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.primary,
  },
  amountInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.gray[100],
    borderRadius: theme.borderRadius.md,
    marginBottom: theme.spacing.md,
  },
  currencySymbol: {
    fontSize: theme.typography.fontSizes.lg,
    color: theme.colors.text.secondary,
    paddingHorizontal: theme.spacing.md,
  },
  amountInput: {
    flex: 1,
    padding: theme.spacing.md,
    fontSize: theme.typography.fontSizes.lg,
    color: theme.colors.text.primary,
  },
  buttonContainer: {
    marginTop: theme.spacing.xl,
  },
  buttonContainerStep2: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: theme.spacing.xl,
  },
  backButton: {
    padding: theme.spacing.md,
  },
  backButtonText: {
    color: theme.colors.primary[500],
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '500',
  },
  continueButton: {
    flex: 1,
    marginLeft: theme.spacing.md,
  },
  skipButton: {
    alignSelf: 'center',
    marginTop: theme.spacing.xl,
  },
  skipButtonText: {
    color: theme.colors.text.secondary,
    fontSize: theme.typography.fontSizes.md,
    textDecorationLine: 'underline',
  },
  dateButton: {
    marginTop: theme.spacing.md,
    marginBottom: theme.spacing.md,
  },
});

export default InitialGoalSetupScreen;