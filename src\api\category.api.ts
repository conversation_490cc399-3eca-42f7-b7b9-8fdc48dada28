import axios from '../lib/axios';

// Enums (match backend exactly)
export enum CategoryType {
  INCOME = 'income',
  EXPENSE = 'expense'
}

export enum CategoryStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive'
}

// DTOs (match backend DTOs exactly)
export interface CreateCategoryDto {
  name: string;
  type: CategoryType;
}

export interface UpdateCategoryDto {
  name?: string;
  type?: CategoryType;
  status?: CategoryStatus;
}

export interface FindAllCategoryDto {
  page?: number;
  limit?: number;
  status?: CategoryStatus;
  type?: CategoryType;
}

export interface FindOneCategoryDto {
  page?: number;
  limit?: number;
}

// Category Entity (match backend response)
export interface ICategory {
  id: string;
  user_id: string;
  name: string;
  type: CategoryType;
  status: CategoryStatus;
  created_at: string;
  updated_at: string;
}

// API Response structures
export interface CategoriesResponse {
  success: boolean;
  data: ICategory[];
  total: number;
  page: number;
  limit: number;
}

const CATEGORIES_ENDPOINT = '/categories';

export const CategoryAPI = {
  // Create a new category
  create: async (data: CreateCategoryDto): Promise<ICategory> => {
    const response = await axios.post(CATEGORIES_ENDPOINT, data);
    return response.data.data;
  },

  // Get all categories with filtering and pagination
  getAll: async (query?: FindAllCategoryDto): Promise<CategoriesResponse> => {

    const params = {
      page: query?.page || 1,
      limit: query?.limit || 10,
      ...(query?.status && { status: query.status }),
      ...(query?.type && { type: query.type })
    };

    const response = await axios.get(CATEGORIES_ENDPOINT, { params });

    // Handle backend response structure
    if (response.data && response.data.data) {
      return {
        success: true,
        data: response.data.data,
        total: response.data.total || response.data.data.length,
        page: response.data.page || params.page,
        limit: response.data.limit || params.limit
      };
    } else {
      console.warn('Unexpected response structure:', response.data);
      return {
        success: false,
        data: [],
        total: 0,
        page: params.page,
        limit: params.limit
      };
    }
  },

  // Get category by ID with optional query params
  getOne: async (id: string, query?: FindOneCategoryDto): Promise<ICategory> => {

    const params = {
      ...(query?.page && { page: query.page }),
      ...(query?.limit && { limit: query.limit })
    };

    const response = await axios.get(`${CATEGORIES_ENDPOINT}/${id}`, { params });

    // Handle backend response - might include transactions
    if (response.data && response.data.data) {
      return response.data.data.category || response.data.data;
    } else {
      return response.data;
    }
  },

  // Update category
  update: async (id: string, data: UpdateCategoryDto): Promise<ICategory> => {
    const response = await axios.patch(`${CATEGORIES_ENDPOINT}/${id}`, data);
    return response.data.data;
  },

  // Delete category
  delete: async (id: string): Promise<void> => {
    await axios.delete(`${CATEGORIES_ENDPOINT}/${id}`);
  },

  // Helper method to get expense categories only
  getExpenseCategories: async (page: number = 1, limit: number = 100): Promise<ICategory[]> => {
    const response = await CategoryAPI.getAll({
      type: CategoryType.EXPENSE, // 'expense'
      status: CategoryStatus.ACTIVE, // 'active'
      page,
      limit
    });
    return response.data;
  },

  // Helper method to get income categories only
  getIncomeCategories: async (page: number = 1, limit: number = 100): Promise<ICategory[]> => {
    const response = await CategoryAPI.getAll({
      type: CategoryType.INCOME, // 'income'
      status: CategoryStatus.ACTIVE, // 'active'
      page,
      limit
    });
    return response.data;
  }
};
