# React Native + Expo + NativeWind Template

A cross-platform mobile app template built with React Native, Expo, NativeWind (TailwindCSS) and TypeScript. This template provides a solid foundation for building mobile applications with a clean architecture, reusable components, and a modern development workflow.

## Features

- **Cross-Platform**: Works on iOS and Android
- **TypeScript**: Type-safe code
- **NativeWind**: TailwindCSS for React Native
- **React Navigation**: Stack and Tab navigation
- **Form Handling**: Form validation with react-hook-form
- **Keyboard Handling**: Automatic keyboard avoidance
- **Reusable Components**: Button, Card, TextInput, etc.
- **Centralized Theme**: Consistent styling across the app

## Project Structure

```
/app
├── components
│   ├── ui/            # Reusable UI components
│   └── common/        # Shared layout components
├── screens/           # App screens
├── navigation/        # Navigation configuration
├── theme/             # Theme system (colors, spacing, etc.)
├── hooks/             # Custom hooks
└── utils/             # Utilities and helpers
```

## Getting Started

### Prerequisites

- Node.js (>= 14)
- npm or yarn
- Expo CLI
- iOS Simulator or Android Emulator (optional)

### Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   # or
   yarn install
   ```
3. Start the development server:
   ```bash
   npm start
   # or
   yarn start
   ```

## Components

### UI Components

- **Button**: Customizable button with different variants (primary, secondary, outline, ghost)
- **Card**: Container component with different variants (elevated, outlined, flat)
- **TextInput**: Input component with validation support
- **FormInput**: TextInput with form integration

### Layout Components

- **Screen**: Base component for all screens with SafeArea and StatusBar handling
- **KeyboardAwareView**: Component that handles keyboard behavior

## Screens

- **HomeScreen**: Showcases various UI components
- **FormScreen**: Demonstrates form handling with validation
- **ProfileScreen**: User profile example
- **SettingsScreen**: Settings with toggles and options
- **DetailsScreen**: Example of a detail view with navigation parameters

## Navigation

- **TabNavigator**: Bottom tab navigation
- **RootNavigator**: Stack navigation

## Theme

The theme system is centralized in the `app/theme` directory. It includes:

- Colors
- Spacing
- Typography
- Border radius

## License

MIT 