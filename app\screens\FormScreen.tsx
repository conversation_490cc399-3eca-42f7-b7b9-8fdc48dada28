import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  Switch,
  Alert,
  TextInput as RNTextInput, // Import with alias for typing
} from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import theme from '../theme';
import Screen from '../components/common/Screen';
import Button from '../components/ui/Button';
import Card from '../components/ui/Card';

// Form data interface
interface FormData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  phone: string;
  bio: string;
  notifications: boolean;
}

// Error state interface
interface FormErrors {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  phone: string;
}

const FormScreen = () => {
  // Form state
  const [formData, setFormData] = useState<FormData>({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    phone: '',
    bio: '',
    notifications: true
  });

  // Error state
  const [errors, setErrors] = useState<FormErrors>({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    phone: '',
  });

  // Input refs for focus handling
  const lastNameRef = useRef<RNTextInput>(null);
  const emailRef = useRef<RNTextInput>(null);
  const passwordRef = useRef<RNTextInput>(null);
  const phoneRef = useRef<RNTextInput>(null);
  const bioRef = useRef<RNTextInput>(null);

  // Handle input changes
  const handleChange = (field: keyof FormData, value: string) => {
    setFormData({
      ...formData,
      [field]: value
    });

    // Clear error when user types
    if (field in errors) {
      setErrors({
        ...errors,
        [field]: ''
      });
    }
  };

  // Toggle notifications switch
  const toggleNotifications = () => {
    setFormData({
      ...formData,
      notifications: !formData.notifications
    });
  };

  // Form validation
  const validateForm = () => {
    let isValid = true;
    const newErrors = { ...errors };

    // First name validation
    if (!formData.firstName.trim()) {
      newErrors.firstName = 'First name is required';
      isValid = false;
    }

    // Last name validation
    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Last name is required';
      isValid = false;
    }

    // Email validation
    const emailRegex = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
      isValid = false;
    } else if (!emailRegex.test(formData.email)) {
      newErrors.email = 'Invalid email address';
      isValid = false;
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = 'Password is required';
      isValid = false;
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
      isValid = false;
    }

    // Phone validation (optional)
    if (formData.phone && !/^\d{10}$/.test(formData.phone.replace(/[^0-9]/g, ''))) {
      newErrors.phone = 'Please enter a valid 10-digit phone number';
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  // Handle form submission
  const handleSubmit = () => {
    if (validateForm()) {
      // Form is valid, show success message
      Alert.alert(
        'Success',
        'Form submitted successfully!',
        [
          { text: 'OK' }
        ]
      );

      // Reset form
      setFormData({
        firstName: '',
        lastName: '',
        email: '',
        password: '',
        phone: '',
        bio: '',
        notifications: true
      });
    }
  };

  const handleCancel = () => {
    // Reset form or navigate back
    Alert.alert(
      'Confirm',
      'Are you sure you want to cancel?',
      [
        { text: 'No', style: 'cancel' },
        {
          text: 'Yes',
          onPress: () => {
            setFormData({
              firstName: '',
              lastName: '',
              email: '',
              password: '',
              phone: '',
              bio: '',
              notifications: true
            });
          }
        }
      ]
    );
  };

  return (
    <Screen>
      <KeyboardAwareScrollView
        style={styles.container}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
        enableOnAndroid={true}
        enableAutomaticScroll={true}
        extraScrollHeight={20}
        extraHeight={100}
      >
          <View style={styles.header}>
            <Text style={styles.title}>Profile Information</Text>
            <Text style={styles.subtitle}>
              Please fill in your information below
            </Text>
          </View>

          {/* Form Fields */}
          <Card style={styles.form}>
            {/* First Name */}
            <View style={styles.formGroup}>
              <Text style={styles.label}>First Name</Text>
              <TextInput
                style={[styles.input, errors.firstName ? styles.inputError : null]}
                placeholder="Enter your first name"
                value={formData.firstName}
                onChangeText={(text) => handleChange('firstName', text)}
                returnKeyType="next"
                onSubmitEditing={() => lastNameRef.current?.focus()}
                autoCapitalize="words"
              />
              {errors.firstName ? (
                <Text style={styles.errorText}>{errors.firstName}</Text>
              ) : null}
            </View>

            {/* Last Name */}
            <View style={styles.formGroup}>
              <Text style={styles.label}>Last Name</Text>
              <TextInput
                ref={lastNameRef}
                style={[styles.input, errors.lastName ? styles.inputError : null]}
                placeholder="Enter your last name"
                value={formData.lastName}
                onChangeText={(text) => handleChange('lastName', text)}
                returnKeyType="next"
                onSubmitEditing={() => emailRef.current?.focus()}
                autoCapitalize="words"
              />
              {errors.lastName ? (
                <Text style={styles.errorText}>{errors.lastName}</Text>
              ) : null}
            </View>

            {/* Email */}
            <View style={styles.formGroup}>
              <Text style={styles.label}>Email</Text>
              <TextInput
                ref={emailRef}
                style={[styles.input, errors.email ? styles.inputError : null]}
                placeholder="Enter your email address"
                value={formData.email}
                onChangeText={(text) => handleChange('email', text)}
                returnKeyType="next"
                onSubmitEditing={() => passwordRef.current?.focus()}
                keyboardType="email-address"
                autoCapitalize="none"
              />
              {errors.email ? (
                <Text style={styles.errorText}>{errors.email}</Text>
              ) : null}
            </View>

            {/* Password */}
            <View style={styles.formGroup}>
              <Text style={styles.label}>Password</Text>
              <TextInput
                ref={passwordRef}
                style={[styles.input, errors.password ? styles.inputError : null]}
                placeholder="Enter your password"
                value={formData.password}
                onChangeText={(text) => handleChange('password', text)}
                secureTextEntry
                returnKeyType="next"
                onSubmitEditing={() => phoneRef.current?.focus()}
              />
              {errors.password ? (
                <Text style={styles.errorText}>{errors.password}</Text>
              ) : null}
            </View>

            {/* Phone */}
            <View style={styles.formGroup}>
              <Text style={styles.label}>Phone Number (Optional)</Text>
              <TextInput
                ref={phoneRef}
                style={[styles.input, errors.phone ? styles.inputError : null]}
                placeholder="Enter your phone number"
                value={formData.phone}
                onChangeText={(text) => handleChange('phone', text)}
                returnKeyType="next"
                onSubmitEditing={() => bioRef.current?.focus()}
                keyboardType="phone-pad"
              />
              {errors.phone ? (
                <Text style={styles.errorText}>{errors.phone}</Text>
              ) : null}
            </View>

            {/* Bio */}
            <View style={styles.formGroup}>
              <Text style={styles.label}>Bio (Optional)</Text>
              <TextInput
                ref={bioRef}
                style={[styles.textArea]}
                placeholder="Tell us about yourself"
                value={formData.bio}
                onChangeText={(text) => handleChange('bio', text)}
                multiline
                numberOfLines={4}
                textAlignVertical="top"
              />
            </View>

            {/* Switch */}
            <View style={styles.switchContainer}>
              <Text style={styles.switchLabel}>
                Receive notifications
              </Text>
              <Switch
                value={formData.notifications}
                onValueChange={toggleNotifications}
                trackColor={{ false: theme.colors.gray[200], true: theme.colors.primary[500] }}
                thumbColor={theme.colors.background.primary}
              />
            </View>
          </Card>

          {/* Buttons */}
          <View style={styles.buttonContainer}>
            <Button
              title="Submit"
              onPress={handleSubmit}
              variant="primary"
              fullWidth
              style={styles.submitButton}
            />

            <Button
              title="Cancel"
              onPress={handleCancel}
              variant="outline"
              fullWidth
              style={styles.cancelButton}
            />
          </View>

          {/* Privacy Notice */}
          <Text style={styles.privacyText}>
            By submitting this form, you agree to our Terms of Service and Privacy Policy.
          </Text>
      </KeyboardAwareScrollView>
    </Screen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    padding: theme.spacing.md,
    paddingBottom: theme.spacing.xl,
  },
  header: {
    marginBottom: theme.spacing.xl,
  },
  title: {
    fontSize: theme.typography.fontSizes['2xl'],
    fontWeight: '700',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.sm,
  },
  subtitle: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.secondary,
  },
  form: {
    marginBottom: theme.spacing.xl,
    padding: theme.spacing.md,
  },
  formGroup: {
    marginBottom: theme.spacing.md,
  },
  label: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '500',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.sm,
  },
  input: {
    backgroundColor: theme.colors.gray[50],
    borderWidth: 1,
    borderColor: theme.colors.gray[300],
    borderRadius: theme.borderRadius.md,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.primary,
  },
  inputError: {
    borderColor: theme.colors.error[500],
    backgroundColor: theme.colors.error[50],
  },
  textArea: {
    backgroundColor: theme.colors.gray[50],
    borderWidth: 1,
    borderColor: theme.colors.gray[300],
    borderRadius: theme.borderRadius.md,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    fontSize: theme.typography.fontSizes.md,
    height: 100,
    color: theme.colors.text.primary,
  },
  errorText: {
    color: theme.colors.error[500],
    fontSize: theme.typography.fontSizes.sm,
    marginTop: theme.spacing.xs,
  },
  switchContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: theme.spacing.md,
  },
  switchLabel: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.primary,
  },
  buttonContainer: {
    marginBottom: theme.spacing.xl,
  },
  submitButton: {
    marginBottom: theme.spacing.sm,
  },
  cancelButton: {
    borderColor: theme.colors.gray[300],
  },
  privacyText: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
    textAlign: 'center',
  },
});

export default FormScreen;