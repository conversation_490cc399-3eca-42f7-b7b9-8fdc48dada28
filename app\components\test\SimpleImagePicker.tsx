import React from 'react';
import { View, Text, TouchableOpacity, Alert, StyleSheet } from 'react-native';
import * as ImagePicker from 'expo-image-picker';

const SimpleImagePicker = () => {
  const testImagePicker = async () => {
    console.log('=== Testing Image Picker ===');
    
    try {
      // Test permissions
      console.log('1. Testing permissions...');
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      console.log('Permission status:', status);
      
      if (status !== 'granted') {
        Alert.alert('Permission denied', 'We need camera roll permissions to make this work!');
        return;
      }

      // Test image picker
      console.log('2. Launching image picker...');
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false,
        quality: 1,
      });

      console.log('3. Image picker result:', result);
      
      if (!result.canceled) {
        Alert.alert('Success!', `Selected image: ${result.assets[0].uri}`);
      } else {
        console.log('User canceled image picker');
      }
    } catch (error) {
      console.error('Error in image picker:', error);
      Alert.alert('Error', `Image picker failed: ${error.message}`);
    }
  };

  const testCamera = async () => {
    console.log('=== Testing Camera ===');
    
    try {
      // Test camera permissions
      console.log('1. Testing camera permissions...');
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      console.log('Camera permission status:', status);
      
      if (status !== 'granted') {
        Alert.alert('Permission denied', 'We need camera permissions to make this work!');
        return;
      }

      // Test camera
      console.log('2. Launching camera...');
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false,
        quality: 1,
      });

      console.log('3. Camera result:', result);
      
      if (!result.canceled) {
        Alert.alert('Success!', `Took photo: ${result.assets[0].uri}`);
      } else {
        console.log('User canceled camera');
      }
    } catch (error) {
      console.error('Error in camera:', error);
      Alert.alert('Error', `Camera failed: ${error.message}`);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Image Picker Test</Text>
      
      <TouchableOpacity style={styles.button} onPress={testImagePicker}>
        <Text style={styles.buttonText}>Test Image Library</Text>
      </TouchableOpacity>
      
      <TouchableOpacity style={styles.button} onPress={testCamera}>
        <Text style={styles.buttonText}>Test Camera</Text>
      </TouchableOpacity>
      
      <Text style={styles.note}>
        Check console logs for detailed information
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 20,
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 15,
    borderRadius: 8,
    marginVertical: 10,
    minWidth: 200,
    alignItems: 'center',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  note: {
    marginTop: 20,
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
});

export default SimpleImagePicker;
