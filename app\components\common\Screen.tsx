import React from 'react';
import { View, StatusBar, StatusBarStyle, ViewProps, StyleSheet, Platform } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import theme from '../../theme';

interface ScreenProps extends ViewProps {
  children: React.ReactNode;
  statusBarStyle?: StatusBarStyle;
  statusBarColor?: string;
  edges?: Array<'top' | 'right' | 'bottom' | 'left'>;
  safeAreaTop?: boolean;
  safeAreaBottom?: boolean;
  backgroundColor?: string;
  style?: any;
}

const Screen: React.FC<ScreenProps> = ({
  children,
  statusBarStyle = 'dark-content',
  statusBarColor = 'transparent',
  edges = ['top', 'right', 'left'],
  safeAreaTop = true,
  safeAreaBottom = false,
  backgroundColor = theme.colors.background.primary,
  style,
  ...props
}) => {
  // Determine which edges to use for safe area
  const safeAreaEdges = [...edges];
  if (safeAreaBottom && !safeAreaEdges.includes('bottom')) {
    safeAreaEdges.push('bottom');
  }

  return (
    <SafeAreaView
      edges={safeAreaEdges}
      style={[styles.safeArea, { backgroundColor }, style]}
      {...props}
    >
      <StatusBar
        translucent
        backgroundColor={statusBarColor}
        barStyle={statusBarStyle}
      />
      <View style={styles.container}>
        {children}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
  },
  container: {
    flex: 1,
    marginBottom: 50,
  }
});

export default Screen;