import axios from '../lib/axios';

const GOALS_ENDPOINT = '/goals';

export enum GoalStatus {
    PENDING = 'pending',
    ACTIVE = 'active',
    COMPLETED = 'completed',
    CANCELLED = 'cancelled',
    PAUSED = 'paused'
}

export interface IGoal {
    id: string;
    name: string;
    description?: string;
    target_amount: number;
    current_amount: number;
    target_date: string;
    start_date: string;
    status: GoalStatus;
    user_id: string;
    created_at: string;
    updated_at: string;
}

export interface CreateGoalDto {
    name: string;
    target_amount: number;
    current_amount: number;
    start_date: string;
    target_date: string;
}

export interface UpdateGoalDto {
    name?: string;
    target_amount?: number;
    current_amount?: number;
    start_date?: string;
    target_date?: string;
    status?: GoalStatus;
}

export interface GoalQueryParams {
    status?: string;
    page?: number;
    limit?: number;
}

export const GoalsAPI = {
    // Create a new goal
    createGoal: async (goalData: CreateGoalDto): Promise<IGoal> => {
        const response = await axios.post(GOALS_ENDPOINT, goalData);
        return response.data.data; // Backend returns { data: Goal }
    },

    // Get all goals with pagination and filters
    getGoals: async (params: GoalQueryParams = {}) => {
        const response = await axios.get(GOALS_ENDPOINT, { params });
        return response.data; // Backend returns { data: Goal[], total, page, limit }
    },

    // Get a single goal by ID
    getGoalById: async (goalId: string): Promise<IGoal> => {
        const response = await axios.get(`${GOALS_ENDPOINT}/${goalId}`);
        return response.data.data; // Backend returns { data: Goal }
    },

    // Update a goal
    updateGoal: async (goalId: string, updateData: UpdateGoalDto): Promise<IGoal> => {
        const response = await axios.patch(`${GOALS_ENDPOINT}/${goalId}`, updateData);
        return response.data.data; // Backend returns { data: Goal }
    },

    // Delete a goal
    deleteGoal: async (goalId: string): Promise<{ success: boolean }> => {
        const response = await axios.delete(`${GOALS_ENDPOINT}/${goalId}`);
        return response.data.data; // Backend returns { data: { success: boolean } }
    }
};