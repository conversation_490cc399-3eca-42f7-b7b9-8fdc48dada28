// English text constants for the application
export const STRINGS = {
  // Common
  COMMON: {
    SAVE: 'Save',
    CANCEL: 'Cancel',
    DELETE: 'Delete',
    EDIT: 'Edit',
    UPDATE: 'Update',
    ADD: 'Add',
    CONTINUE: 'Continue',
    BACK: 'Back',
    NEXT: 'Next',
    SKIP: 'Skip',
    COMPLETE: 'Complete',
    RETRY: 'Retry',
    LOADING: 'Loading...',
    ERROR: 'Error',
    SUCCESS: 'Success',
    CONFIRM: 'Confirm',
    OR: 'Or',
    ALL: 'All',
    SEARCH: 'Search',
    FILTER: 'Filter',
    CLOSE: 'Close',
    DONE: 'Done',
  },

  // Authentication
  AUTH: {
    LOGIN: 'Login',
    REGISTER: 'Register',
    LOGOUT: 'Logout',
    EMAIL: 'Email',
    PASSWORD: 'Password',
    CONFIRM_PASSWORD: 'Confirm Password',
    PHONE: 'Phone Number',
    FORGOT_PASSWORD: 'Forgot Password?',
    LOGIN_WITH_GOOGLE: '🔶 Login with Google',
    NO_ACCOUNT: "Don't have an account? ",
    REGISTER_NOW: 'Register now',
    EMAIL_OR_PHONE: 'Email or phone number',
    ENTER_PASSWORD: 'Enter password',
    CREATE_ACCOUNT: 'Create Account',
    ENTER_ACCOUNT_INFO: 'Enter account information to get started',
    SMART_FINANCE_MANAGEMENT: 'Smart Finance Management',
  },

  // Home Screen
  HOME: {
    HELLO: 'Hello!',
    DASHBOARD: 'Dashboard',
    TOTAL_BALANCE: 'Total Balance',
    MONTHLY_INCOME: 'Monthly Income',
    MONTHLY_EXPENSES: 'Monthly Expenses',
    YOUR_GOALS: 'Your Goals',
    VIEW_ALL: 'View All',
    RECENT_TRANSACTIONS: 'Recent Transactions',
    ADD_TRANSACTION: 'Add Transaction',
    SETUP_INITIAL_BALANCE: 'Setup Initial Balance',
    SETUP_BALANCE_DESC: 'Set up your initial balance to start tracking your finances',
    SETUP_NOW: 'Setup Now',
    DISMISS: 'Dismiss',
  },

  // Goals
  GOALS: {
    YOUR_GOALS: 'Your Goals',
    TRACK_MANAGE_GOALS: 'Track and manage your financial goals',
    ADD_NEW_GOAL: 'Add New Goal',
    NO_GOALS_YET: 'No goals yet',
    NO_GOALS_DESC: "You haven't created any goals yet. Add your first goal!",
    GOAL_NAME: 'Goal Name',
    TARGET_AMOUNT: 'Target Amount',
    CURRENT_AMOUNT: 'Current Amount',
    START_DATE: 'Start Date',
    TARGET_DATE: 'Target Date',
    DUE_DATE: 'Due Date',
    STATUS: 'Status',
    PROGRESS: 'Progress',
    ENTER_GOAL_NAME: 'Enter goal name',
    ENTER_TARGET_AMOUNT: 'Enter target amount',
    ENTER_CURRENT_AMOUNT: 'Enter current amount',
    UPDATE_GOAL: 'Update Goal',
    DELETE_GOAL: 'Delete Goal',
    GOAL_DETAILS: 'Goal Details',
    CREATED_DATE: 'Created Date',
    GOAL_TYPE: 'Goal Type',
    DESCRIPTION: 'Description',
    OPTIONAL: '(optional)',
    ENTER_DESCRIPTION: 'Enter detailed description of your goal',

    // Goal Types
    BUY_HOUSE: 'Buy House',
    BUY_CAR: 'Buy Car',
    TRAVEL: 'Travel',
    EDUCATION: 'Education',
    EMERGENCY_FUND: 'Emergency Fund',
    INVESTMENT: 'Investment',
    WEDDING: 'Wedding',
    SAVINGS: 'Savings',
    OTHER: 'Other',

    // Goal Type Descriptions
    BUY_HOUSE_DESC: 'Save to buy or make a down payment on a new house',
    BUY_CAR_DESC: 'Save to buy a new or used car',
    TRAVEL_DESC: 'Save for vacations and trips',
    EDUCATION_DESC: 'Save for tuition and personal development',
    EMERGENCY_FUND_DESC: 'Reserve for emergency situations',
    INVESTMENT_DESC: 'Save to invest and grow assets',
    WEDDING_DESC: 'Save for wedding expenses',
    SAVINGS_DESC: 'General savings goal',
    OTHER_DESC: 'Custom goal',

    // Goal Status
    ACTIVE: 'Active',
    COMPLETED: 'Completed',
    CANCELLED: 'Cancelled',
    PAUSED: 'Paused',
    PENDING: 'Pending',
    UNKNOWN: 'Unknown',

    // Messages
    GOAL_CREATED_SUCCESS: 'Goal created successfully',
    GOAL_UPDATED_SUCCESS: 'Goal updated successfully',
    GOAL_DELETED_SUCCESS: 'Goal deleted successfully',
    DELETE_GOAL_CONFIRM: 'Are you sure you want to delete this goal? This action cannot be undone.',
    FILL_ALL_FIELDS: 'Please fill in all required fields',
    FAILED_TO_CREATE: 'Failed to create goal. Please try again later.',
    FAILED_TO_UPDATE: 'Failed to update goal. Please try again later.',
    FAILED_TO_DELETE: 'Failed to delete goal. Please try again later.',
    FAILED_TO_LOAD: 'Failed to load goal information. Please try again later.',
  },

  // Transactions
  TRANSACTIONS: {
    TRANSACTIONS: 'Transactions',
    MANAGE_ALL_TRANSACTIONS: 'Manage all your transactions',
    SEARCH_TRANSACTIONS: 'Search transactions...',
    INCOME: 'Income',
    EXPENSE: 'Expense',
    ADD_TRANSACTION: 'Add Transaction',
    TRANSACTION_TYPE: 'Transaction Type',
    AMOUNT: 'Amount',
    CATEGORY: 'Category',
    DATE: 'Date',
    DESCRIPTION: 'Description',
    ENTER_AMOUNT: 'Enter amount',
    ENTER_DESCRIPTION: 'Enter description',
    SELECT_CATEGORY: 'Select Category',

    // Categories
    FOOD: 'Food & Dining',
    TRANSPORT: 'Transportation',
    SHOPPING: 'Shopping',
    ENTERTAINMENT: 'Entertainment',
    HOUSING: 'Housing',
    HEALTH: 'Healthcare',
    EDUCATION: 'Education',
    SALARY: 'Salary',
    BONUS: 'Bonus',
    INVESTMENT: 'Investment',
    GIFT: 'Gift',
    OTHER_INCOME: 'Other Income',
    OTHER_EXPENSE: 'Other Expense',
  },

  // Budget
  BUDGET: {
    BUDGET: 'Budget',
    MONTHLY_BUDGET: 'Monthly Budget',
    SPENT: 'Spent',
    REMAINING: 'Remaining',
    TOTAL_BUDGET: 'Total Budget',
    OVER_BUDGET: 'Over Budget',
    EXCEEDED: 'exceeded',
    MONTHLY_TREND: 'Monthly Trend',
    SETUP_BUDGET: 'Setup Budget',
    BUDGET_AMOUNT: 'Budget Amount',
    PERIOD: 'Period',
    MONTHLY: 'Monthly',
    WEEKLY: 'Weekly',
    YEARLY: 'Yearly',
    NOTE: 'Note',
    ENTER_NOTE: 'Enter note (optional)',
  },

  // Time Filters
  TIME: {
    THIS_WEEK: 'This Week',
    THIS_MONTH: 'This Month',
    THIS_YEAR: 'This Year',
    WEEK: 'Week',
    MONTH: 'Month',
    YEAR: 'Year',
  },

  // Error Messages
  ERRORS: {
    NETWORK_ERROR: 'Network error. Please check your connection.',
    SOMETHING_WENT_WRONG: 'Something went wrong. Please try again.',
    INVALID_INPUT: 'Invalid input. Please check your data.',
    UNAUTHORIZED: 'Unauthorized access. Please login again.',
    NOT_FOUND: 'Resource not found.',
    SERVER_ERROR: 'Server error. Please try again later.',
  },

  // Success Messages
  SUCCESS: {
    OPERATION_SUCCESSFUL: 'Operation completed successfully',
    DATA_SAVED: 'Data saved successfully',
    DATA_UPDATED: 'Data updated successfully',
    DATA_DELETED: 'Data deleted successfully',
  },

  // Navigation
  NAV: {
    HOME: 'Home',
    GOALS: 'Goals',
    TRANSACTIONS: 'Transactions',
    BUDGET: 'Budget',
    ANALYTICS: 'Analytics',
    PROFILE: 'Profile',
    SETTINGS: 'Settings',
  },

  // Currency
  CURRENCY: {
    VND: 'VND',
    USD: 'USD',
    CURRENCY_SYMBOL: '₫',
  },

  // Date Formats
  DATE: {
    TODAY: 'Today',
    YESTERDAY: 'Yesterday',
    TOMORROW: 'Tomorrow',
    DUE_ON: 'Due on',
    CREATED_ON: 'Created on',
  },

  // Analytics
  ANALYTICS: {
    ANALYTICS: 'Analytics',
    SPENDING_OVERVIEW: 'Spending Overview',
    INCOME_VS_EXPENSES: 'Income vs Expenses',
    CATEGORY_BREAKDOWN: 'Category Breakdown',
    MONTHLY_COMPARISON: 'Monthly Comparison',
  },

  // Profile & Settings
  PROFILE: {
    PROFILE: 'Profile',
    SETTINGS: 'Settings',
    PERSONAL_INFO: 'Personal Information',
    PREFERENCES: 'Preferences',
    NOTIFICATIONS: 'Notifications',
    SECURITY: 'Security',
    ABOUT: 'About',
    LOGOUT: 'Logout',
  },

  // Onboarding
  ONBOARDING: {
    WELCOME: 'Welcome',
    GET_STARTED: 'Get Started',
    SETUP_COMPLETE: 'Setup Complete',
    INITIAL_BALANCE: 'Initial Balance',
    SET_INITIAL_BALANCE: 'Set your initial balance to start tracking',
    ENTER_BALANCE: 'Enter your current balance',
    WHAT_IS_YOUR_GOAL: 'What is your goal?',
    SETUP_FIRST_GOAL: 'Set up your first financial goal',
    GOAL_AMOUNT: 'Goal Amount',
    TIME_FRAME: 'Time Frame',
    MONTHS: 'months',
    MONTHLY_SAVINGS: 'Monthly savings needed',
  },

  // Features (for login screen carousel)
  FEATURES: {
    EASY_FINANCE_MANAGEMENT: 'Easy Finance Management',
    EASY_FINANCE_DESC: 'Track income and expenses efficiently anytime, anywhere',
    SET_FINANCIAL_GOALS: 'Set Financial Goals',
    SET_GOALS_DESC: 'Plan and achieve your personal financial goals',
    SPENDING_ANALYSIS: 'Spending Analysis',
    SPENDING_DESC: 'Visual charts help you understand your spending habits',
  },
};

export default STRINGS;
