# Hướng Dẫn Sử Dụng Theme

## Giới Thiệu

Tài liệu này hướng dẫn cách sử dụng hệ thống theme đã được thiết lập để đảm bảo giao diện nhất quán trong toàn bộ ứng dụng. Bằng cách tuân theo hướng dẫn này, chúng ta có thể:

- <PERSON><PERSON><PERSON> bảo tính nhất quán trong thiết kế
- <PERSON><PERSON> dàng cập nhật giao diện toàn cục
- Giảm mã lặp lại
- Đơn giản hóa việc tạo theme tối/sáng

## Cấu Trúc Theme

File `app/theme/index.ts` chứa tất cả các cấu hình theme, bao gồm:

```typescript
const theme = {
  colors,       // Màu sắc
  spacing,      // Khoảng cách
  borderRadius, // Bo góc
  typography,   // Ki<PERSON>u chữ
  buttons,      // Nút
  cards,        // Thẻ
  inputs,       // Trường nhập liệu
  icons,        // Biểu tượng
};
```

## Cách Sử Dụng Theme

### 1. Import Theme

```typescript
import theme from '../theme';
```

### 2. Sử Dụng Colors

```typescript
// Ví dụ:
const styles = StyleSheet.create({
  container: {
    backgroundColor: theme.colors.background.primary,
    borderColor: theme.colors.primary[500],
  },
  text: {
    color: theme.colors.text.primary,
  },
  errorMessage: {
    color: theme.colors.error,
  },
});
```

### 3. Sử Dụng Spacing

```typescript
const styles = StyleSheet.create({
  container: {
    padding: theme.spacing.md,
    marginBottom: theme.spacing.lg,
  },
  header: {
    marginTop: theme.spacing.xl,
  },
});
```

### 4. Sử Dụng Typography

```typescript
const styles = StyleSheet.create({
  title: {
    fontSize: theme.typography.fontSizes.xl,
    fontWeight: theme.typography.fontWeights.bold,
    lineHeight: theme.typography.lineHeights.tight,
  },
  body: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: theme.typography.fontWeights.normal,
  },
});
```

## Components Sử Dụng Theme

Dự án đã bao gồm các components tiêu chuẩn đã được cấu hình để sử dụng theme:

### Button

```typescript
import Button from '../components/ui/Button';

// Sử dụng:
<Button
  title="Đăng nhập"
  onPress={handleLogin}
  variant="primary"  // Các variant: primary, secondary, outline, danger
  size="md"          // Các kích thước: sm, md, lg
  fullWidth          // Optional: chiếm toàn bộ chiều rộng
/>
```

### TextInput

```typescript
import TextInput from '../components/ui/TextInput';

// Sử dụng:
<TextInput
  placeholder="Email hoặc số điện thoại"
  value={email}
  onChangeText={setEmail}
  label="Email"      // Optional
  error="Email không hợp lệ" // Optional: hiển thị lỗi
  icon={emailIcon}   // Optional
  iconPosition="right" // "left" | "right"
/>
```

### Card

```typescript
import Card from '../components/ui/Card';

// Sử dụng:
<Card
  variant="primary"  // "primary" | "flat"
  style={styles.customCard}
>
  {/* Content */}
</Card>
```

### Screen

```typescript
import Screen from '../components/common/Screen';

// Sử dụng:
<Screen
  statusBarStyle="dark-content"  // Optional
  backgroundColor={theme.colors.background.primary}  // Optional
  safeAreaTop={true}  // Optional
  safeAreaBottom={false}  // Optional
>
  {/* Content */}
</Screen>
```

## Mở Rộng Theme

Để mở rộng theme, hãy cập nhật file `app/theme/index.ts`:

```typescript
// Thêm màu mới
export const colors = {
  // Các màu hiện có
  newColor: '#FF5722',
};

// Thêm kiểu button mới
export const buttons = {
  // Các kiểu hiện có
  newVariant: {
    backgroundColor: colors.newColor,
    textColor: '#FFFFFF',
    // ...các thuộc tính khác
  },
};
```

## Sửa Đổi Theme

Khi cần thay đổi theme toàn cục (ví dụ: đổi màu chủ đạo), chỉ cần cập nhật trong file theme:

```typescript
export const colors = {
  primary: {
    // Thay đổi từ màu xanh lam sang xanh lá
    500: '#10B981', // Xanh lá thay vì '#0EA5E9'
    // ...các tông màu khác
  },
  // ...các màu khác
};
```

## Quy Tắc Quan Trọng

1. **Luôn sử dụng giá trị từ theme** thay vì hardcoded values
2. **Tránh định nghĩa màu sắc, kích thước font, spacing cứng** trong styles của component
3. **Sử dụng các component UI có sẵn** khi có thể
4. **Khi cần tạo component mới**, hãy đảm bảo nó sử dụng theme

## Ví Dụ Chuyển Đổi

### Trước khi sử dụng theme:

```typescript
const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 8,
  },
  text: {
    fontSize: 16,
    color: '#111827',
    marginBottom: 8,
  },
  button: {
    backgroundColor: '#0EA5E9',
    padding: 12,
    borderRadius: 8,
  }
});
```

### Sau khi sử dụng theme:

```typescript
const styles = StyleSheet.create({
  container: {
    backgroundColor: theme.colors.background.primary,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
  },
  text: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.sm,
  },
  // Hoặc sử dụng Button component thay vì tạo style riêng
});
``` 