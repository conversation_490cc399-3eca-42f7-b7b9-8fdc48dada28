import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation/types';
import { BudgetStatistics, BudgetCategoryStatistics } from '../types/transaction';
import { TransactionAPI } from '../../src/api/transaction.api';
import theme from '../theme';
import Screen from '../components/common/Screen';
import Button from '../components/ui/Button';
import Card from '../components/ui/Card';

type BudgetStatisticsScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'BudgetStatistics'
>;

const BudgetStatisticsScreen = () => {
  const navigation = useNavigation<BudgetStatisticsScreenNavigationProp>();

  const [statistics, setStatistics] = useState<BudgetStatistics | null>(null);
  const [loading, setLoading] = useState(false);
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());

  const months = [
    'Tháng 1', 'Tháng 2', 'Tháng 3', 'Tháng 4', 'Tháng 5', 'Tháng 6',
    'Tháng 7', 'Tháng 8', 'Tháng 9', 'Tháng 10', 'Tháng 11', 'Tháng 12'
  ];

  const years = Array.from({ length: 5 }, (_, i) => new Date().getFullYear() - 2 + i);

  useEffect(() => {
    loadStatistics();
  }, [selectedMonth, selectedYear]);

  const loadStatistics = async () => {
    try {
      setLoading(true);
      const stats = await TransactionAPI.getBudgetStatistics(selectedMonth, selectedYear);
      setStatistics(stats);
    } catch (error) {
      console.error('Error loading budget statistics:', error);
      Alert.alert('Lỗi', 'Không thể tải thống kê ngân sách');
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return amount.toLocaleString('vi-VN') + ' ₫';
  };

  const getStatusColor = (percentage: number, isExceeded: boolean) => {
    if (isExceeded) return theme.colors.error;
    if (percentage >= 80) return theme.colors.warning;
    if (percentage >= 60) return theme.colors.secondary[500];
    return theme.colors.success;
  };

  const getStatusText = (percentage: number, isExceeded: boolean) => {
    if (isExceeded) return 'Vượt ngân sách';
    if (percentage >= 80) return 'Gần hết ngân sách';
    if (percentage >= 60) return 'Đang sử dụng nhiều';
    return 'Trong tầm kiểm soát';
  };

  const renderMonthYearSelector = () => (
    <Card style={styles.sectionCard}>
      <Text style={styles.sectionTitle}>Chọn tháng/năm</Text>
      
      <View style={styles.selectorContainer}>
        <View style={styles.selectorGroup}>
          <Text style={styles.selectorLabel}>Tháng</Text>
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.monthContainer}
          >
            {months.map((month, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  styles.monthButton,
                  selectedMonth === index + 1 && styles.selectedMonthButton,
                ]}
                onPress={() => setSelectedMonth(index + 1)}
              >
                <Text style={[
                  styles.monthButtonText,
                  selectedMonth === index + 1 && styles.selectedMonthButtonText,
                ]}>
                  {month}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        <View style={styles.selectorGroup}>
          <Text style={styles.selectorLabel}>Năm</Text>
          <View style={styles.yearContainer}>
            {years.map((year) => (
              <TouchableOpacity
                key={year}
                style={[
                  styles.yearButton,
                  selectedYear === year && styles.selectedYearButton,
                ]}
                onPress={() => setSelectedYear(year)}
              >
                <Text style={[
                  styles.yearButtonText,
                  selectedYear === year && styles.selectedYearButtonText,
                ]}>
                  {year}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </View>
    </Card>
  );

  const renderOverviewSection = () => {
    if (!statistics) return null;

    const overallColor = getStatusColor(statistics.overall_percentage, statistics.total_spent > statistics.total_budget);

    return (
      <Card style={styles.sectionCard}>
        <Text style={styles.sectionTitle}>Tổng quan ngân sách</Text>
        
        <View style={styles.overviewContainer}>
          <View style={styles.overviewItem}>
            <Text style={styles.overviewLabel}>Tổng ngân sách</Text>
            <Text style={styles.overviewValue}>
              {formatCurrency(statistics.total_budget)}
            </Text>
          </View>
          
          <View style={styles.overviewItem}>
            <Text style={styles.overviewLabel}>Đã chi tiêu</Text>
            <Text style={[styles.overviewValue, { color: overallColor }]}>
              {formatCurrency(statistics.total_spent)}
            </Text>
          </View>
          
          <View style={styles.overviewItem}>
            <Text style={styles.overviewLabel}>Còn lại</Text>
            <Text style={[
              styles.overviewValue,
              { color: statistics.remaining_budget >= 0 ? theme.colors.success : theme.colors.error }
            ]}>
              {formatCurrency(statistics.remaining_budget)}
            </Text>
          </View>
          
          <View style={styles.overviewItem}>
            <Text style={styles.overviewLabel}>Tỷ lệ sử dụng</Text>
            <Text style={[styles.overviewValue, { color: overallColor }]}>
              {statistics.overall_percentage.toFixed(1)}%
            </Text>
          </View>
        </View>

        {/* Overall Progress Bar */}
        <View style={styles.progressContainer}>
          <View style={styles.progressBar}>
            <View 
              style={[
                styles.progressFill,
                { 
                  width: `${Math.min(statistics.overall_percentage, 100)}%`,
                  backgroundColor: overallColor,
                }
              ]} 
            />
          </View>
          <Text style={[styles.progressText, { color: overallColor }]}>
            {getStatusText(statistics.overall_percentage, statistics.total_spent > statistics.total_budget)}
          </Text>
        </View>
      </Card>
    );
  };

  const renderCategoryItem = (category: BudgetCategoryStatistics) => {
    const statusColor = getStatusColor(category.percentage_used, category.is_exceeded);
    
    return (
      <Card key={category.budget_id} style={styles.categoryCard}>
        <View style={styles.categoryHeader}>
          <Text style={styles.categoryName}>{category.category_name}</Text>
          <View style={styles.categoryStatus}>
            <View style={[styles.statusDot, { backgroundColor: statusColor }]} />
            <Text style={[styles.statusText, { color: statusColor }]}>
              {getStatusText(category.percentage_used, category.is_exceeded)}
            </Text>
          </View>
        </View>

        <View style={styles.categoryAmounts}>
          <View style={styles.amountItem}>
            <Text style={styles.amountLabel}>Ngân sách</Text>
            <Text style={styles.amountValue}>
              {formatCurrency(category.budget_amount)}
            </Text>
          </View>
          
          <View style={styles.amountItem}>
            <Text style={styles.amountLabel}>Đã chi</Text>
            <Text style={[styles.amountValue, { color: statusColor }]}>
              {formatCurrency(category.spent_amount)}
            </Text>
          </View>
          
          <View style={styles.amountItem}>
            <Text style={styles.amountLabel}>Còn lại</Text>
            <Text style={[
              styles.amountValue,
              { color: category.remaining_amount >= 0 ? theme.colors.success : theme.colors.error }
            ]}>
              {formatCurrency(category.remaining_amount)}
            </Text>
          </View>
        </View>

        {/* Category Progress Bar */}
        <View style={styles.progressContainer}>
          <View style={styles.progressBar}>
            <View 
              style={[
                styles.progressFill,
                { 
                  width: `${Math.min(category.percentage_used, 100)}%`,
                  backgroundColor: statusColor,
                }
              ]} 
            />
          </View>
          <Text style={styles.progressPercentage}>
            {category.percentage_used.toFixed(1)}% ({category.transaction_count} giao dịch)
          </Text>
        </View>

        {/* Recent Transactions */}
        {category.transactions.length > 0 && (
          <View style={styles.transactionsSection}>
            <Text style={styles.transactionsTitle}>Giao dịch gần đây</Text>
            {category.transactions.slice(0, 3).map((transaction) => (
              <View key={transaction.id} style={styles.transactionItem}>
                <Text style={styles.transactionDescription}>
                  {transaction.description || 'Không có mô tả'}
                </Text>
                <View style={styles.transactionDetails}>
                  <Text style={styles.transactionAmount}>
                    {formatCurrency(transaction.amount)}
                  </Text>
                  <Text style={styles.transactionDate}>
                    {new Date(transaction.transaction_date).toLocaleDateString('vi-VN')}
                  </Text>
                </View>
              </View>
            ))}
            {category.transactions.length > 3 && (
              <Text style={styles.moreTransactions}>
                Và {category.transactions.length - 3} giao dịch khác...
              </Text>
            )}
          </View>
        )}
      </Card>
    );
  };

  const renderCategoriesSection = () => {
    if (!statistics || statistics.categories.length === 0) return null;

    const sortedCategories = [...statistics.categories].sort((a, b) => b.percentage_used - a.percentage_used);

    return (
      <View style={styles.categoriesContainer}>
        <Text style={styles.sectionTitle}>Chi tiết theo danh mục</Text>
        {sortedCategories.map(renderCategoryItem)}
      </View>
    );
  };

  const renderEmptyState = () => (
    <Card style={styles.sectionCard}>
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyIcon}>💰</Text>
        <Text style={styles.emptyTitle}>Chưa có ngân sách</Text>
        <Text style={styles.emptySubtitle}>
          Bạn chưa thiết lập ngân sách cho tháng {selectedMonth}/{selectedYear}
        </Text>
        <Button
          title="Tạo ngân sách"
          onPress={() => navigation.navigate('CreateBudget')}
          style={styles.emptyButton}
        />
      </View>
    </Card>
  );

  return (
    <Screen>
      <ScrollView 
        style={styles.container}
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
      >
        <Text style={styles.headerTitle}>Thống kê ngân sách</Text>
        
        {renderMonthYearSelector()}
        
        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.colors.primary[500]} />
            <Text style={styles.loadingText}>Đang tải thống kê...</Text>
          </View>
        ) : statistics && statistics.total_budget > 0 ? (
          <>
            {renderOverviewSection()}
            {renderCategoriesSection()}
          </>
        ) : (
          renderEmptyState()
        )}
      </ScrollView>
    </Screen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background.secondary,
  },
  scrollContainer: {
    padding: theme.spacing.md,
  },
  headerTitle: {
    fontSize: theme.typography.fontSizes['2xl'],
    fontWeight: theme.typography.fontWeights.bold,
    color: theme.colors.text.primary,
    textAlign: 'center',
    marginBottom: theme.spacing.xl,
  },
  sectionCard: {
    marginBottom: theme.spacing.md,
    padding: theme.spacing.md,
  },
  sectionTitle: {
    fontSize: theme.typography.fontSizes.lg,
    fontWeight: theme.typography.fontWeights.semibold,
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.md,
  },
  
  // Month/Year Selector
  selectorContainer: {
    gap: theme.spacing.md,
  },
  selectorGroup: {
    gap: theme.spacing.sm,
  },
  selectorLabel: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '500',
    color: theme.colors.text.primary,
  },
  monthContainer: {
    gap: theme.spacing.sm,
    paddingHorizontal: theme.spacing.xs,
  },
  monthButton: {
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.md,
    backgroundColor: theme.colors.gray[100],
    borderRadius: theme.borderRadius.md,
  },
  selectedMonthButton: {
    backgroundColor: theme.colors.primary[500],
  },
  monthButtonText: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.primary,
  },
  selectedMonthButtonText: {
    color: theme.colors.text.inverse,
    fontWeight: '500',
  },
  yearContainer: {
    flexDirection: 'row',
    gap: theme.spacing.sm,
    flexWrap: 'wrap',
  },
  yearButton: {
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.lg,
    backgroundColor: theme.colors.gray[100],
    borderRadius: theme.borderRadius.md,
  },
  selectedYearButton: {
    backgroundColor: theme.colors.primary[500],
  },
  yearButtonText: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.primary,
  },
  selectedYearButtonText: {
    color: theme.colors.text.inverse,
    fontWeight: '500',
  },
  
  // Overview Section
  overviewContainer: {
    gap: theme.spacing.md,
  },
  overviewItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: theme.spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray[100],
  },
  overviewLabel: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.secondary,
  },
  overviewValue: {
    fontSize: theme.typography.fontSizes.lg,
    fontWeight: theme.typography.fontWeights.semibold,
    color: theme.colors.text.primary,
  },
  
  // Progress Bar
  progressContainer: {
    marginTop: theme.spacing.md,
    gap: theme.spacing.sm,
  },
  progressBar: {
    height: 8,
    backgroundColor: theme.colors.gray[200],
    borderRadius: theme.borderRadius.sm,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: theme.borderRadius.sm,
  },
  progressText: {
    fontSize: theme.typography.fontSizes.sm,
    fontWeight: '500',
    textAlign: 'center',
  },
  progressPercentage: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
    textAlign: 'center',
  },
  
  // Categories Section
  categoriesContainer: {
    gap: theme.spacing.sm,
  },
  categoryCard: {
    marginBottom: theme.spacing.sm,
    padding: theme.spacing.md,
  },
  categoryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  categoryName: {
    fontSize: theme.typography.fontSizes.lg,
    fontWeight: theme.typography.fontWeights.semibold,
    color: theme.colors.text.primary,
    flex: 1,
  },
  categoryStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.xs,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  statusText: {
    fontSize: theme.typography.fontSizes.sm,
    fontWeight: '500',
  },
  categoryAmounts: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: theme.spacing.md,
  },
  amountItem: {
    alignItems: 'center',
    flex: 1,
  },
  amountLabel: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
    marginBottom: theme.spacing.xs,
  },
  amountValue: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: theme.typography.fontWeights.semibold,
    color: theme.colors.text.primary,
    textAlign: 'center',
  },
  
  // Transactions Section
  transactionsSection: {
    marginTop: theme.spacing.md,
    paddingTop: theme.spacing.md,
    borderTopWidth: 1,
    borderTopColor: theme.colors.gray[100],
  },
  transactionsTitle: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '500',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.sm,
  },
  transactionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: theme.spacing.sm,
  },
  transactionDescription: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.primary,
    flex: 1,
    marginRight: theme.spacing.sm,
  },
  transactionDetails: {
    alignItems: 'flex-end',
  },
  transactionAmount: {
    fontSize: theme.typography.fontSizes.sm,
    fontWeight: '500',
    color: theme.colors.text.primary,
  },
  transactionDate: {
    fontSize: theme.typography.fontSizes.xs,
    color: theme.colors.text.secondary,
    marginTop: 2,
  },
  moreTransactions: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
    fontStyle: 'italic',
    textAlign: 'center',
    marginTop: theme.spacing.sm,
  },
  
  // Loading State
  loadingContainer: {
    alignItems: 'center',
    paddingVertical: theme.spacing.xl,
  },
  loadingText: {
    marginTop: theme.spacing.sm,
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.secondary,
  },
  
  // Empty State
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: theme.spacing.xl,
  },
  emptyIcon: {
    fontSize: 64,
    marginBottom: theme.spacing.lg,
  },
  emptyTitle: {
    fontSize: theme.typography.fontSizes.xl,
    fontWeight: theme.typography.fontWeights.semibold,
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.sm,
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.secondary,
    textAlign: 'center',
    marginBottom: theme.spacing.xl,
    lineHeight: 24,
  },
  emptyButton: {
    paddingHorizontal: theme.spacing.xl,
  },
});

export default BudgetStatisticsScreen; 