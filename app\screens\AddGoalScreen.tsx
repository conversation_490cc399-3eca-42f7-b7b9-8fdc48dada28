import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  Alert,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation/types';
import theme from '../theme';
import Screen from '../components/common/Screen';
import Button from '../components/ui/Button';
import TextInput from '../components/ui/TextInput';
import Card from '../components/ui/Card';
import { GoalsAPI, IGoal, CreateGoalDto, UpdateGoalDto } from '../../src/api/goals.api';
import DateTimePicker from '@react-native-community/datetimepicker';
import STRINGS from '../constants/strings';

const { width } = Dimensions.get('window');
const ICON_SIZE = 50;

type AddGoalScreenRouteProp = RouteProp<RootStackParamList, 'AddGoal'>;
type AddGoalScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;

interface AddGoalScreenParams {
  goalId?: string;
}

interface GoalType {
  id: string;
  name: string;
  icon: string;
  color: string;
  description: string;
}

const goalTypes: GoalType[] = [
  {
    id: '1',
    name: STRINGS.GOALS.BUY_HOUSE,
    icon: '🏠',
    color: theme.colors.primary[100],
    description: STRINGS.GOALS.BUY_HOUSE_DESC
  },
  {
    id: '2',
    name: STRINGS.GOALS.BUY_CAR,
    icon: '🚗',
    color: theme.colors.warning[100],
    description: STRINGS.GOALS.BUY_CAR_DESC
  },
  {
    id: '3',
    name: STRINGS.GOALS.TRAVEL,
    icon: '✈️',
    color: theme.colors.success[100],
    description: STRINGS.GOALS.TRAVEL_DESC
  },
  {
    id: '4',
    name: STRINGS.GOALS.EDUCATION,
    icon: '🎓',
    color: theme.colors.secondary[100],
    description: STRINGS.GOALS.EDUCATION_DESC
  },
  {
    id: '5',
    name: STRINGS.GOALS.EMERGENCY_FUND,
    icon: '🚨',
    color: theme.colors.error[100],
    description: STRINGS.GOALS.EMERGENCY_FUND_DESC
  },
  {
    id: '6',
    name: STRINGS.GOALS.INVESTMENT,
    icon: '📈',
    color: theme.colors.primary[200],
    description: STRINGS.GOALS.INVESTMENT_DESC
  },
  {
    id: '7',
    name: STRINGS.GOALS.OTHER,
    icon: '✨',
    color: theme.colors.gray[200],
    description: STRINGS.GOALS.OTHER_DESC
  },
];

const AddGoalScreen = () => {
  const navigation = useNavigation<AddGoalScreenNavigationProp>();
  const route = useRoute<RouteProp<RootStackParamList, 'AddGoal'>>();
  const params = (route.params || {}) as AddGoalScreenParams;
  const isEditing = params?.goalId !== undefined;

  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(isEditing);
  const [selectedGoalType, setSelectedGoalType] = useState<GoalType | null>(null);
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [targetAmount, setTargetAmount] = useState('');
  const [currentAmount, setCurrentAmount] = useState('');
  const [startDate, setStartDate] = useState(new Date());
  const [targetDate, setTargetDate] = useState(new Date());
  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showTargetDatePicker, setShowTargetDatePicker] = useState(false);

  useEffect(() => {
    if (isEditing && params.goalId) {
      loadGoalDetails();
    }
  }, []);

  const loadGoalDetails = async () => {
    if (!params.goalId) return;

    try {
      const goalData = await GoalsAPI.getGoalById(params.goalId);
      const goalType = goalTypes.find(type => type.name === goalData.name) || goalTypes[6];

      setSelectedGoalType(goalType);
      setName(goalData.name);
      setDescription(goalData.description || '');
      setTargetAmount(goalData.target_amount.toString());
      setCurrentAmount(goalData.current_amount.toString());
      setStartDate(new Date(goalData.start_date));
      setTargetDate(new Date(goalData.target_date));
    } catch (error) {
      Alert.alert(STRINGS.COMMON.ERROR, STRINGS.GOALS.FAILED_TO_LOAD);
    } finally {
      setInitialLoading(false);
    }
  };

  const handleCreate = async () => {
    if (!selectedGoalType || !name || !targetAmount || !currentAmount) {
      Alert.alert(STRINGS.COMMON.ERROR, STRINGS.GOALS.FILL_ALL_FIELDS);
      return;
    }

    if (startDate > targetDate) {
      Alert.alert(STRINGS.COMMON.ERROR, 'Start date must be before target date');
      return;
    }

    try {
      setLoading(true);
      const createData: CreateGoalDto = {
        name,
        target_amount: parseFloat(targetAmount.replace(/,/g, '')),
        current_amount: parseFloat(currentAmount.replace(/,/g, '')),
        start_date: startDate.toISOString(),
        target_date: targetDate.toISOString(),
      };
      await GoalsAPI.createGoal(createData);
      Alert.alert(STRINGS.COMMON.SUCCESS, STRINGS.GOALS.GOAL_CREATED_SUCCESS);
      navigation.navigate('Main', {
        screen: 'Goals',
        params: { refresh: true }
      });
    } catch (error) {
      Alert.alert(STRINGS.COMMON.ERROR, STRINGS.GOALS.FAILED_TO_CREATE);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (text: string) => {
    const number = text.replace(/[^0-9]/g, '');
    return number.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  };

  if (initialLoading) {
    return (
      <Screen>
        <View style={[styles.container, styles.centerContent]}>
          <ActivityIndicator size="large" color={theme.colors.primary[500]} />
        </View>
      </Screen>
    );
  }

  return (
    <Screen>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.container}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
        >
          <Text style={styles.sectionTitle}>{STRINGS.GOALS.GOAL_TYPE}</Text>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.goalTypesContainer}
          >
            {goalTypes.map((type) => (
              <TouchableOpacity
                key={type.id}
                style={[
                  styles.goalTypeCard,
                  { backgroundColor: type.color },
                  selectedGoalType?.id === type.id && styles.selectedGoalType,
                ]}
                onPress={() => {
                  setSelectedGoalType(type);
                  if (!isEditing) {
                    setName(type.name);
                  }
                }}
              >
                <Text style={styles.goalTypeIcon}>{type.icon}</Text>
                <Text style={styles.goalTypeName}>{type.name}</Text>
                <Text style={styles.goalTypeDescription}>{type.description}</Text>
              </TouchableOpacity>
            ))}
          </ScrollView>

          <Card style={styles.formCard}>
            <Text style={styles.label}>{STRINGS.GOALS.GOAL_NAME}</Text>
            <TextInput
              value={name}
              onChangeText={setName}
              placeholder={STRINGS.GOALS.ENTER_GOAL_NAME}
              style={styles.input}
            />

            <Text style={styles.label}>{STRINGS.GOALS.DESCRIPTION} {STRINGS.GOALS.OPTIONAL}</Text>
            <TextInput
              value={description}
              onChangeText={setDescription}
              placeholder={STRINGS.GOALS.ENTER_DESCRIPTION}
              multiline
              numberOfLines={3}
              style={[styles.input, styles.textArea]}
            />

            <Text style={styles.label}>{STRINGS.GOALS.TARGET_AMOUNT}</Text>
            <TextInput
              value={targetAmount}
              onChangeText={(text) => setTargetAmount(formatCurrency(text))}
              placeholder={STRINGS.GOALS.ENTER_TARGET_AMOUNT}
              keyboardType="numeric"
              style={styles.input}
            />

            <Text style={styles.label}>{STRINGS.GOALS.CURRENT_AMOUNT}</Text>
            <TextInput
              value={currentAmount}
              onChangeText={(text) => setCurrentAmount(formatCurrency(text))}
              placeholder={STRINGS.GOALS.ENTER_CURRENT_AMOUNT}
              keyboardType="numeric"
              style={styles.input}
            />

            <Text style={styles.label}>{STRINGS.GOALS.START_DATE}</Text>
            <Button
              title={startDate.toLocaleDateString('en-US')}
              onPress={() => setShowStartDatePicker(true)}
              variant="secondary"
              style={styles.dateButton}
            />

            <Text style={styles.label}>{STRINGS.GOALS.TARGET_DATE}</Text>
            <Button
              title={targetDate.toLocaleDateString('en-US')}
              onPress={() => setShowTargetDatePicker(true)}
              variant="secondary"
              style={styles.dateButton}
            />

            {showStartDatePicker && (
              <DateTimePicker
                value={startDate}
                mode="date"
                display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                onChange={(event, selectedDate) => {
                  if (Platform.OS === 'android') {
                    setShowStartDatePicker(false);
                  }
                  if (event.type === 'set' && selectedDate) {
                    setStartDate(selectedDate);
                    if (Platform.OS === 'ios') {
                      setShowStartDatePicker(false);
                    }
                  } else if (event.type === 'dismissed') {
                    setShowStartDatePicker(false);
                  }
                }}
                minimumDate={new Date()}
              />
            )}

            {showTargetDatePicker && (
              <DateTimePicker
                value={targetDate}
                mode="date"
                display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                onChange={(event, selectedDate) => {
                  if (Platform.OS === 'android') {
                    setShowTargetDatePicker(false);
                  }
                  if (event.type === 'set' && selectedDate) {
                    setTargetDate(selectedDate);
                    if (Platform.OS === 'ios') {
                      setShowTargetDatePicker(false);
                    }
                  } else if (event.type === 'dismissed') {
                    setShowTargetDatePicker(false);
                  }
                }}
                minimumDate={startDate}
              />
            )}
          </Card>
        </ScrollView>

        <View style={styles.buttonContainer}>
          <Button
            title={isEditing ? STRINGS.COMMON.UPDATE : STRINGS.GOALS.ADD_NEW_GOAL}
            onPress={handleCreate}
            variant="primary"
            loading={loading}
            style={styles.button}
          />
        </View>
      </KeyboardAvoidingView>
    </Screen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollContainer: {
    flexGrow: 1,
    padding: theme.spacing.md,
  },
  sectionTitle: {
    fontSize: theme.typography.fontSizes.lg,
    fontWeight: '600',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.md,
  },
  goalTypesContainer: {
    paddingBottom: theme.spacing.md,
  },
  goalTypeCard: {
    width: width * 0.7,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
    marginRight: theme.spacing.md,
    alignItems: 'center',
  },
  selectedGoalType: {
    borderWidth: 2,
    borderColor: theme.colors.primary[500],
  },
  goalTypeIcon: {
    fontSize: 32,
    marginBottom: theme.spacing.sm,
  },
  goalTypeName: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '600',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.xs,
  },
  goalTypeDescription: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
    textAlign: 'center',
  },
  formCard: {
    padding: theme.spacing.md,
    marginTop: theme.spacing.md,
  },
  label: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '500',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.sm,
  },
  input: {
    backgroundColor: theme.colors.gray[100],
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.md,
    fontSize: theme.typography.fontSizes.md,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  dateButton: {
    marginBottom: theme.spacing.md,
  },
  buttonContainer: {
    padding: theme.spacing.md,
    backgroundColor: theme.colors.background.primary,
    borderTopWidth: 1,
    borderTopColor: theme.colors.gray[200],
  },
  button: {
    marginTop: theme.spacing.sm,
  },
});

export default AddGoalScreen;