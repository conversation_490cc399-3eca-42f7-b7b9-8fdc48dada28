import React from 'react';
import { View, Text, Image, ScrollView, StyleSheet, TouchableOpacity } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import theme from '../theme';
import Screen from '../components/common/Screen';

const ProfileScreen = () => {
  // Mock user data
  const user = {
    name: '<PERSON>',
    role: 'Product Designer',
    email: '<EMAIL>',
    avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
    bio: 'Product designer based in San Francisco. I love creating beautiful user interfaces and experiences that solve real problems.',
    followers: 856,
    following: 247,
    posts: 36,
  };

  return (
    <Screen>
      <StatusBar style="dark" />
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header with back button */}
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Profile</Text>
          <TouchableOpacity style={styles.headerAction}>
            <Text style={styles.headerActionText}>Edit</Text>
          </TouchableOpacity>
        </View>

        {/* Profile Info */}
        <View style={styles.profileSection}>
          <Image source={{ uri: user.avatar }} style={styles.avatar} />
          <Text style={styles.userName}>{user.name}</Text>
          <Text style={styles.userRole}>{user.role}</Text>
          
          <View style={styles.statsContainer}>
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{user.posts}</Text>
              <Text style={styles.statLabel}>Posts</Text>
            </View>
            <View style={styles.statDivider} />
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{user.followers}</Text>
              <Text style={styles.statLabel}>Followers</Text>
            </View>
            <View style={styles.statDivider} />
            <View style={styles.statItem}>
              <Text style={styles.statValue}>{user.following}</Text>
              <Text style={styles.statLabel}>Following</Text>
            </View>
          </View>
        </View>

        {/* Bio Section */}
        <View style={styles.bioSection}>
          <Text style={styles.sectionTitle}>About</Text>
          <Text style={styles.bioText}>{user.bio}</Text>
        </View>

        {/* Action Buttons */}
        <View style={styles.buttonGroup}>
          <TouchableOpacity style={styles.primaryButton}>
            <Text style={styles.primaryButtonText}>Follow</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.secondaryButton}>
            <Text style={styles.secondaryButtonText}>Message</Text>
          </TouchableOpacity>
        </View>

        {/* Recent Activity */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Recent Activity</Text>
            <TouchableOpacity>
              <Text style={styles.sectionAction}>See All</Text>
            </TouchableOpacity>
          </View>

          {/* Activity Cards */}
          {[1, 2, 3].map((item) => (
            <View key={item} style={styles.activityCard}>
              <View style={styles.activityHeader}>
                <Image 
                  source={{ uri: `https://picsum.photos/200/${300 + item}` }} 
                  style={styles.activityImage} 
                />
                <View>
                  <Text style={styles.activityTitle}>New Project {item}</Text>
                  <Text style={styles.activityTime}>2 days ago</Text>
                </View>
              </View>
              <Text style={styles.activityDescription}>
                Created a new design for the mobile application dashboard view.
              </Text>
              <View style={styles.activityFooter}>
                <TouchableOpacity style={styles.activityAction}>
                  <Text style={styles.activityActionText}>❤️ Like</Text>
                </TouchableOpacity>
                <TouchableOpacity style={styles.activityAction}>
                  <Text style={styles.activityActionText}>💬 Comment</Text>
                </TouchableOpacity>
              </View>
            </View>
          ))}
        </View>

        {/* Photos Section */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Photos</Text>
            <TouchableOpacity>
              <Text style={styles.sectionAction}>See All</Text>
            </TouchableOpacity>
          </View>
          
          <View style={styles.photosGrid}>
            {[1, 2, 3, 4, 5, 6].map((item) => (
              <Image 
                key={item}
                source={{ uri: `https://picsum.photos/200/${300 + item}` }}
                style={styles.photoThumbnail}
              />
            ))}
          </View>
        </View>

        {/* Bottom Space */}
        <View style={{ height: theme.spacing.xl }} />
      </ScrollView>
    </Screen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background.primary,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.xl,
    paddingVertical: theme.spacing.md,
  },
  headerTitle: {
    fontSize: theme.typography.fontSizes.lg,
    fontWeight: '600',
    color: theme.colors.text.primary,
  },
  headerAction: {
    padding: theme.spacing.sm,
  },
  headerActionText: {
    color: theme.colors.primary[500],
    fontWeight: '500',
  },
  profileSection: {
    alignItems: 'center',
    paddingVertical: theme.spacing.xl,
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
    marginBottom: theme.spacing.md,
  },
  userName: {
    fontSize: theme.typography.fontSizes['2xl'],
    fontWeight: '700',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.xs,
  },
  userRole: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.secondary,
    marginBottom: theme.spacing.xl,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    width: '80%',
    borderRadius: theme.borderRadius.md,
    backgroundColor: theme.colors.gray[100],
    paddingVertical: theme.spacing.md,
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statValue: {
    fontSize: theme.typography.fontSizes.lg,
    fontWeight: '700',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.xs,
  },
  statLabel: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
  },
  statDivider: {
    width: 1,
    height: 24,
    backgroundColor: theme.colors.gray[200],
    marginTop: theme.spacing.sm,
  },
  bioSection: {
    paddingHorizontal: theme.spacing.xl,
    paddingVertical: theme.spacing.xl,
  },
  bioText: {
    fontSize: theme.typography.fontSizes.md,
    lineHeight: 22,
    color: theme.colors.text.secondary,
  },
  buttonGroup: {
    flexDirection: 'row',
    paddingHorizontal: theme.spacing.xl,
    paddingBottom: theme.spacing.xl,
  },
  primaryButton: {
    backgroundColor: theme.colors.primary[500],
    borderRadius: theme.borderRadius.md,
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.xl,
    flex: 1,
    marginRight: theme.spacing.sm,
    alignItems: 'center',
  },
  primaryButtonText: {
    color: theme.colors.background.primary,
    fontWeight: '600',
    fontSize: theme.typography.fontSizes.md,
  },
  secondaryButton: {
    borderWidth: 1,
    borderColor: theme.colors.primary[500],
    borderRadius: theme.borderRadius.md,
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.xl,
    flex: 1,
    marginLeft: theme.spacing.sm,
    alignItems: 'center',
  },
  secondaryButtonText: {
    color: theme.colors.primary[500],
    fontWeight: '600',
    fontSize: theme.typography.fontSizes.md,
  },
  section: {
    paddingHorizontal: theme.spacing.xl,
    paddingTop: theme.spacing.md,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  sectionTitle: {
    fontSize: theme.typography.fontSizes.lg,
    fontWeight: '600',
    color: theme.colors.text.primary,
  },
  sectionAction: {
    color: theme.colors.primary[500],
    fontSize: theme.typography.fontSizes.sm,
  },
  activityCard: {
    backgroundColor: theme.colors.gray[100],
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.md,
  },
  activityHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  activityImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: theme.spacing.md,
  },
  activityTitle: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '600',
    color: theme.colors.text.primary,
  },
  activityTime: {
    fontSize: theme.typography.fontSizes.xs,
    color: theme.colors.text.secondary,
    marginTop: theme.spacing.xs,
  },
  activityDescription: {
    fontSize: theme.typography.fontSizes.md,
    lineHeight: 22,
    color: theme.colors.text.secondary,
    marginBottom: theme.spacing.md,
  },
  activityFooter: {
    flexDirection: 'row',
    marginTop: theme.spacing.sm,
  },
  activityAction: {
    marginRight: theme.spacing.md,
  },
  activityActionText: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
  },
  photosGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -4,
  },
  photoThumbnail: {
    width: '33%',
    aspectRatio: 1,
    margin: 1,
    borderRadius: theme.borderRadius.xs,
  },
});

export default ProfileScreen; 