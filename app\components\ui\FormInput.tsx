import React from 'react';
import { Controller, Control, FieldValues, FieldPath, RegisterOptions } from 'react-hook-form';
import TextInput from './TextInput';

interface FormInputProps<T extends FieldValues> {
  name: FieldPath<T>;
  control: Control<T>;
  rules?: RegisterOptions;
  label?: string;
  placeholder?: string;
  secureTextEntry?: boolean;
  disabled?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  onRightIconPress?: () => void;
  autoCapitalize?: 'none' | 'sentences' | 'words' | 'characters';
  keyboardType?: 'default' | 'number-pad' | 'decimal-pad' | 'numeric' | 'email-address' | 'phone-pad';
  multiline?: boolean;
  numberOfLines?: number;
  returnKeyType?: 'done' | 'go' | 'next' | 'search' | 'send';
  onSubmitEditing?: () => void;
  className?: string;
}

function FormInput<T extends FieldValues>({
  name,
  control,
  rules,
  label,
  placeholder,
  secureTextEntry,
  disabled,
  leftIcon,
  rightIcon,
  onRightIconPress,
  autoCapitalize,
  keyboardType,
  multiline,
  numberOfLines,
  returnKeyType,
  onSubmitEditing,
  className,
}: FormInputProps<T>) {
  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={({ field: { onChange, onBlur, value }, fieldState: { error } }) => (
        <TextInput
          value={value}
          onChangeText={onChange}
          label={label}
          placeholder={placeholder}
          secureTextEntry={secureTextEntry}
          disabled={disabled}
          leftIcon={leftIcon}
          rightIcon={rightIcon}
          onRightIconPress={onRightIconPress}
          autoCapitalize={autoCapitalize}
          keyboardType={keyboardType}
          multiline={multiline}
          numberOfLines={numberOfLines}
          returnKeyType={returnKeyType}
          onSubmitEditing={onSubmitEditing}
          error={error?.message}
          className={className}
        />
      )}
    />
  );
}

export default FormInput; 