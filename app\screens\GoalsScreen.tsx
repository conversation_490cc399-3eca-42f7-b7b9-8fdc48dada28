import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Dimensions,
  ActivityIndicator,
  RefreshControl,
  Alert,
} from 'react-native';
import { useNavigation, useRoute, useFocusEffect } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { StatusBar } from 'expo-status-bar';
import { RootStackParamList } from '../navigation/types';
import theme from '../theme';
import Screen from '../components/common/Screen';
import DrawerHeader from '../components/common/DrawerHeader';
import Button from '../components/ui/Button';
import { GoalsAPI, IGoal } from '../../src/api/goals.api';
import GoalCard from '../components/goals/GoalCard';
import EmptyState from '../components/common/EmptyState';
import STRINGS from '../constants/strings';

const { width } = Dimensions.get('window');

type GoalsScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;

const GoalsScreen = () => {
  const navigation = useNavigation<GoalsScreenNavigationProp>();
  const route = useRoute();
  const [goals, setGoals] = useState<IGoal[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [totalGoals, setTotalGoals] = useState(0);

  const loadGoals = async (pageNum = 1, shouldRefresh = false) => {
    try {
      if (shouldRefresh) {
        setRefreshing(true);
      }
      const response = await GoalsAPI.getGoals({ page: pageNum, limit: 10 });
      const newGoals = response.data;
      setTotalGoals(response.total);

      if (shouldRefresh || pageNum === 1) {
        setGoals(newGoals);
      } else {
        setGoals(prev => [...prev, ...newGoals]);
      }

      setHasMore(newGoals.length === 10);
      setPage(pageNum);
    } catch (error: any) {
      // Determine error message based on error type
      let errorMessage = 'Failed to load goals. Please try again.';

      if (error.code === 'NETWORK_ERROR' || error.message?.includes('timeout')) {
        errorMessage = 'Network connection failed. Please check your internet connection and try again.';
      } else if (error.response?.status === 401) {
        errorMessage = 'Authentication failed. Please login again.';
      } else if (error.response?.status === 403) {
        errorMessage = 'Access denied. You do not have permission to view goals.';
      } else if (error.response?.status >= 500) {
        errorMessage = 'Server error. Please try again later.';
      } else if (error.message) {
        errorMessage = error.message;
      }

      setError(errorMessage);

      // Show alert for critical errors
      if (error.response?.status === 401) {
        Alert.alert(
          'Authentication Required',
          'Your session has expired. Please login again.',
          [
            {
              text: 'OK',
              onPress: () => {
                // Navigate to login screen
                // You might want to implement navigation to auth stack here
              }
            }
          ]
        );
      } else if (!shouldRefresh) {
        // Only show alert if it's not a refresh action
        Alert.alert(
          'Error Loading Goals',
          errorMessage,
          [
            {
              text: 'Retry',
              onPress: () => loadGoals(pageNum, shouldRefresh)
            },
            {
              text: 'Cancel',
              style: 'cancel'
            }
          ]
        );
      }
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useFocusEffect(
    React.useCallback(() => {
      if (route.params?.refresh) {
        loadGoals(1, true);
      }
    }, [route.params?.refresh])
  );

  useEffect(() => {
    loadGoals();
  }, []);

  const handleRefresh = () => {
    loadGoals(1, true);
  };

  const handleLoadMore = () => {
    if (!loading && hasMore) {
      loadGoals(page + 1);
    }
  };

  const calculateProgress = (current: number, target: number) => {
    return Math.min((current / target) * 100, 100);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(amount);
  };

  const getGoalIcon = (name: string) => {
    const iconMap: { [key: string]: string } = {
      [STRINGS.GOALS.BUY_HOUSE]: '🏠',
      [STRINGS.GOALS.BUY_CAR]: '🚗',
      [STRINGS.GOALS.TRAVEL]: '✈️',
      [STRINGS.GOALS.EDUCATION]: '🎓',
      [STRINGS.GOALS.WEDDING]: '💍',
      [STRINGS.GOALS.SAVINGS]: '💰',
      [STRINGS.GOALS.EMERGENCY_FUND]: '🚨',
      // Keep Vietnamese for backward compatibility
      'Mua nhà': '🏠',
      'Mua xe': '🚗',
      'Du lịch': '✈️',
      'Học tập': '🎓',
      'Đám cưới': '💍',
      'Tiết kiệm': '💰',
      'Quỹ khẩn cấp': '🚨',
    };
    return iconMap[name] || '✨';
  };

  const renderGoalCard = ({ item }: { item: IGoal }) => (
    <GoalCard
      goal={item}
      onPress={() => navigation.navigate('GoalDetail', { id: item.id })}
    />
  );

  const renderFooter = () => {
    if (!loading || !hasMore) return null;
    return (
      <View style={styles.footerLoader}>
        <ActivityIndicator size="small" color={theme.colors.primary[500]} />
      </View>
    );
  };

  if (loading && !refreshing && goals.length === 0) {
    return (
      <Screen>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary[500]} />
        </View>
      </Screen>
    );
  }

  return (
    <Screen>
      <DrawerHeader title="Goals" />
      <StatusBar style="dark" />
      <View style={styles.header}>
        <Text style={styles.title}>{STRINGS.GOALS.YOUR_GOALS}</Text>
        <Text style={styles.subtitle}>{STRINGS.GOALS.TRACK_MANAGE_GOALS}</Text>
      </View>

      {error ? (
        <View style={[styles.container, styles.centerContent]}>
          <Text style={styles.errorText}>{error}</Text>
          <Button
            title={STRINGS.COMMON.RETRY}
            onPress={() => loadGoals()}
            variant="secondary"
            style={styles.retryButton}
          />
        </View>
      ) : (
        <FlatList
          data={goals}
          renderItem={renderGoalCard}
          keyExtractor={item => item.id}
          contentContainerStyle={styles.listContainer}
          ItemSeparatorComponent={() => <View style={styles.separator} />}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
          }
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.5}
          ListFooterComponent={renderFooter}
          ListEmptyComponent={
            <EmptyState
              title={STRINGS.GOALS.NO_GOALS_YET}
              description={STRINGS.GOALS.NO_GOALS_DESC}
              icon="🎯"
            />
          }
        />
      )}

      <View style={styles.buttonContainer}>
        <Button
          title={STRINGS.GOALS.ADD_NEW_GOAL}
          onPress={() => navigation.navigate('AddGoal')}
          variant="primary"
        />
      </View>
    </Screen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    paddingHorizontal: theme.spacing.md,
    paddingTop: theme.spacing.md,
    paddingBottom: theme.spacing.md,
  },
  title: {
    fontSize: theme.typography.fontSizes['2xl'],
    fontWeight: '700',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.xs,
  },
  subtitle: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.secondary,
  },
  listContainer: {
    flexGrow: 1,
    padding: theme.spacing.md,
  },
  separator: {
    height: theme.spacing.md,
  },
  buttonContainer: {
    padding: theme.spacing.md,
    backgroundColor: theme.colors.background.primary,
    borderTopWidth: 1,
    borderTopColor: theme.colors.gray[200],
  },
  footerLoader: {
    paddingVertical: theme.spacing.md,
    alignItems: 'center',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.error[500],
    textAlign: 'center',
    marginBottom: theme.spacing.md,
  },
  retryButton: {
    width: 120,
  },
});

export default GoalsScreen;