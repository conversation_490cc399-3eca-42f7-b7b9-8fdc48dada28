import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ScrollView,
  FlatList,
  Modal,
  ActivityIndicator,
} from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useNavigation, useRoute } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import DateTimePicker from '@react-native-community/datetimepicker';
import { RootStackParamList } from '../navigation/types';
import { TransactionType, InputMethod, UpdateTransactionData, Transaction } from '../types/transaction';
import { TransactionAPI } from '../../src/api/transaction.api';
import theme from '../theme';
import Screen from '../components/common/Screen';
import Button from '../components/ui/Button';
import TextInput from '../components/ui/TextInput';
import Card from '../components/ui/Card';
import DatePicker from '../components/ui/DatePicker';
import ImagePicker from '../components/ui/ImagePicker';

type EditTransactionScreenRouteProp = {
  key: string;
  name: 'EditTransaction';
  params: { id: string };
};
type EditTransactionScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'EditTransaction'
>;

interface Category {
  id: string;
  name: string;
  icon?: string;
  type: string;
}

interface CategorySelectorProps {
  visible: boolean;
  onClose: () => void;
  onSelect: (category: Category) => void;
  transactionType: TransactionType;
}

const CategorySelector: React.FC<CategorySelectorProps> = ({
  visible,
  onClose,
  onSelect,
  transactionType,
}) => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (visible) {
      loadCategories();
    }
  }, [visible, transactionType]);

  const loadCategories = async () => {
    try {
      setLoading(true);
      // Mock data for categories
      const mockCategories = transactionType === TransactionType.EXPENSE ? [
        { id: '1', name: 'Food & Dining', icon: '🍽️', type: 'expense' },
        { id: '2', name: 'Transportation', icon: '🚗', type: 'expense' },
        { id: '3', name: 'Shopping', icon: '🛒', type: 'expense' },
        { id: '4', name: 'Entertainment', icon: '🎬', type: 'expense' },
        { id: '5', name: 'Healthcare', icon: '🏥', type: 'expense' },
        { id: '6', name: 'Education', icon: '📚', type: 'expense' },
        { id: '7', name: 'Utilities', icon: '⚡', type: 'expense' },
        { id: '8', name: 'Clothing', icon: '👕', type: 'expense' },
      ] : [
        { id: '10', name: 'Salary', icon: '💰', type: 'income' },
        { id: '11', name: 'Bonus', icon: '🎁', type: 'income' },
        { id: '12', name: 'Investment', icon: '📈', type: 'income' },
        { id: '13', name: 'Sales', icon: '🛍️', type: 'income' },
        { id: '14', name: 'Freelance', icon: '💻', type: 'income' },
        { id: '15', name: 'Rental', icon: '🏠', type: 'income' },
      ];
      setCategories(mockCategories);
    } catch (error) {
      console.error('Error loading categories:', error);
    } finally {
      setLoading(false);
    }
  };

  const renderCategoryItem = ({ item }: { item: Category }) => (
    <TouchableOpacity
      style={styles.categoryItem}
      onPress={() => {
        onSelect(item);
        onClose();
      }}
    >
      <Text style={styles.categoryItemIcon}>{item.icon}</Text>
      <Text style={styles.categoryItemName}>{item.name}</Text>
    </TouchableOpacity>
  );

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <View style={styles.modalContainer}>
        <View style={styles.modalHeader}>
          <TouchableOpacity onPress={onClose}>
            <Text style={styles.modalCancelText}>Cancel</Text>
          </TouchableOpacity>
          <Text style={styles.modalTitle}>Select Category</Text>
          <View style={{ width: 50 }} />
        </View>

        <FlatList
          data={categories}
          renderItem={renderCategoryItem}
          keyExtractor={(item) => item.id}
          numColumns={2}
          contentContainerStyle={styles.categoriesGrid}
          showsVerticalScrollIndicator={false}
        />
      </View>
    </Modal>
  );
};

const EditTransactionScreen = () => {
  const navigation = useNavigation<EditTransactionScreenNavigationProp>();
  const route = useRoute<EditTransactionScreenRouteProp>();
  const { id } = route.params;

  const [transaction, setTransaction] = useState<Transaction | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  const [formData, setFormData] = useState<UpdateTransactionData>({});
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);
  const [amountText, setAmountText] = useState('');
  const [description, setDescription] = useState('');
  const [notes, setNotes] = useState('');
  const [imageUrl, setImageUrl] = useState<string>();
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showCategorySelector, setShowCategorySelector] = useState(false);

  useEffect(() => {
    loadTransaction();
  }, [id]);

  const loadTransaction = async () => {
    try {
      setLoading(true);
      const transactionData = await TransactionAPI.getById(id);
      setTransaction(transactionData);
      
      // Initialize form data
      setFormData({
        amount: transactionData.amount,
        type: transactionData.type as TransactionType,
        category_id: transactionData.category_id,
        transaction_date: new Date(transactionData.transaction_date),
      });

      setAmountText(transactionData.amount.toLocaleString('en-US'));
      setDescription(transactionData.description || '');
      setNotes(transactionData.notes || '');
      setImageUrl(transactionData.image_url);

      if (transactionData.category) {
        setSelectedCategory({
          id: transactionData.category.id,
          name: transactionData.category.name,
          icon: transactionData.category.icon,
          type: transactionData.category.type,
        });
      }
    } catch (error) {
      console.error('Error loading transaction:', error);
      Alert.alert('Error', 'Unable to load transaction information');
      navigation.goBack();
    } finally {
      setLoading(false);
    }
  };

  const handleAmountChange = (text: string) => {
    const numericValue = text.replace(/[^0-9]/g, '');

    if (numericValue) {
      const amount = parseInt(numericValue, 10);
      const formattedValue = amount.toLocaleString('en-US');
      setAmountText(formattedValue);
      setFormData(prev => ({ ...prev, amount }));
    } else {
      setAmountText('');
      setFormData(prev => ({ ...prev, amount: 0 }));
    }
  };

  const handleDateChange = (event: any, selectedDate?: Date) => {
    setShowDatePicker(false);
    if (selectedDate) {
      setFormData(prev => ({ ...prev, transaction_date: selectedDate }));
    }
  };

  const handleCategorySelect = (category: Category) => {
    setSelectedCategory(category);
    setFormData(prev => ({ ...prev, category_id: category.id }));
  };

  const handleSubmit = async () => {
    if (!formData.amount || formData.amount <= 0) {
      Alert.alert('Error', 'Please enter a valid amount');
      return;
    }

    if (formData.transaction_date && formData.transaction_date > new Date()) {
      Alert.alert('Error', 'Transaction date cannot be in the future');
      return;
    }

    try {
      setSaving(true);

      const updateData: UpdateTransactionData = {
        ...formData,
        description: description.trim() || undefined,
        notes: notes.trim() || undefined,
        image_url: imageUrl?.trim() || undefined,
      };

      await TransactionAPI.update(id, updateData);

      Alert.alert(
        'Success',
        'Transaction has been updated successfully',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (error) {
      console.error('Error updating transaction:', error);
      Alert.alert('Error', 'Unable to update transaction. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const renderTypeSelector = () => (
    <Card style={styles.sectionCard}>
      <Text style={styles.sectionTitle}>Transaction Type</Text>
      <View style={styles.typeSelector}>
        <TouchableOpacity
          style={[
            styles.typeButton,
            formData.type === TransactionType.EXPENSE && styles.activeTypeButton,
          ]}
          onPress={() => {
            setFormData(prev => ({ ...prev, type: TransactionType.EXPENSE }));
            setSelectedCategory(null);
          }}
        >
          <Text style={styles.typeButtonIcon}>💸</Text>
          <Text
            style={[
              styles.typeButtonText,
              formData.type === TransactionType.EXPENSE && styles.activeTypeButtonText,
            ]}
          >
            Expense
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.typeButton,
            formData.type === TransactionType.INCOME && styles.activeTypeButton,
          ]}
          onPress={() => {
            setFormData(prev => ({ ...prev, type: TransactionType.INCOME }));
            setSelectedCategory(null);
          }}
        >
          <Text style={styles.typeButtonIcon}>💰</Text>
          <Text
            style={[
              styles.typeButtonText,
              formData.type === TransactionType.INCOME && styles.activeTypeButtonText,
            ]}
          >
            Income
          </Text>
        </TouchableOpacity>
      </View>
    </Card>
  );

  const renderAmountSection = () => (
    <Card style={styles.sectionCard}>
      <Text style={styles.sectionTitle}>Amount *</Text>
      <View style={styles.amountContainer}>
        <Text style={styles.currencySymbol}>₫</Text>
        <TextInput
          placeholder="0"
          keyboardType="numeric"
          value={amountText}
          onChangeText={handleAmountChange}
          containerStyle={styles.amountInputContainer}
          inputStyle={styles.amountInput}
        />
      </View>
      {formData.amount && formData.amount > 0 && (
        <Text style={styles.amountHelper}>
          {formData.amount.toLocaleString('en-US')} VND
        </Text>
      )}
    </Card>
  );

  const renderCategorySection = () => (
    <Card style={styles.sectionCard}>
      <Text style={styles.sectionTitle}>Category</Text>
      <TouchableOpacity
        style={styles.categoryButton}
        onPress={() => setShowCategorySelector(true)}
      >
        <View style={styles.categoryButtonContent}>
          <Text style={styles.categoryButtonIcon}>
            {selectedCategory?.icon || '📂'}
          </Text>
          <Text style={styles.categoryButtonText}>
            {selectedCategory?.name || 'Select Category'}
          </Text>
        </View>
        <Text style={styles.categoryButtonArrow}>›</Text>
      </TouchableOpacity>
    </Card>
  );

  const renderDateSection = () => (
    <Card style={styles.sectionCard}>
      <Text style={styles.sectionTitle}>Transaction Date *</Text>
      <TouchableOpacity
        style={styles.dateButton}
        onPress={() => setShowDatePicker(true)}
      >
        <Text style={styles.dateButtonIcon}>📅</Text>
        <Text style={styles.dateButtonText}>
          {formData.transaction_date ? formatDate(formData.transaction_date) : 'Select Date'}
        </Text>
      </TouchableOpacity>
    </Card>
  );

  const renderDescriptionSection = () => (
    <Card style={styles.sectionCard}>
      <TextInput
        label="Description"
        placeholder="Transaction description (optional)"
        value={description}
        onChangeText={setDescription}
        maxLength={255}
        multiline
        numberOfLines={3}
      />
      <Text style={styles.characterCount}>{description.length}/255</Text>
    </Card>
  );

  const renderNotesSection = () => (
    <Card style={styles.sectionCard}>
      <TextInput
        label="Notes"
        placeholder="Additional notes (optional)"
        value={notes}
        onChangeText={setNotes}
        maxLength={1000}
        multiline
        numberOfLines={4}
      />
      <Text style={styles.characterCount}>{notes.length}/1000</Text>
    </Card>
  );

  const renderImageSection = () => (
    <ImagePicker
      label="Transaction Image"
      placeholder="Add an image of your transaction (optional)"
      value={imageUrl}
      onImageSelected={setImageUrl}
      onImageRemoved={() => setImageUrl(undefined)}
      containerStyle={styles.sectionCard}
    />
  );

  if (loading) {
    return (
      <Screen>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary[500]} />
          <Text style={styles.loadingText}>Loading...</Text>
        </View>
      </Screen>
    );
  }

  return (
    <Screen>
      <KeyboardAwareScrollView
        style={styles.container}
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
        enableOnAndroid={true}
        enableAutomaticScroll={true}
        extraScrollHeight={20}
      >
        <Text style={styles.headerTitle}>Edit Transaction</Text>

        {renderTypeSelector()}
        {renderAmountSection()}
        {renderCategorySection()}
        {renderDateSection()}
        {renderDescriptionSection()}
        {renderNotesSection()}
        {renderImageSection()}

        <Button
          title={saving ? 'Saving...' : 'Update Transaction'}
          onPress={handleSubmit}
          disabled={saving || !formData.amount || formData.amount <= 0}
          style={styles.submitButton}
        />
      </KeyboardAwareScrollView>

      {showDatePicker && formData.transaction_date && (
        <DateTimePicker
          value={formData.transaction_date}
          mode="date"
          display="default"
          maximumDate={new Date()}
          minimumDate={new Date('2000-01-01')}
          onChange={handleDateChange}
        />
      )}

      {formData.type && (
        <CategorySelector
          visible={showCategorySelector}
          onClose={() => setShowCategorySelector(false)}
          onSelect={handleCategorySelect}
          transactionType={formData.type}
        />
      )}
    </Screen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background.secondary,
  },
  scrollContainer: {
    padding: theme.spacing.md,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: theme.spacing.sm,
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.secondary,
  },
  headerTitle: {
    fontSize: theme.typography.fontSizes['2xl'],
    fontWeight: theme.typography.fontWeights.bold,
    color: theme.colors.text.primary,
    textAlign: 'center',
    marginBottom: theme.spacing.xl,
  },
  sectionCard: {
    marginBottom: theme.spacing.md,
    padding: theme.spacing.md,
  },
  sectionTitle: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: theme.typography.fontWeights.semibold,
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.sm,
  },
  
  // Type Selector
  typeSelector: {
    flexDirection: 'row',
    backgroundColor: theme.colors.gray[100],
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.xs,
  },
  typeButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
    gap: theme.spacing.sm,
  },
  activeTypeButton: {
    backgroundColor: theme.colors.primary[500],
  },
  typeButtonIcon: {
    fontSize: 20,
  },
  typeButtonText: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: 'normal',
    color: theme.colors.text.secondary,
  },
  activeTypeButtonText: {
    color: theme.colors.text.inverse,
  },

  // Amount Section
  amountContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.gray[50],
    borderRadius: theme.borderRadius.lg,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
  },
  currencySymbol: {
    fontSize: theme.typography.fontSizes['2xl'],
    fontWeight: theme.typography.fontWeights.semibold,
    color: theme.colors.text.primary,
    marginRight: theme.spacing.sm,
  },
  amountInputContainer: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  amountInput: {
    fontSize: theme.typography.fontSizes['2xl'],
    fontWeight: theme.typography.fontWeights.semibold,
    textAlign: 'right',
  },
  amountHelper: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
    marginTop: theme.spacing.xs,
    textAlign: 'center',
  },

  // Category Section
  categoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.md,
    backgroundColor: theme.colors.gray[50],
    borderRadius: theme.borderRadius.md,
  },
  categoryButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.sm,
  },
  categoryButtonIcon: {
    fontSize: 24,
  },
  categoryButtonText: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.primary,
  },
  categoryButtonArrow: {
    fontSize: 20,
    color: theme.colors.text.secondary,
  },

  // Date Section
  dateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.md,
    backgroundColor: theme.colors.gray[50],
    borderRadius: theme.borderRadius.md,
    gap: theme.spacing.sm,
  },
  dateButtonIcon: {
    fontSize: 20,
  },
  dateButtonText: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.primary,
  },

  // Character Count
  characterCount: {
    fontSize: theme.typography.fontSizes.xs,
    color: theme.colors.text.secondary,
    textAlign: 'right',
    marginTop: theme.spacing.xs,
  },

  // Submit Button
  submitButton: {
    marginTop: theme.spacing.xl,
    marginBottom: theme.spacing.xl,
  },

  // Modal Styles
  modalContainer: {
    flex: 1,
    backgroundColor: theme.colors.background.primary,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray[200],
  },
  modalCancelText: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.secondary,
  },
  modalTitle: {
    fontSize: theme.typography.fontSizes.lg,
    fontWeight: theme.typography.fontWeights.semibold,
    color: theme.colors.text.primary,
  },
  categoriesGrid: {
    padding: theme.spacing.md,
  },
  categoryItem: {
    flex: 1,
    aspectRatio: 1,
    margin: theme.spacing.xs,
    padding: theme.spacing.md,
    backgroundColor: theme.colors.background.secondary,
    borderRadius: theme.borderRadius.lg,
    alignItems: 'center',
    justifyContent: 'center',
    gap: theme.spacing.sm,
  },
  categoryItemIcon: {
    fontSize: 32,
  },
  categoryItemName: {
    fontSize: theme.typography.fontSizes.sm,
    fontWeight: 'normal',
    color: theme.colors.text.primary,
    textAlign: 'center',
  },
});

export default EditTransactionScreen; 