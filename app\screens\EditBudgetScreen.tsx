import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useNavigation, useRoute } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation/types';
import theme from '../theme';
import Screen from '../components/common/Screen';
import Button from '../components/ui/Button';
import Card from '../components/ui/Card';
import TextInput from '../components/ui/TextInput';
import { BudgetAPI, IBudget, UpdateBudgetDto, BudgetStatus } from '../../src/api/budget.api';

type EditBudgetScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;

const EditBudgetScreen = () => {
  const navigation = useNavigation<EditBudgetScreenNavigationProp>();
  const route = useRoute();
  const { id } = route.params as { id: string };

  // State management
  const [budget, setBudget] = useState<IBudget | null>(null);
  const [amount, setAmount] = useState('');
  const [status, setStatus] = useState<BudgetStatus>(BudgetStatus.ACTIVE);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  // Load budget details
  const loadBudget = async () => {
    try {
      setLoading(true);
      const budgetData = await BudgetAPI.getById(id);
      setBudget(budgetData);
      setAmount(budgetData.amount.toLocaleString('vi-VN'));
      setStatus(budgetData.status);
    } catch (error: any) {
      Alert.alert('Error', 'Failed to load budget details');
      navigation.goBack();
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadBudget();
  }, [id]);

  const formatCurrency = (value: string) => {
    // Remove non-numeric characters
    const numericValue = value.replace(/[^0-9]/g, '');

    // Format with thousand separators
    if (numericValue) {
      return parseInt(numericValue).toLocaleString('vi-VN');
    }
    return '';
  };

  const handleAmountChange = (value: string) => {
    const formatted = formatCurrency(value);
    setAmount(formatted);
  };

  const getNumericAmount = () => {
    return parseInt(amount.replace(/[^0-9]/g, '') || '0');
  };

  const handleSave = async () => {
    const numericAmount = getNumericAmount();
    if (numericAmount <= 0) {
      Alert.alert('Error', 'Please enter a valid amount');
      return;
    }

    try {
      setSaving(true);

      const updateData: UpdateBudgetDto = {
        amount: numericAmount,
        status,
      };

      await BudgetAPI.update(id, updateData);

      Alert.alert(
        'Success',
        'Budget updated successfully!',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (error: any) {
      let errorMessage = 'Failed to update budget. Please try again.';

      if (error.response?.status === 400) {
        errorMessage = error.response.data?.message || 'Invalid budget data.';
      } else if (error.response?.status === 401) {
        errorMessage = 'Authentication failed. Please login again.';
      } else if (error.response?.status === 404) {
        errorMessage = 'Budget not found.';
      } else if (error.message) {
        errorMessage = error.message;
      }

      Alert.alert('Error', errorMessage);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <Screen>
        <View style={[styles.container, { justifyContent: 'center', alignItems: 'center' }]}>
          <ActivityIndicator size="large" color={theme.colors.primary[500]} />
          <Text style={{ marginTop: theme.spacing.md, color: theme.colors.text.secondary }}>
            Loading budget details...
          </Text>
        </View>
      </Screen>
    );
  }

  if (!budget) {
    return (
      <Screen>
        <View style={[styles.container, { justifyContent: 'center', alignItems: 'center' }]}>
          <Text style={styles.errorText}>Budget not found</Text>
          <Button
            title="Go Back"
            onPress={() => navigation.goBack()}
            variant="primary"
            style={{ marginTop: theme.spacing.md }}
          />
        </View>
      </Screen>
    );
  }

  return (
    <Screen>
      <KeyboardAwareScrollView
        style={styles.container}
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
        enableOnAndroid={true}
        enableAutomaticScroll={true}
        extraScrollHeight={20}
        extraHeight={100}
      >
        <View style={styles.content}>
          <Text style={styles.title}>Edit Budget</Text>
          <Text style={styles.subtitle}>
            {budget.category?.name} - {new Date(budget.year, budget.month - 1).toLocaleDateString('en-US', {
              month: 'long',
              year: 'numeric',
            })}
          </Text>

          {/* Amount Input */}
          <Card style={styles.card}>
            <Text style={styles.sectionTitle}>Budget Amount</Text>
            <TextInput
              label="Amount (VND)"
              value={amount}
              onChangeText={handleAmountChange}
              placeholder="Enter budget amount"
              keyboardType="numeric"
              style={styles.amountInput}
            />
            {amount && (
              <Text style={styles.amountPreview}>
                Budget: {amount} VND
              </Text>
            )}
          </Card>

          {/* Status Selection */}
          <Card style={styles.card}>
            <Text style={styles.sectionTitle}>Status</Text>
            <View style={styles.statusContainer}>
              <Button
                title="Active"
                onPress={() => setStatus(BudgetStatus.ACTIVE)}
                variant={status === BudgetStatus.ACTIVE ? "primary" : "secondary"}
                style={[styles.statusButton, { marginRight: theme.spacing.xs }]}
              />
              <Button
                title="Ended"
                onPress={() => setStatus(BudgetStatus.ENDED)}
                variant={status === BudgetStatus.ENDED ? "primary" : "secondary"}
                style={[styles.statusButton, { marginLeft: theme.spacing.xs }]}
              />
            </View>
          </Card>

          {/* Save Button */}
          <Button
            title={saving ? "Saving..." : "Save Changes"}
            onPress={handleSave}
            variant="primary"
            disabled={saving || !amount}
            style={styles.saveButton}
          />
        </View>
      </KeyboardAwareScrollView>
    </Screen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    paddingBottom: theme.spacing.xl,
  },
  content: {
    padding: theme.spacing.md,
  },
  title: {
    fontSize: theme.typography.fontSizes['2xl'],
    fontWeight: '700',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.xs,
  },
  subtitle: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.secondary,
    marginBottom: theme.spacing.lg,
  },
  card: {
    marginBottom: theme.spacing.lg,
  },
  sectionTitle: {
    fontSize: theme.typography.fontSizes.lg,
    fontWeight: '600',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.md,
  },
  amountInput: {
    marginBottom: theme.spacing.sm,
    fontSize: theme.typography.fontSizes.lg,
    height: 60, // Tăng chiều cao
    paddingHorizontal: theme.spacing.md,
  },
  amountPreview: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '600',
    color: theme.colors.primary[600],
    textAlign: 'center',
  },
  statusContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statusButton: {
    flex: 1,
    marginVertical: 0,
  },
  saveButton: {
    marginTop: theme.spacing.md,
  },
  errorText: {
    fontSize: theme.typography.fontSizes.lg,
    color: theme.colors.error,
    textAlign: 'center',
  },
});

export default EditBudgetScreen;
