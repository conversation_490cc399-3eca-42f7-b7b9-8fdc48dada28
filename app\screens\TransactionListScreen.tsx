import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Alert,
  RefreshControl,
  ActivityIndicator,
  Modal,
} from 'react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

import { RootStackParamList } from '../navigation/types';
import { Transaction, TransactionQuery, TransactionType } from '../types/transaction';
import { TransactionAPI } from '../../src/api/transaction.api';
import theme from '../theme';
import Screen from '../components/common/Screen';
import DrawerHeader from '../components/common/DrawerHeader';
import TextInput from '../components/ui/TextInput';
import Button from '../components/ui/Button';
import Card from '../components/ui/Card';


type TransactionListScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'TransactionList'
>;

interface FilterModalProps {
  visible: boolean;
  onClose: () => void;
  onApply: (filters: TransactionQuery) => void;
  currentFilters: TransactionQuery;
}

const FilterModal: React.FC<FilterModalProps> = ({
  visible,
  onClose,
  onApply,
  currentFilters
}) => {
  const [filters, setFilters] = useState<TransactionQuery>(currentFilters);
  const [showStartDatePicker, setShowStartDatePicker] = useState(false);
  const [showEndDatePicker, setShowEndDatePicker] = useState(false);

  const handleApply = () => {
    onApply(filters);
    onClose();
  };

  const handleReset = () => {
    const resetFilters = { page: 1, limit: 10 };
    setFilters(resetFilters);
    onApply(resetFilters);
    onClose();
  };

  const handleStartDateChange = (_: any, selectedDate?: Date) => {
    setShowStartDatePicker(false);
    if (selectedDate) {
      setFilters({ ...filters, start_date: selectedDate });
    }
  };

  const handleEndDateChange = (_: any, selectedDate?: Date) => {
    setShowEndDatePicker(false);
    if (selectedDate) {
      setFilters({ ...filters, end_date: selectedDate });
    }
  };

  const formatDisplayDate = (date?: Date) => {
    if (!date) return 'Select date';
    return date.toLocaleDateString('en-US');
  };

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <View style={styles.modalContainer}>
        <View style={styles.modalHeader}>
          <TouchableOpacity onPress={onClose}>
            <Text style={styles.modalCancelText}>Cancel</Text>
          </TouchableOpacity>
          <Text style={styles.modalTitle}>Filter</Text>
          <TouchableOpacity onPress={handleReset}>
            <Text style={styles.modalResetText}>Reset</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.modalContent}>
          {/* Transaction Type Filter */}
          <View style={styles.filterSection}>
            <Text style={styles.filterLabel}>Transaction Type</Text>
            <View style={styles.typeFilterContainer}>
              <TouchableOpacity
                style={[
                  styles.typeFilterButton,
                  !filters.type && styles.activeTypeFilter,
                ]}
                onPress={() => setFilters({ ...filters, type: undefined })}
              >
                <Text style={[
                  styles.typeFilterText,
                  !filters.type && styles.activeTypeFilterText,
                ]}>All</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.typeFilterButton,
                  filters.type === TransactionType.INCOME && styles.activeTypeFilter,
                ]}
                onPress={() => setFilters({ ...filters, type: TransactionType.INCOME })}
              >
                <Text style={[
                  styles.typeFilterText,
                  filters.type === TransactionType.INCOME && styles.activeTypeFilterText,
                ]}>Income</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.typeFilterButton,
                  filters.type === TransactionType.EXPENSE && styles.activeTypeFilter,
                ]}
                onPress={() => setFilters({ ...filters, type: TransactionType.EXPENSE })}
              >
                <Text style={[
                  styles.typeFilterText,
                  filters.type === TransactionType.EXPENSE && styles.activeTypeFilterText,
                ]}>Expense</Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Date Range Filter */}
          <View style={styles.filterSection}>
            <View style={styles.dateRangeHeader}>
              <Text style={styles.filterLabel}>📅 Date Range</Text>
              {(filters.start_date || filters.end_date) && (
                <TouchableOpacity
                  style={styles.clearDateIconButton}
                  onPress={() => setFilters({ ...filters, start_date: undefined, end_date: undefined })}
                >
                  <Text style={styles.clearDateIcon}>✕</Text>
                </TouchableOpacity>
              )}
            </View>

            <View style={styles.dateRangeRow}>
              {/* Start Date Column */}
              <View style={styles.dateColumn}>
                <Text style={styles.dateColumnLabel}>From</Text>
                <TouchableOpacity
                  style={[
                    styles.datePickerButton,
                    filters.start_date && styles.datePickerButtonSelected
                  ]}
                  onPress={() => setShowStartDatePicker(true)}
                >
                  <Text style={styles.datePickerIcon}>📅</Text>
                  <View style={styles.datePickerTextContainer}>
                    <Text style={[
                      styles.datePickerText,
                      filters.start_date && styles.datePickerTextSelected
                    ]}>
                      {filters.start_date ? formatDisplayDate(filters.start_date) : 'Select start date'}
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>

              {/* Separator */}
              <View style={styles.dateSeparatorContainer}>
                <Text style={styles.dateSeparatorText}>to</Text>
              </View>

              {/* End Date Column */}
              <View style={styles.dateColumn}>
                <Text style={styles.dateColumnLabel}>To</Text>
                <TouchableOpacity
                  style={[
                    styles.datePickerButton,
                    filters.end_date && styles.datePickerButtonSelected
                  ]}
                  onPress={() => setShowEndDatePicker(true)}
                >
                  <Text style={styles.datePickerIcon}>📅</Text>
                  <View style={styles.datePickerTextContainer}>
                    <Text style={[
                      styles.datePickerText,
                      filters.end_date && styles.datePickerTextSelected
                    ]}>
                      {filters.end_date ? formatDisplayDate(filters.end_date) : 'Select end date'}
                    </Text>
                  </View>
                </TouchableOpacity>
              </View>
            </View>

            {/* Date Range Summary */}
            {(filters.start_date || filters.end_date) && (
              <View style={styles.dateRangeSummary}>
                <Text style={styles.dateRangeSummaryText}>
                  {filters.start_date && filters.end_date
                    ? `Showing transactions from ${formatDisplayDate(filters.start_date)} to ${formatDisplayDate(filters.end_date)}`
                    : filters.start_date
                    ? `Showing transactions from ${formatDisplayDate(filters.start_date)} onwards`
                    : `Showing transactions up to ${formatDisplayDate(filters.end_date)}`
                  }
                </Text>
              </View>
            )}

            {/* Date Pickers - Show immediately when button pressed */}
            {showStartDatePicker && (
              <DateTimePicker
                value={filters.start_date || new Date()}
                mode="date"
                display="default"
                onChange={handleStartDateChange}
              />
            )}

            {showEndDatePicker && (
              <DateTimePicker
                value={filters.end_date || new Date()}
                mode="date"
                display="default"
                onChange={handleEndDateChange}
              />
            )}
          </View>

          {/* Sort Options */}
          <View style={styles.filterSection}>
            <Text style={styles.filterLabel}>Sort By</Text>
            <View style={styles.sortContainer}>
              {[
                { key: 'transaction_date', label: 'Transaction Date' },
                { key: 'amount', label: 'Amount' },
                { key: 'created_at', label: 'Created Date' },
              ].map((option) => (
                <TouchableOpacity
                  key={option.key}
                  style={[
                    styles.sortOption,
                    filters.sort_by === option.key && styles.activeSortOption,
                  ]}
                  onPress={() => setFilters({ ...filters, sort_by: option.key as any })}
                >
                  <Text style={[
                    styles.sortOptionText,
                    filters.sort_by === option.key && styles.activeSortOptionText,
                  ]}>{option.label}</Text>
                </TouchableOpacity>
              ))}
            </View>

            <View style={styles.sortOrderContainer}>
              <TouchableOpacity
                style={[
                  styles.sortOrderButton,
                  filters.sort_order === 'DESC' && styles.activeSortOrder,
                ]}
                onPress={() => setFilters({ ...filters, sort_order: 'DESC' })}
              >
                <Text style={[
                  styles.sortOrderText,
                  filters.sort_order === 'DESC' && styles.activeSortOrderText,
                ]}>Descending</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.sortOrderButton,
                  filters.sort_order === 'ASC' && styles.activeSortOrder,
                ]}
                onPress={() => setFilters({ ...filters, sort_order: 'ASC' })}
              >
                <Text style={[
                  styles.sortOrderText,
                  filters.sort_order === 'ASC' && styles.activeSortOrderText,
                ]}>Ascending</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>

        <View style={styles.modalFooter}>
          <Button
            title="Apply"
            onPress={handleApply}
            style={styles.applyButton}
          />
        </View>


      </View>
    </Modal>
  );
};

const TransactionListScreen = () => {
  const navigation = useNavigation<TransactionListScreenNavigationProp>();
  
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilterModal, setShowFilterModal] = useState(false);
  
  const [filters, setFilters] = useState<TransactionQuery>({
    page: 1,
    limit: 10,
    sort_by: 'transaction_date',
    sort_order: 'DESC',
  });

  const [totalItems, setTotalItems] = useState(0);

  const loadTransactions = useCallback(async (isRefresh = false, isLoadMore = false) => {
    if (loading && !isRefresh && !isLoadMore) return;
    
    try {
      if (isRefresh) {
        setRefreshing(true);
      } else if (isLoadMore) {
        setLoadingMore(true);
      } else {
        setLoading(true);
      }

      const currentPage = isLoadMore ? (filters.page || 1) + 1 : isRefresh ? 1 : (filters.page || 1);
      const queryFilters = {
        ...filters,
        page: currentPage,
        search: searchQuery || undefined,
      };

      const response = await TransactionAPI.getAll(queryFilters);

      if (isRefresh || currentPage === 1) {
        setTransactions(response.data);
      } else {
        setTransactions(prev => [...prev, ...response.data]);
      }

      setTotalItems(response.meta.totalItems);
      setHasMore(currentPage < response.meta.totalPages);
      
      if (isLoadMore) {
        setFilters(prev => ({ ...prev, page: currentPage }));
      }

    } catch (error) {
      Alert.alert('Error', 'Unable to load transactions');
    } finally {
      setLoading(false);
      setRefreshing(false);
      setLoadingMore(false);
    }
  }, [filters, searchQuery, loading]);

  useFocusEffect(
    useCallback(() => {
      loadTransactions();
    }, [])
  );

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (filters.page === 1) {
        loadTransactions();
      } else {
        setFilters(prev => ({ ...prev, page: 1 }));
      }
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [searchQuery]);

  useEffect(() => {
    loadTransactions();
  }, [filters]);

  const handleLoadMore = () => {
    if (!loadingMore && hasMore && !loading) {
      loadTransactions(false, true);
    }
  };

  const handleRefresh = () => {
    loadTransactions(true);
  };

  const formatCurrency = (amount: number) => {
    return amount.toLocaleString('vi-VN') + ' ₫';
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN');
  };

  const handleApplyFilters = (newFilters: TransactionQuery) => {
    setFilters({ ...newFilters, page: 1 });
  };

  const renderTransactionItem = ({ item }: { item: Transaction }) => (
    <TouchableOpacity
      onPress={() => navigation.navigate('TransactionDetail', { id: item.id })}
    >
      <Card style={styles.transactionCard}>
        <View style={styles.transactionContent}>
          <View style={styles.transactionLeft}>
            <View style={styles.categoryContainer}>
              <Text style={styles.categoryIcon}>
                {item.category?.icon || (item.type === TransactionType.INCOME ? '💰' : '💸')}
              </Text>
              <View style={styles.transactionInfo}>
                <Text style={styles.categoryName}>
                  {item.category?.name || 'Không có danh mục'}
                </Text>
                <Text style={styles.transactionDate}>
                  {formatDate(item.transaction_date)}
                </Text>
                {item.description && (
                  <Text style={styles.description} numberOfLines={1}>
                    {item.description}
                  </Text>
                )}
              </View>
            </View>
          </View>
          
          <View style={styles.transactionRight}>
            <Text style={[
              styles.amountText,
              item.type === TransactionType.INCOME ? styles.incomeAmount : styles.expenseAmount
            ]}>
              {item.type === TransactionType.INCOME ? '+' : '-'} {formatCurrency(item.amount)}
            </Text>
          </View>
        </View>
      </Card>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyIcon}>📋</Text>
      <Text style={styles.emptyTitle}>No Transactions</Text>
      <Text style={styles.emptySubtitle}>
        {searchQuery || Object.keys(filters).length > 3
          ? 'No transactions found matching your criteria'
          : 'Add your first transaction'
        }
      </Text>
      {!searchQuery && Object.keys(filters).length <= 3 && (
        <Button
          title="Add Transaction"
          onPress={() => navigation.navigate('CreateTransaction')}
          style={styles.emptyButton}
        />
      )}
    </View>
  );

  const renderFooter = () => {
    if (!loadingMore) return null;
    
    return (
      <View style={styles.footerLoading}>
        <ActivityIndicator size="small" color={theme.colors.primary[500]} />
        <Text style={styles.footerLoadingText}>Loading more...</Text>
      </View>
    );
  };

  const renderHeader = () => (
    <View style={styles.headerContainer}>
      <View style={styles.searchContainer}>
        <TextInput
          placeholder="Search transactions..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          containerStyle={styles.searchInput}
        />
        <TouchableOpacity
          style={styles.filterButton}
          onPress={() => setShowFilterModal(true)}
        >
          <Text style={styles.filterButtonText}>⚙️</Text>
        </TouchableOpacity>
      </View>
      
      {totalItems > 0 && (
        <Text style={styles.resultCount}>
          Found {totalItems} transactions
        </Text>
      )}
    </View>
  );

  return (
    <Screen>
      <DrawerHeader title="Transactions" />

      {/* Header Section */}
      <View style={styles.header}>
        <Text style={styles.title}>Transactions</Text>
        <Text style={styles.subtitle}>
          Manage your {totalItems} transactions
        </Text>
      </View>

      {renderHeader()}

      <FlatList
        data={transactions}
        renderItem={renderTransactionItem}
        keyExtractor={(item) => item.id}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme.colors.primary[500]]}
          />
        }
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.3}
        ListEmptyComponent={!loading ? renderEmptyState : null}
        ListFooterComponent={renderFooter}
        contentContainerStyle={
          transactions.length === 0 ? styles.emptyListContainer : styles.listContainer
        }
        showsVerticalScrollIndicator={false}
      />

      {loading && transactions.length === 0 && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary[500]} />
          <Text style={styles.loadingText}>Loading...</Text>
        </View>
      )}

      <FilterModal
        visible={showFilterModal}
        onClose={() => setShowFilterModal(false)}
        onApply={handleApplyFilters}
        currentFilters={filters}
      />

      {/* Add Button Container */}
      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => navigation.navigate('CreateTransaction')}
          activeOpacity={0.8}
        >
          <Text style={styles.addButtonText}>+ Add New Transaction</Text>
        </TouchableOpacity>
      </View>
    </Screen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background.secondary,
  },
  header: {
    paddingHorizontal: theme.spacing.md,
    paddingTop: theme.spacing.md,
    paddingBottom: theme.spacing.md,
  },
  title: {
    fontSize: theme.typography.fontSizes['2xl'],
    fontWeight: '700',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.xs,
  },
  subtitle: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.secondary,
  },
  headerContainer: {
    backgroundColor: theme.colors.background.primary,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray[200],
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.sm,
  },
  searchInput: {
    flex: 1,
    marginBottom: 0,
  },
  filterButton: {
    width: 50,
    height: 50,
    backgroundColor: theme.colors.primary[500],
    borderRadius: theme.borderRadius.lg,
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'flex-end',
  },
  filterButtonText: {
    fontSize: 20,
  },
  resultCount: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
    marginTop: theme.spacing.xs,
  },
  listContainer: {
    padding: theme.spacing.md,
  },
  emptyListContainer: {
    flex: 1,
    padding: theme.spacing.md,
  },
  transactionCard: {
    marginBottom: theme.spacing.sm,
    padding: theme.spacing.md,
  },
  transactionContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  transactionLeft: {
    flex: 1,
  },
  categoryContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.sm,
  },
  categoryIcon: {
    fontSize: 24,
    width: 40,
    textAlign: 'center',
  },
  transactionInfo: {
    flex: 1,
  },
  categoryName: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: theme.typography.fontWeights.medium,
    color: theme.colors.text.primary,
  },
  transactionDate: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
    marginTop: theme.spacing.xs / 2,
  },
  description: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
    marginTop: theme.spacing.xs / 2,
  },
  transactionRight: {
    alignItems: 'flex-end',
  },
  amountText: {
    fontSize: theme.typography.fontSizes.lg,
    fontWeight: theme.typography.fontWeights.semibold,
  },
  incomeAmount: {
    color: theme.colors.success,
  },
  expenseAmount: {
    color: theme.colors.error,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.xl,
  },
  emptyIcon: {
    fontSize: 64,
    marginBottom: theme.spacing.lg,
  },
  emptyTitle: {
    fontSize: theme.typography.fontSizes['2xl'],
    fontWeight: theme.typography.fontWeights.semibold,
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.sm,
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.secondary,
    textAlign: 'center',
    marginBottom: theme.spacing.xl,
    lineHeight: 24,
  },
  emptyButton: {
    paddingHorizontal: theme.spacing.xl,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: theme.spacing.sm,
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.secondary,
  },
  footerLoading: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: theme.spacing.md,
    gap: theme.spacing.sm,
  },
  footerLoadingText: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
  },
  buttonContainer: {
    padding: theme.spacing.md,
    backgroundColor: theme.colors.background.primary,
    borderTopWidth: 1,
    borderTopColor: theme.colors.gray[200],
  },
  addButton: {
    backgroundColor: theme.colors.primary[500],
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.lg,
    borderRadius: theme.borderRadius.lg,
    alignItems: 'center',
    justifyContent: 'center',
  },
  addButtonText: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.background.primary,
    fontWeight: '600',
  },
  
  // Modal styles
  modalContainer: {
    flex: 1,
    backgroundColor: theme.colors.background.primary,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray[200],
  },
  modalCancelText: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.secondary,
  },
  modalTitle: {
    fontSize: theme.typography.fontSizes.lg,
    fontWeight: theme.typography.fontWeights.semibold,
    color: theme.colors.text.primary,
  },
  modalResetText: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.primary[500],
  },
  modalContent: {
    flex: 1,
    padding: theme.spacing.md,
  },
  modalFooter: {
    padding: theme.spacing.md,
    borderTopWidth: 1,
    borderTopColor: theme.colors.gray[200],
  },
  applyButton: {
    marginTop: 0,
  },
  filterSection: {
    marginBottom: theme.spacing.xl,
  },
  filterLabel: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: theme.typography.fontWeights.medium,
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.sm,
  },
  typeFilterContainer: {
    flexDirection: 'row',
    backgroundColor: theme.colors.gray[100],
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.xs,
  },
  typeFilterButton: {
    flex: 1,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.borderRadius.sm,
    alignItems: 'center',
  },
  activeTypeFilter: {
    backgroundColor: theme.colors.primary[500],
  },
  typeFilterText: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
  },
  activeTypeFilterText: {
    color: theme.colors.text.inverse,
    fontWeight: theme.typography.fontWeights.medium,
  },
  // New Date Range Styles
  dateRangeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  clearDateIconButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: theme.colors.error,
    justifyContent: 'center',
    alignItems: 'center',
  },
  clearDateIcon: {
    fontSize: 14,
    color: theme.colors.text.inverse,
    fontWeight: 'bold',
  },
  dateRangeRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: theme.spacing.sm,
  },
  dateColumn: {
    flex: 1,
  },
  dateColumnLabel: {
    fontSize: theme.typography.fontSizes.sm,
    fontWeight: theme.typography.fontWeights.medium,
    color: theme.colors.text.secondary,
    marginBottom: theme.spacing.xs,
    textAlign: 'center',
  },
  datePickerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.sm,
    backgroundColor: theme.colors.gray[100],
    borderRadius: theme.borderRadius.lg,
    borderWidth: 2,
    borderColor: 'transparent',
    minHeight: 50,
  },
  datePickerButtonSelected: {
    backgroundColor: theme.colors.primary[50],
    borderColor: theme.colors.primary[500],
  },
  datePickerIcon: {
    fontSize: 18,
    marginRight: theme.spacing.xs,
  },
  datePickerTextContainer: {
    flex: 1,
  },
  datePickerText: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
    textAlign: 'center',
  },
  datePickerTextSelected: {
    color: theme.colors.primary[700],
    fontWeight: theme.typography.fontWeights.medium,
  },
  dateSeparatorContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.xs,
    marginBottom: theme.spacing.sm,
  },
  dateSeparatorText: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
    fontStyle: 'italic',
  },
  dateRangeSummary: {
    marginTop: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.md,
    backgroundColor: theme.colors.primary[50],
    borderRadius: theme.borderRadius.md,
    borderLeftWidth: 4,
    borderLeftColor: theme.colors.primary[500],
  },
  dateRangeSummaryText: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.primary[700],
    fontWeight: theme.typography.fontWeights.medium,
    textAlign: 'center',
  },

  // Legacy styles (keeping for compatibility)
  dateRangeContainer: {
    gap: theme.spacing.md,
  },
  dateInputWrapper: {
    flex: 1,
  },
  datePickerWrapper: {
    flex: 1,
  },
  datePickerContainer: {
    marginBottom: 0,
  },
  dateLabel: {
    fontSize: theme.typography.fontSizes.sm,
    fontWeight: theme.typography.fontWeights.medium,
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.xs,
  },
  dateButton: {
    flex: 1,
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.md,
    backgroundColor: theme.colors.gray[100],
    borderRadius: theme.borderRadius.md,
    alignItems: 'center',
  },
  dateButtonText: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.primary,
  },
  clearDateButton: {
    marginTop: theme.spacing.sm,
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.md,
    backgroundColor: theme.colors.error,
    borderRadius: theme.borderRadius.md,
    alignItems: 'center',
  },
  clearDateText: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.inverse,
    fontWeight: theme.typography.fontWeights.medium,
  },
  dateSeparator: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
  },
  sortContainer: {
    gap: theme.spacing.sm,
  },
  sortOption: {
    paddingVertical: theme.spacing.sm,
    paddingHorizontal: theme.spacing.md,
    backgroundColor: theme.colors.gray[100],
    borderRadius: theme.borderRadius.md,
  },
  activeSortOption: {
    backgroundColor: theme.colors.primary[500],
  },
  sortOptionText: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.primary,
    textAlign: 'center',
  },
  activeSortOptionText: {
    color: theme.colors.text.inverse,
    fontWeight: theme.typography.fontWeights.medium,
  },
  sortOrderContainer: {
    flexDirection: 'row',
    marginTop: theme.spacing.sm,
    backgroundColor: theme.colors.gray[100],
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.xs,
  },
  sortOrderButton: {
    flex: 1,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.borderRadius.sm,
    alignItems: 'center',
  },
  activeSortOrder: {
    backgroundColor: theme.colors.primary[500],
  },
  sortOrderText: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
  },
  activeSortOrderText: {
    color: theme.colors.text.inverse,
    fontWeight: theme.typography.fontWeights.medium,
  },
});

export default TransactionListScreen; 