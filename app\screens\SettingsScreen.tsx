import React, { useState } from 'react';
import { View, Text, Switch, TouchableOpacity, ScrollView, StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import theme from '../theme';
import Screen from '../components/common/Screen';
import Card from '../components/ui/Card';

interface SettingItemProps {
  title: string;
  description?: string;
  value?: boolean;
  onValueChange?: (value: boolean) => void;
  onPress?: () => void;
  isSwitch?: boolean;
  iconBgColor?: string;
  icon?: string;
}

const SettingItem: React.FC<SettingItemProps> = ({
  title,
  description,
  value,
  onValueChange,
  onPress,
  isSwitch = false,
  iconBgColor,
  icon,
}) => {
  return (
    <TouchableOpacity
      style={styles.settingItem}
      onPress={onPress}
      disabled={isSwitch || !onPress}
    >
      {icon && (
        <View style={[styles.settingIcon, { backgroundColor: iconBgColor || theme.colors.primary[100] }]}>
          <Text style={styles.iconText}>{icon}</Text>
        </View>
      )}
      <View style={styles.settingContent}>
        <Text style={styles.settingTitle}>{title}</Text>
        {description && (
          <Text style={styles.settingDescription}>{description}</Text>
        )}
      </View>
      {isSwitch && (
        <Switch
          value={value}
          onValueChange={onValueChange}
          trackColor={{ false: theme.colors.gray[200], true: theme.colors.primary[500] }}
          thumbColor={theme.colors.background.primary}
        />
      )}
      {!isSwitch && onPress && (
        <Text style={styles.settingArrow}>›</Text>
      )}
    </TouchableOpacity>
  );
};

const SettingsScreen = () => {
  const [settings, setSettings] = useState({
    notifications: true,
    darkMode: false,
    locationServices: true,
    autoUpdate: false,
    dataSaving: false,
  });

  const handleToggle = (setting: keyof typeof settings) => (value: boolean) => {
    setSettings((prev) => ({ ...prev, [setting]: value }));
  };

  return (
    <Screen>
      <StatusBar style="dark" />
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Settings</Text>
          <Text style={styles.headerSubtitle}>
            Manage your app preferences and account settings
          </Text>
        </View>

        {/* App Settings Section */}
        <View style={styles.section}>
          <Text style={styles.sectionHeader}>App Settings</Text>
          
          <Card style={styles.card}>
            <SettingItem
              icon="🔔"
              iconBgColor={theme.colors.primary[100]}
              title="Notifications"
              description="Receive push notifications"
              isSwitch
              value={settings.notifications}
              onValueChange={handleToggle('notifications')}
            />
            
            <View style={styles.divider} />
            
            <SettingItem
              icon="🌙"
              iconBgColor={theme.colors.secondary[100]}
              title="Dark Mode"
              description="Enable dark theme"
              isSwitch
              value={settings.darkMode}
              onValueChange={handleToggle('darkMode')}
            />
            
            <View style={styles.divider} />
            
            <SettingItem
              icon="📍"
              iconBgColor={theme.colors.warning[100]}
              title="Location Services"
              description="Allow app to use your location"
              isSwitch
              value={settings.locationServices}
              onValueChange={handleToggle('locationServices')}
            />
            
            <View style={styles.divider} />
            
            <SettingItem
              icon="🔄"
              iconBgColor={theme.colors.success[100]}
              title="Auto Update"
              description="Update app automatically"
              isSwitch
              value={settings.autoUpdate}
              onValueChange={handleToggle('autoUpdate')}
            />
            
            <View style={styles.divider} />
            
            <SettingItem
              icon="📊"
              iconBgColor={theme.colors.warning[100]}
              title="Data Saving Mode"
              description="Reduce data usage"
              isSwitch
              value={settings.dataSaving}
              onValueChange={handleToggle('dataSaving')}
            />
          </Card>
        </View>

        {/* Account Section */}
        <View style={styles.section}>
          <Text style={styles.sectionHeader}>Account</Text>
          
          <Card style={styles.card}>
            <SettingItem
              icon="👤"
              iconBgColor={theme.colors.gray[100]}
              title="Personal Information"
              description="Update your profile information"
              onPress={() => alert('Navigate to personal information')}
            />
            
            <View style={styles.divider} />
            
            <SettingItem
              icon="🔒"
              iconBgColor={theme.colors.error[100]}
              title="Security"
              description="Change password and security settings"
              onPress={() => alert('Navigate to security settings')}
            />
            
            <View style={styles.divider} />
            
            <SettingItem
              icon="🔐"
              iconBgColor={theme.colors.primary[100]}
              title="Privacy Policy"
              onPress={() => alert('Navigate to privacy policy')}
            />
            
            <View style={styles.divider} />
            
            <SettingItem
              icon="📄"
              iconBgColor={theme.colors.secondary[100]}
              title="Terms of Service"
              onPress={() => alert('Navigate to terms of service')}
            />
          </Card>
        </View>

        {/* Support Section */}
        <View style={styles.section}>
          <Text style={styles.sectionHeader}>Support</Text>
          
          <Card style={styles.card}>
            <SettingItem
              icon="❓"
              iconBgColor={theme.colors.success[100]}
              title="Help & Support"
              description="Get help with using the app"
              onPress={() => alert('Navigate to help and support')}
            />
            
            <View style={styles.divider} />
            
            <SettingItem
              icon="ℹ️"
              iconBgColor={theme.colors.primary[100]}
              title="About"
              description="App version 1.0.0"
              onPress={() => alert('Navigate to about')}
            />
            
            <View style={styles.divider} />
            
            <SettingItem
              icon="🚪"
              iconBgColor={theme.colors.error[100]}
              title="Sign Out"
              onPress={() => alert('Sign out user')}
            />
          </Card>
        </View>
        
        <View style={{height: theme.spacing.xl}} />
      </ScrollView>
    </Screen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background.primary,
  },
  header: {
    paddingHorizontal: theme.spacing.xl,
    paddingTop: theme.spacing.xl,
    paddingBottom: theme.spacing.md,
  },
  headerTitle: {
    fontSize: theme.typography.fontSizes['2xl'],
    fontWeight: '700',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.sm,
  },
  headerSubtitle: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.secondary,
    lineHeight: 22,
  },
  section: {
    paddingHorizontal: theme.spacing.xl,
    marginBottom: theme.spacing.xl,
  },
  sectionHeader: {
    fontSize: theme.typography.fontSizes.lg,
    fontWeight: '600',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.md,
    marginTop: theme.spacing.sm,
  },
  card: {
    backgroundColor: theme.colors.background.primary,
    borderRadius: theme.borderRadius.md,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.md,
  },
  settingIcon: {
    width: 36,
    height: 36,
    borderRadius: theme.borderRadius.md,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: theme.spacing.md,
  },
  iconText: {
    fontSize: 20,
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '500',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.xs,
  },
  settingDescription: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
  },
  settingArrow: {
    fontSize: 20,
    color: theme.colors.gray[400],
    marginLeft: theme.spacing.sm,
  },
  divider: {
    height: 1,
    backgroundColor: theme.colors.gray[100],
    marginLeft: 68,
  },
});

export default SettingsScreen; 