import axios from '../lib/axios';

// Enums (match backend exactly)
export enum BudgetStatus {
  ACTIVE = 'active',
  ARCHIVED = 'archived',
  ENDED = 'ended'
}

// DTOs (match backend DTOs exactly)
export interface CreateBudgetDto {
  categoryId: string;
  month: number; // 1-12
  year: number;
  amount: number; // Budget amount in smallest currency unit
}

export interface UpdateBudgetDto {
  amount?: number; // Updated budget amount
  status?: BudgetStatus; // Budget status
}

export interface BudgetQueryDto {
  month?: number; // Filter by month (1-12)
  year?: number; // Filter by year
  categoryId?: string; // Filter by category ID
  status?: BudgetStatus; // Filter by status
}

// Response DTOs (match backend response structure)
export interface BudgetOverviewDto {
  categoryId: string; // Category ID
  categoryName: string; // Category name
  budgetAmount: number; // Total budget amount
  spentAmount: number; // Amount already spent
  remainingAmount: number; // Remaining budget amount
  progressPercent: number; // Progress percentage (0-100)
  warning: boolean; // Warning flag if spending is over 80%
}

export interface TransactionSummaryDto {
  id: string; // Transaction ID
  amount: number; // Transaction amount
  transaction_date: Date; // Transaction date
  description?: string; // Transaction description
}

export interface BudgetCategoryReportDto {
  categoryId: string; // Category ID
  categoryName: string; // Category name
  budgetAmount: number; // Budget amount for this category
  spentAmount: number; // Amount spent in this category
  remainingAmount: number; // Remaining amount in this category
  transactions: TransactionSummaryDto[]; // List of transactions in this category
}

export interface BudgetReportDto {
  totalBudget: number; // Total budget for the month
  totalSpent: number; // Total amount spent
  totalRemaining: number; // Total remaining budget
  categories: BudgetCategoryReportDto[]; // Budget breakdown by category
  generatedAt: Date; // Report generation date
}

export interface ExportBudgetReportDto {
  month: number; // Month for the report (1-12)
  year: number; // Year for the report
  format?: 'csv' | 'pdf'; // Export format
}

// Budget Entity (match backend Budget entity)
export interface IBudget {
  id: string; // Budget ID
  user_id: string; // User ID who owns this budget
  category_id: string; // Category ID for this budget
  month: number; // Month (1-12)
  year: number; // Year
  amount: number; // Budget amount
  status: BudgetStatus; // Budget status (active, archived, ended)
  is_active: boolean; // Soft delete flag
  created_at: Date; // Creation timestamp
  updated_at: Date; // Last update timestamp
  category?: {
    id: string;
    name: string;
    type: string; // 'income' | 'expense'
    status?: string; // 'active' | 'inactive'
  };
}

const BUDGETS_ENDPOINT = '/budgets';

export const BudgetAPI = {
  // Debug endpoint to test backend connectivity
  debugRoutes: async () => {
    const response = await axios.get(`${BUDGETS_ENDPOINT}/debug/routes`);
    return response.data;
  },

  // Create a new budget
  create: async (data: CreateBudgetDto): Promise<IBudget> => {
    const response = await axios.post(BUDGETS_ENDPOINT, data);

    // Backend returns: { data: Budget }
    return response.data.data || response.data;
  },

  // Get budgets with optional filtering
  getAll: async (query?: BudgetQueryDto): Promise<IBudget[]> => {
    const response = await axios.get(BUDGETS_ENDPOINT, { params: query });

    // Backend returns: { data: Budget[] }
    return response.data.data || response.data || [];
  },

  // Get budget overview for a specific month/year
  getOverview: async (month: number, year: number): Promise<BudgetOverviewDto[]> => {

    // Ensure month and year are numbers (backend expects integers)
    const params = {
      month: Number(month),
      year: Number(year)
    };

    const response = await axios.get(`${BUDGETS_ENDPOINT}/overview`, { params });

    // Backend returns: { data: BudgetOverviewDto[] }
    return response.data.data || response.data || [];
  },

  // Get detailed budget report
  getReport: async (month: number, year: number): Promise<BudgetReportDto> => {
    const response = await axios.get(`${BUDGETS_ENDPOINT}/report`, {
      params: { month: Number(month), year: Number(year) }
    });

    // Backend returns: { data: BudgetReportDto }
    return response.data.data || response.data;
  },

  // Get budget by ID
  getById: async (id: string): Promise<IBudget> => {
    const response = await axios.get(`${BUDGETS_ENDPOINT}/${id}`);

    // Backend returns: { data: Budget }
    return response.data.data || response.data;
  },

  // Update budget
  update: async (id: string, data: UpdateBudgetDto): Promise<IBudget> => {
    const response = await axios.patch(`${BUDGETS_ENDPOINT}/${id}`, data);

    // Backend returns: { data: Budget }
    return response.data.data || response.data;
  },

  // Delete budget (soft delete)
  delete: async (id: string): Promise<void> => {
    await axios.delete(`${BUDGETS_ENDPOINT}/${id}`);
  },

  // Export budget report
  exportBudgetReport: async (data: ExportBudgetReportDto): Promise<{ blob: Blob; filename: string }> => {
    console.log('🌐 Making export API request:', data);

    const response = await axios.post(`${BUDGETS_ENDPOINT}/export`, data, {
      responseType: 'blob',
      headers: {
        'Accept': data.format === 'pdf' ? 'application/pdf' : 'text/csv',
      },
    });

    console.log('📡 Export API Response:');
    console.log('- Status:', response.status);
    console.log('- Headers:', response.headers);
    console.log('- Data type:', typeof response.data);
    console.log('- Data size:', response.data?.size || 'unknown');

    // Extract filename from Content-Disposition header
    const contentDisposition = response.headers['content-disposition'];
    let filename = `budget-report-${data.year}-${data.month.toString().padStart(2, '0')}.${data.format}`;

    console.log('📋 Content-Disposition header:', contentDisposition);

    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename="(.+)"/);
      if (filenameMatch) {
        filename = filenameMatch[1];
        console.log('✅ Extracted filename from header:', filename);
      }
    } else {
      console.log('⚠️ Using default filename:', filename);
    }

    return {
      blob: response.data,
      filename,
    };
  }
};
