export enum CategoryType {
    INCOME = 'income',
    EXPENSE = 'expense',
}

export enum CategoryStatus {
    ACTIVE = 'active',
    INACTIVE = 'inactive',
}

export interface Category {
    id: string;
    name: string;
    type: CategoryType;
    status: CategoryStatus;
    user_id: string;
    icon?: string;
    description?: string;
    created_at: string;
    updated_at: string;
}

export interface FindAllCategoryDto {
    page?: number;
    limit?: number;
    status?: CategoryStatus;
    type?: CategoryType;
}

export interface CreateCategoryDto {
    name: string;
    type: CategoryType;
}

export interface UpdateCategoryDto {
    name?: string;
    type?: CategoryType;
    status?: CategoryStatus;
}

export interface CategoriesResponse {
    data: Category[];
    total: number;
    page: number;
    limit: number;
}