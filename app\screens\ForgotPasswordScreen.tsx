import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { StatusBar } from 'expo-status-bar';
import { AuthStackParamList } from '../navigation/types';
import theme from '../theme';
import Screen from '../components/common/Screen';
import Button from '../components/ui/Button';
import Card from '../components/ui/Card';
import { AuthAPI, ForgotPasswordDto, VerifyOtpDto } from '../../src/api/auth.api';

type ForgotPasswordScreenNavigationProp = NativeStackNavigationProp<
  AuthStackParamList,
  'ForgotPassword'
>;

const ForgotPasswordScreen = () => {
  const navigation = useNavigation<ForgotPasswordScreenNavigationProp>();
  const [email, setEmail] = useState('');
  const [otp, setOtp] = useState('');
  const [loading, setLoading] = useState(false);
  const [step, setStep] = useState<'email' | 'otp'>('email');
  const [emailSent, setEmailSent] = useState(false);

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleSendOtp = async () => {
    if (!validateEmail(email)) {
      Alert.alert('Error', 'Please enter a valid email address');
      return;
    }

    try {
      setLoading(true);
      const forgotPasswordData: ForgotPasswordDto = { email };
      await AuthAPI.forgotPassword(forgotPasswordData);

      setEmailSent(true);
      setStep('otp');
      Alert.alert('Success', 'OTP has been sent to your email');
    } catch (error: any) {
      let errorMessage = 'Failed to send OTP. Please try again.';

      if (error.response?.status === 400) {
        errorMessage = 'Email does not exist in the system';
      } else if (error.response?.status >= 500) {
        errorMessage = 'Server error. Please try again later.';
      } else if (error.message) {
        errorMessage = error.message;
      }

      Alert.alert('Error', errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyOtp = async () => {
    if (!otp.trim()) {
      Alert.alert('Error', 'Please enter the OTP code');
      return;
    }

    if (otp.length !== 6) {
      Alert.alert('Error', 'OTP must be 6 digits');
      return;
    }

    try {
      setLoading(true);
      const verifyOtpData: VerifyOtpDto = { email, otp };
      await AuthAPI.verifyOtp(verifyOtpData);

      // Navigate to reset password screen with email and otp
      navigation.navigate('ResetPassword', { email, otp });
    } catch (error: any) {
      let errorMessage = 'Invalid OTP. Please try again.';

      if (error.response?.status === 400) {
        const message = error.response?.data?.message;
        if (message?.includes('expired')) {
          errorMessage = 'OTP has expired. Please request a new one.';
        } else if (message?.includes('Incorrect')) {
          errorMessage = 'Incorrect OTP code. Please check and try again.';
        }
      } else if (error.response?.status >= 500) {
        errorMessage = 'Server error. Please try again later.';
      } else if (error.message) {
        errorMessage = error.message;
      }

      Alert.alert('Error', errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const handleBackToLogin = () => {
    navigation.navigate('Login');
  };

  const renderEmailStep = () => (
    <>
      <Text style={styles.title}>Forgot Password?</Text>
      <Text style={styles.subtitle}>
        Enter your email address and we'll send you an OTP to reset your password.
      </Text>

      <Card style={styles.formContainer}>
        <TextInput
          style={styles.input}
          placeholder="Email"
          value={email}
          onChangeText={setEmail}
          keyboardType="email-address"
          autoCapitalize="none"
        />

        <Button
          title={loading ? 'Sending...' : 'Send OTP'}
          variant="primary"
          fullWidth
          onPress={handleSendOtp}
          disabled={!email || loading}
          style={[!email ? styles.disabledButton : null]}
        />

        <Button
          title="Back to Login"
          variant="outline"
          fullWidth
          onPress={handleBackToLogin}
          style={styles.backButton}
        />
      </Card>
    </>
  );

  const renderOtpStep = () => (
    <>
      <Text style={styles.title}>Enter OTP</Text>
      <Text style={styles.subtitle}>
        We've sent a 6-digit code to {email}. Please enter it below.
      </Text>

      <Card style={styles.formContainer}>
        <TextInput
          style={styles.input}
          placeholder="Enter 6-digit OTP"
          value={otp}
          onChangeText={setOtp}
          keyboardType="numeric"
          maxLength={6}
          autoCapitalize="none"
        />

        <Button
          title={loading ? 'Verifying...' : 'Verify OTP'}
          variant="primary"
          fullWidth
          onPress={handleVerifyOtp}
          disabled={!otp || otp.length !== 6 || loading}
          style={[(!otp || otp.length !== 6) ? styles.disabledButton : null]}
        />

        <Button
          title="Resend OTP"
          variant="outline"
          fullWidth
          onPress={handleSendOtp}
          disabled={loading}
          style={styles.backButton}
        />

        <Button
          title="Back to Email"
          variant="outline"
          fullWidth
          onPress={() => {
            setStep('email');
            setOtp('');
            setEmailSent(false);
          }}
          style={styles.backButton}
        />
      </Card>
    </>
  );



  return (
    <Screen>
      <StatusBar style="dark" />
      <KeyboardAwareScrollView
        style={styles.container}
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
        enableOnAndroid={true}
        enableAutomaticScroll={true}
        extraScrollHeight={20}
        extraHeight={100}
      >
          <TouchableOpacity
            style={styles.backIconButton}
            onPress={handleBackToLogin}
          >
            <Text style={styles.backIcon}>←</Text>
          </TouchableOpacity>

          {step === 'email' ? renderEmailStep() : renderOtpStep()}
      </KeyboardAwareScrollView>
    </Screen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    paddingHorizontal: theme.spacing.xl,
    paddingBottom: theme.spacing.xl,
  },
  backIconButton: {
    marginTop: theme.spacing.md,
    marginBottom: theme.spacing.xl,
    width: 40,
    height: 40,
    borderRadius: theme.borderRadius.full,
    backgroundColor: theme.colors.gray[100],
    justifyContent: 'center',
    alignItems: 'center',
  },
  backIcon: {
    fontSize: theme.typography.fontSizes.xl,
    color: theme.colors.text.primary,
  },
  title: {
    fontSize: theme.typography.fontSizes['2xl'],
    fontWeight: '700',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.sm,
  },
  subtitle: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.secondary,
    marginBottom: theme.spacing.xl,
  },
  formContainer: {
    width: '100%',
    padding: theme.spacing.md,
  },
  input: {
    backgroundColor: theme.colors.gray[100],
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.xl,
    fontSize: theme.typography.fontSizes.md,
  },
  disabledButton: {
    backgroundColor: theme.colors.gray[400],
  },
  backButton: {
    marginTop: theme.spacing.md,
  },
  confirmationIcon: {
    width: 80,
    height: 80,
    borderRadius: theme.borderRadius.full,
    backgroundColor: theme.colors.primary[100],
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
    marginBottom: theme.spacing.xl,
  },
  confirmationIconText: {
    fontSize: 40,
  },
  resendButton: {
    marginTop: theme.spacing.md,
  },
});

export default ForgotPasswordScreen;