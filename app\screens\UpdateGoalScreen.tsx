import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
  ActivityIndicator,
  TouchableOpacity,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../navigation/types';
import theme from '../theme';
import Screen from '../components/common/Screen';
import Button from '../components/ui/Button';
import TextInput from '../components/ui/TextInput';
import Card from '../components/ui/Card';
import { GoalsAPI, UpdateGoalDto, GoalStatus } from '../../src/api/goals.api';
import DateTimePicker from '@react-native-community/datetimepicker';
import STRINGS from '../constants/strings';

// type UpdateGoalScreenRouteProp = RouteProp<RootStackParamList, 'UpdateGoal'>;
type UpdateGoalScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;

const UpdateGoalScreen = () => {
  const navigation = useNavigation<UpdateGoalScreenNavigationProp>();
  const route = useRoute();
  const goalId = (route.params as any)?.id;

  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [name, setName] = useState('');
  const [targetAmount, setTargetAmount] = useState('');
  const [currentAmount, setCurrentAmount] = useState('');
  const [targetDate, setTargetDate] = useState(new Date());
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [status, setStatus] = useState<GoalStatus>(GoalStatus.ACTIVE);

  useEffect(() => {
    loadGoalDetails();
  }, []);

  const loadGoalDetails = async () => {
    try {
      const goalData = await GoalsAPI.getGoalById(goalId);
      setName(goalData.name);
      setTargetAmount(goalData.target_amount.toString());
      setCurrentAmount(goalData.current_amount.toString());
      setTargetDate(new Date(goalData.target_date));
      setStatus(goalData.status as GoalStatus);
    } catch (error: any) {
      // Determine error message based on error type
      let errorMessage = 'Failed to load goal details. Please try again.';

      if (error.code === 'NETWORK_ERROR' || error.message?.includes('timeout')) {
        errorMessage = 'Network connection failed. Please check your internet connection and try again.';
      } else if (error.response?.status === 401) {
        errorMessage = 'Authentication failed. Please login again.';
      } else if (error.response?.status === 404) {
        errorMessage = 'Goal not found. It may have been deleted.';
      } else if (error.response?.status >= 500) {
        errorMessage = 'Server error. Please try again later.';
      } else if (error.message) {
        errorMessage = error.message;
      }

      Alert.alert(
        'Error Loading Goal',
        errorMessage,
        [
          {
            text: 'Retry',
            onPress: () => loadGoalDetails()
          },
          {
            text: 'Go Back',
            style: 'cancel',
            onPress: () => navigation.goBack()
          }
        ]
      );
    } finally {
      setInitialLoading(false);
    }
  };

  const handleUpdate = async () => {
    if (!name || !targetAmount || !currentAmount) {
      Alert.alert(STRINGS.COMMON.ERROR, STRINGS.GOALS.FILL_ALL_FIELDS);
      return;
    }

    try {
      setLoading(true);
      const updateData: UpdateGoalDto = {
        name,
        target_amount: parseFloat(targetAmount.replace(/,/g, '')),
        current_amount: parseFloat(currentAmount.replace(/,/g, '')),
        target_date: targetDate.toISOString(),
        status,
      };
      await GoalsAPI.updateGoal(goalId, updateData);
      Alert.alert(STRINGS.COMMON.SUCCESS, STRINGS.GOALS.GOAL_UPDATED_SUCCESS);
      navigation.goBack();
    } catch (error: any) {
      // Determine error message based on error type
      let errorMessage = 'Failed to update goal. Please try again.';

      if (error.code === 'NETWORK_ERROR' || error.message?.includes('timeout')) {
        errorMessage = 'Network connection failed. Please check your internet connection and try again.';
      } else if (error.response?.status === 400) {
        errorMessage = 'Invalid goal data. Please check your information and try again.';
      } else if (error.response?.status === 401) {
        errorMessage = 'Authentication failed. Please login again.';
      } else if (error.response?.status === 404) {
        errorMessage = 'Goal not found. It may have been deleted.';
      } else if (error.response?.status >= 500) {
        errorMessage = 'Server error. Please try again later.';
      } else if (error.message) {
        errorMessage = error.message;
      }

      Alert.alert(
        'Update Failed',
        errorMessage,
        [{ text: 'OK', style: 'default' }]
      );
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    Alert.alert(
      STRINGS.GOALS.DELETE_GOAL,
      STRINGS.GOALS.DELETE_GOAL_CONFIRM,
      [
        { text: STRINGS.COMMON.CANCEL, style: 'cancel' },
        {
          text: STRINGS.COMMON.DELETE,
          style: 'destructive',
          onPress: async () => {
            try {
              setLoading(true);
              await GoalsAPI.deleteGoal(goalId);
              Alert.alert(STRINGS.COMMON.SUCCESS, STRINGS.GOALS.GOAL_DELETED_SUCCESS);
              // Navigate về tab Goals
              navigation.navigate('Main', { screen: 'Goals' });
            } catch (error: any) {
              // Determine error message based on error type
              let errorMessage = 'Failed to delete goal. Please try again.';

              if (error.code === 'NETWORK_ERROR' || error.message?.includes('timeout')) {
                errorMessage = 'Network connection failed. Please check your internet connection and try again.';
              } else if (error.response?.status === 401) {
                errorMessage = 'Authentication failed. Please login again.';
              } else if (error.response?.status === 404) {
                errorMessage = 'Goal not found. It may have already been deleted.';
              } else if (error.response?.status >= 500) {
                errorMessage = 'Server error. Please try again later.';
              } else if (error.message) {
                errorMessage = error.message;
              }

              Alert.alert(
                'Delete Failed',
                errorMessage,
                [{ text: 'OK', style: 'default' }]
              );
            } finally {
              setLoading(false);
            }
          },
        },
      ],
    );
  };

  const formatCurrency = (text: string) => {
    const number = text.replace(/[^0-9]/g, '');
    return number.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  };

  const getStatusText = (status: GoalStatus) => {
    const statusTexts: { [key in GoalStatus]: string } = {
      [GoalStatus.ACTIVE]: STRINGS.GOALS.ACTIVE,
      [GoalStatus.COMPLETED]: STRINGS.GOALS.COMPLETED,
      [GoalStatus.CANCELLED]: STRINGS.GOALS.CANCELLED,
      [GoalStatus.PAUSED]: STRINGS.GOALS.PAUSED,
      [GoalStatus.PENDING]: STRINGS.GOALS.PENDING,
    };
    return statusTexts[status] || STRINGS.GOALS.UNKNOWN;
  };

  const getStatusColor = (status: GoalStatus) => {
    const statusColors: { [key in GoalStatus]: string } = {
      [GoalStatus.ACTIVE]: theme.colors.primary[500],
      [GoalStatus.COMPLETED]: theme.colors.success,
      [GoalStatus.CANCELLED]: theme.colors.error,
      [GoalStatus.PAUSED]: theme.colors.warning || '#f59e0b',
      [GoalStatus.PENDING]: theme.colors.gray[500],
    };
    return statusColors[status] || theme.colors.gray[500];
  };

  if (initialLoading) {
    return (
      <Screen>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary[500]} />
        </View>
      </Screen>
    );
  }

  return (
    <Screen>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.container}
      >
        <ScrollView contentContainerStyle={styles.scrollContainer}>
          <Card style={styles.formCard}>
            <Text style={styles.label}>{STRINGS.GOALS.GOAL_NAME}</Text>
            <TextInput
              value={name}
              onChangeText={setName}
              placeholder={STRINGS.GOALS.ENTER_GOAL_NAME}
              style={styles.input}
            />

            <Text style={styles.label}>{STRINGS.GOALS.TARGET_AMOUNT}</Text>
            <TextInput
              value={targetAmount}
              onChangeText={(text) => setTargetAmount(formatCurrency(text))}
              placeholder="0"
              keyboardType="numeric"
              style={styles.input}
            />

            <Text style={styles.label}>{STRINGS.GOALS.CURRENT_AMOUNT}</Text>
            <TextInput
              value={currentAmount}
              onChangeText={(text) => setCurrentAmount(formatCurrency(text))}
              placeholder="0"
              keyboardType="numeric"
              style={styles.input}
            />

            <Text style={styles.label}>{STRINGS.GOALS.TARGET_DATE}</Text>
            <Button
              title={targetDate.toLocaleDateString('en-US')}
              onPress={() => setShowDatePicker(true)}
              variant="secondary"
              style={styles.dateButton}
            />

            {showDatePicker && (
              <DateTimePicker
                value={targetDate}
                mode="date"
                display={Platform.OS === 'ios' ? 'spinner' : 'default'}
                onChange={(event, selectedDate) => {
                  if (Platform.OS === 'android') {
                    setShowDatePicker(false);
                  }
                  if (event.type === 'set' && selectedDate) {
                    setTargetDate(selectedDate);
                    if (Platform.OS === 'ios') {
                      setShowDatePicker(false);
                    }
                  } else if (event.type === 'dismissed') {
                    setShowDatePicker(false);
                  }
                }}
                minimumDate={new Date()}
              />
            )}

            <Text style={styles.label}>{STRINGS.GOALS.STATUS}</Text>
            <View style={styles.statusContainer}>
              {Object.values(GoalStatus).map((statusOption) => (
                <TouchableOpacity
                  key={statusOption}
                  style={[
                    styles.statusOption,
                    { backgroundColor: status === statusOption ? getStatusColor(statusOption) : theme.colors.gray[100] }
                  ]}
                  onPress={() => setStatus(statusOption)}
                >
                  <Text style={[
                    styles.statusOptionText,
                    { color: status === statusOption ? theme.colors.background.primary : theme.colors.text.primary }
                  ]}>
                    {getStatusText(statusOption)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </Card>

          <View style={styles.buttonContainer}>
            <Button
              title={STRINGS.COMMON.UPDATE}
              onPress={handleUpdate}
              variant="primary"
              loading={loading}
              style={styles.updateButton}
            />
            <Button
              title={STRINGS.GOALS.DELETE_GOAL}
              onPress={handleDelete}
              variant="danger"
              loading={loading}
              style={styles.deleteButton}
            />
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </Screen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollContainer: {
    flexGrow: 1,
    padding: theme.spacing.md,
  },
  formCard: {
    padding: theme.spacing.md,
  },
  label: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '500',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.sm,
  },
  input: {
    backgroundColor: theme.colors.gray[100],
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.md,
    fontSize: theme.typography.fontSizes.md,
  },
  dateButton: {
    marginBottom: theme.spacing.md,
  },
  buttonContainer: {
    padding: theme.spacing.md,
    gap: theme.spacing.md,
  },
  updateButton: {
    marginBottom: 0,
  },
  deleteButton: {
    backgroundColor: theme.colors.error,
  },
  statusContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: theme.spacing.sm,
    marginBottom: theme.spacing.md,
  },
  statusOption: {
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.borderRadius.full,
    borderWidth: 1,
    borderColor: theme.colors.gray[200],
  },
  statusOptionText: {
    fontSize: theme.typography.fontSizes.sm,
    fontWeight: '500',
  },
});

export default UpdateGoalScreen;