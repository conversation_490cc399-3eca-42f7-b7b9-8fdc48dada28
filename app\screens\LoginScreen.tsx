import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  Alert,
} from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { AuthStackParamList, RootStackParamList } from '../navigation/types';

// Import theme and components
import theme from '../theme';
import Screen from '../components/common/Screen';
import Button from '../components/ui/Button';
import TextInput from '../components/ui/TextInput';
import Card from '../components/ui/Card';
import { GoogleIcon } from '../components/icons';
import { AuthAPI } from '../../src/api/auth.api';
import STRINGS from '../constants/strings';

// Get screen dimensions for responsive design
const { width, height } = Dimensions.get('window');

type LoginScreenNavigationProp = NativeStackNavigationProp<
  AuthStackParamList & RootStackParamList,
  'Login'
>;

const LoginScreen = () => {
  const navigation = useNavigation<LoginScreenNavigationProp>();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);
  const [activeSlide, setActiveSlide] = useState(0);
  const [loading, setLoading] = useState(false);

  // Features carousel data
  const features = [
    {
      id: 1,
      title: 'Easy Financial Management',
      description: 'Track expenses and save effectively anytime, anywhere',
      icon: '💰',
    },
    {
      id: 2,
      title: 'Set Financial Goals',
      description: 'Plan and achieve your personal financial objectives',
      icon: '🎯',
    },
    {
      id: 3,
      title: 'Expense Analysis',
      description: 'Visual charts help you understand your spending habits',
      icon: '📊',
    },
  ];

  const handleLogin = async () => {
    if (!email.trim()) {
      Alert.alert(STRINGS.COMMON.ERROR, 'Please enter your email');
      return;
    }

    if (!password.trim()) {
      Alert.alert(STRINGS.COMMON.ERROR, 'Please enter your password');
      return;
    }

    setLoading(true);
    try {
      const user = await AuthAPI.login({
        email: email.trim(),
        password: password.trim()
      });

      // Login successful, clear form and navigate
      setEmail('');
      setPassword('');
      navigation.navigate('Main', { screen: 'Home' });
    } catch (error: any) {
      // Determine error message based on error type
      let errorMessage = 'Login failed. Please try again.';

      if (error.code === 'NETWORK_ERROR' || error.message?.includes('timeout')) {
        errorMessage = 'Network connection failed. Please check your internet connection and try again.';
      } else if (error.response?.status === 400) {
        errorMessage = 'Invalid email or password. Please check your credentials.';
      } else if (error.response?.status === 401) {
        errorMessage = 'Invalid email or password. Please try again.';
      } else if (error.response?.status >= 500) {
        errorMessage = 'Server error. Please try again later.';
      } else if (error.message) {
        errorMessage = error.message;
      }

      Alert.alert(
        'Login Failed',
        errorMessage,
        [
          {
            text: 'OK',
            style: 'default'
          }
        ]
      );
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleLogin = () => {
    // To be implemented with Google OAuth
    navigation.navigate('Main', { screen: 'Home' });
  };

  const togglePasswordVisibility = () => {
    setIsPasswordVisible(!isPasswordVisible);
  };

  // Password visibility icon
  const passwordIcon = (
    <Text>{isPasswordVisible ? '👁️' : '👁️‍🗨️'}</Text>
  );

  return (
    <Screen>
      <KeyboardAwareScrollView
        style={styles.container}
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
        enableOnAndroid={true}
        enableAutomaticScroll={true}
        extraScrollHeight={20}
        extraHeight={100}
      >
          <View style={styles.logoContainer}>
            <Text style={styles.logoText}>FinanceApp</Text>
            <Text style={styles.logoSubtitle}>Smart Financial Management</Text>
          </View>

          {/* Features Carousel */}
          <ScrollView
            horizontal
            pagingEnabled
            showsHorizontalScrollIndicator={false}
            onScroll={e => {
              const slide = Math.round(e.nativeEvent.contentOffset.x / width);
              if (slide !== activeSlide) {
                setActiveSlide(slide);
              }
            }}
            scrollEventThrottle={16}
          >
            {features.map((feature, index) => (
              <View style={[styles.featureSlide, { width }]} key={feature.id}>
                <Text style={styles.featureIcon}>{feature.icon}</Text>
                <Text style={styles.featureTitle}>{feature.title}</Text>
                <Text style={styles.featureDescription}>{feature.description}</Text>
              </View>
            ))}
          </ScrollView>

          {/* Pagination dots */}
          <View style={styles.paginationContainer}>
            {features.map((_, index) => (
              <View
                key={index}
                style={[
                  styles.paginationDot,
                  { opacity: index === activeSlide ? 1 : 0.4 },
                ]}
              />
            ))}
          </View>

          <Card style={styles.formContainer} variant="flat">
            <TextInput
              placeholder="Email or phone number"
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
            />


            <TextInput
              placeholder="Password"
              value={password}
              onChangeText={setPassword}
              secureTextEntry={!isPasswordVisible}
              icon={passwordIcon}
              iconPosition="right"
              onIconPress={togglePasswordVisibility}
            />

            <TouchableOpacity
              style={styles.forgotPasswordContainer}
              onPress={() => navigation.navigate('ForgotPassword')}
            >
              <Text style={styles.forgotPasswordText}>Forgot Password?</Text>
            </TouchableOpacity>

            <Button
              title={STRINGS.AUTH.LOGIN}
              onPress={handleLogin}
              variant="primary"
              fullWidth
              loading={loading}
            />

            <View style={styles.dividerContainer}>
              <View style={styles.divider} />
              <Text style={styles.dividerText}>Or</Text>
              <View style={styles.divider} />
            </View>

            <TouchableOpacity
              style={styles.googleButton}
              onPress={handleGoogleLogin}
              activeOpacity={0.8}
            >
              <GoogleIcon width={20} height={20} />
              <Text style={styles.googleButtonText}>Continue with Google</Text>
            </TouchableOpacity>

            <View style={styles.registerContainer}>
              <Text style={styles.registerText}>Don't have an account? </Text>
              <TouchableOpacity onPress={() => navigation.navigate('Register')}>
                <Text style={styles.registerLink}>Register now</Text>
              </TouchableOpacity>
            </View>
          </Card>
      </KeyboardAwareScrollView>
    </Screen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    paddingBottom: theme.spacing['2xl'],
  },
  logoContainer: {
    alignItems: 'center',
    marginTop: height * 0.06,
    marginBottom: theme.spacing.lg,
  },
  logoText: {
    fontSize: theme.typography.fontSizes['3xl'],
    fontWeight: 'bold',
    color: theme.colors.primary[500],
    letterSpacing: 0.5,
  },
  logoSubtitle: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
    marginTop: theme.spacing.xs,
    fontStyle: 'italic',
  },
  featureSlide: {
    alignItems: 'center',
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.sm,
  },
  featureIcon: {
    fontSize: 48,
    marginBottom: theme.spacing.sm,
  },
  featureTitle: {
    fontSize: theme.typography.fontSizes.lg,
    fontWeight: '600',
    color: theme.colors.text.primary,
    textAlign: 'center',
    marginBottom: theme.spacing.xs,
  },
  featureDescription: {
    fontSize: theme.typography.fontSizes.sm,
    color: theme.colors.text.secondary,
    textAlign: 'center',
    paddingHorizontal: theme.spacing.lg,
  },
  paginationContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginVertical: theme.spacing.md,
  },
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: theme.borderRadius.full,
    backgroundColor: theme.colors.primary[500],
    marginHorizontal: 4,
  },
  formContainer: {
    paddingHorizontal: theme.spacing.lg,
    width: '100%',
    backgroundColor: theme.colors.background.primary,
  },
  forgotPasswordContainer: {
    alignSelf: 'flex-end',
    marginBottom: theme.spacing.lg,
  },
  forgotPasswordText: {
    color: theme.colors.text.link,
    fontSize: theme.typography.fontSizes.sm,
    fontWeight: '500',
  },
  dividerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: theme.spacing.md,
  },
  divider: {
    flex: 1,
    height: 1,
    backgroundColor: theme.colors.gray[200],
  },
  dividerText: {
    color: theme.colors.text.secondary,
    paddingHorizontal: theme.spacing.md,
    fontSize: theme.typography.fontSizes.sm,
  },
  registerContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: theme.spacing.md,
  },
  registerText: {
    color: theme.colors.text.secondary,
    fontSize: theme.typography.fontSizes.sm,
  },
  registerLink: {
    color: theme.colors.text.link,
    fontSize: theme.typography.fontSizes.sm,
    fontWeight: '500'
  },
  googleButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.colors.background.primary,
    borderWidth: 1,
    borderColor: theme.colors.gray[300],
    borderRadius: theme.borderRadius.lg,
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.lg,
    marginBottom: theme.spacing.sm,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  googleButtonText: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: '500',
    color: theme.colors.text.primary,
    marginLeft: theme.spacing.sm,
  },
});

export default LoginScreen;