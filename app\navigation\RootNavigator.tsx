import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { RootStackParamList, AuthStackParamList } from './types';

import DrawerNavigator from './DrawerNavigator';
import AuthNavigator from './AuthNavigator';

// Placeholder imports for new screens (to be created)
import GoalSetupScreen from '../screens/GoalSetupScreen';
import InitialBalanceScreen from '../screens/InitialBalanceScreen';

import CreateTransactionScreen from '../screens/CreateTransactionScreen';
import EditTransactionScreen from '../screens/EditTransactionScreen';
import TransactionDetailScreen from '../screens/TransactionDetailScreen';
import TransactionListScreen from '../screens/TransactionListScreen';
import TransactionStatisticsScreen from '../screens/TransactionStatisticsScreen';
import BudgetStatisticsScreen from '../screens/BudgetStatisticsScreen';
import GoalDetailScreen from '../screens/GoalDetailScreen';
import CategoryDetailScreen from '../screens/CategoryDetailScreen';
import BudgetDetailScreen from '../screens/BudgetDetailScreen';
import SetupBudgetScreen from '../screens/SetupBudgetScreen';
import CreateBudgetScreen from '../screens/CreateBudgetScreen';
import EditBudgetScreen from '../screens/EditBudgetScreen';
import BudgetReportScreen from '../screens/BudgetReportScreen';
import AddGoalScreen from '../screens/AddGoalScreen';
import AddCategoryScreen from '../screens/AddCategoryScreen';
import EditCategoryScreen from '../screens/EditCategoryScreen';
import ChartDetailScreen from '../screens/ChartDetailScreen';
import InitialGoalSetupScreen from '../screens/InitialGoalSetupScreen';
import UpdateGoalScreen from '../screens/UpdateGoalScreen';

const Stack = createNativeStackNavigator<RootStackParamList>();

const RootNavigator = () => {
  return (
    <NavigationContainer>
      <Stack.Navigator
        initialRouteName="Auth"
        screenOptions={{
          headerShown: false,
          contentStyle: {
            backgroundColor: '#FFFFFF',
          },
          animation: 'slide_from_right',
          headerShadowVisible: false,
        }}
      >
        <Stack.Screen name="Auth" component={AuthNavigator} />
        <Stack.Screen name="Main" component={DrawerNavigator} />

        {/* Initial Setup Screens */}
        <Stack.Screen
          name="InitialGoalSetup"
          component={InitialGoalSetupScreen}
          options={{
            headerShown: true,
            title: 'Initial Goal Setup',
            headerTitleStyle: {
              fontWeight: '600',
              fontSize: 18,
              color: '#111827',
            },
            headerTintColor: '#0EA5E9',
          }}
        />

        {/* Goal Setup Screens */}
        <Stack.Screen
          name="GoalSetup"
          component={GoalSetupScreen}
          options={{
            headerShown: true,
            title: 'Goal Setup',
            headerTitleStyle: {
              fontWeight: '600',
              fontSize: 18,
              color: '#111827',
            },
            headerTintColor: '#0EA5E9',
          }}
        />

        <Stack.Screen
          name="InitialBalance"
          component={InitialBalanceScreen}
          options={{
            headerShown: true,
            title: 'Initial Balance',
            headerTitleStyle: {
              fontWeight: '600',
              fontSize: 18,
              color: '#111827',
            },
            headerTintColor: '#0EA5E9',
          }}
        />

        {/* Transaction Screens */}
        <Stack.Screen
          name="CreateTransaction"
          component={CreateTransactionScreen}
          options={{
            headerShown: true,
            title: 'Add Transaction',
            headerTitleStyle: {
              fontWeight: '600',
              fontSize: 18,
              color: '#111827',
            },
            headerTintColor: '#0EA5E9',
          }}
        />

        <Stack.Screen
          name="EditTransaction"
          component={EditTransactionScreen}
          options={{
            headerShown: true,
            title: 'Edit Transaction',
            headerTitleStyle: {
              fontWeight: '600',
              fontSize: 18,
              color: '#111827',
            },
            headerTintColor: '#0EA5E9',
          }}
        />

        <Stack.Screen
          name="TransactionDetail"
          component={TransactionDetailScreen}
          options={{
            headerShown: true,
            title: 'Transaction Detail',
            headerTitleStyle: {
              fontWeight: '600',
              fontSize: 18,
              color: '#111827',
            },
            headerTintColor: '#0EA5E9',
          }}
        />

        <Stack.Screen
          name="TransactionList"
          component={TransactionListScreen}
          options={{
            headerShown: true,
            title: 'Transaction List',
            headerTitleStyle: {
              fontWeight: '600',
              fontSize: 18,
              color: '#111827',
            },
            headerTintColor: '#0EA5E9',
          }}
        />

        <Stack.Screen
          name="TransactionStatistics"
          component={TransactionStatisticsScreen}
          options={{
            headerShown: true,
            title: 'Transaction Statistics',
            headerTitleStyle: {
              fontWeight: '600',
              fontSize: 18,
              color: '#111827',
            },
            headerTintColor: '#0EA5E9',
          }}
        />

        <Stack.Screen
          name="BudgetStatistics"
          component={BudgetStatisticsScreen}
          options={{
            headerShown: true,
            title: 'Budget Statistics',
            headerTitleStyle: {
              fontWeight: '600',
              fontSize: 18,
              color: '#111827',
            },
            headerTintColor: '#0EA5E9',
          }}
        />

        {/* Goal Screens */}
        <Stack.Screen
          name="GoalDetail"
          component={GoalDetailScreen}
          options={{
            headerShown: true,
            title: 'Goal Detail',
            headerTitleStyle: {
              fontWeight: '600',
              fontSize: 18,
              color: '#111827',
            },
            headerTintColor: '#0EA5E9',
          }}
        />

        <Stack.Screen
          name="UpdateGoal"
          component={UpdateGoalScreen}
          options={{
            headerShown: true,
            title: 'Update Goal',
            headerTitleStyle: {
              fontWeight: '600',
              fontSize: 18,
              color: '#111827',
            },
            headerTintColor: '#0EA5E9',
          }}
        />

        <Stack.Screen
          name="AddGoal"
          component={AddGoalScreen}
          options={{
            headerShown: true,
            title: 'Add Goal',
            headerTitleStyle: {
              fontWeight: '600',
              fontSize: 18,
              color: '#111827',
            },
            headerTintColor: '#0EA5E9',
          }}
        />

        {/* Category Screens */}
        <Stack.Screen
          name="CategoryDetail"
          component={CategoryDetailScreen}
          options={{
            headerShown: true,
            title: 'Category Detail',
            headerTitleStyle: {
              fontWeight: '600',
              fontSize: 18,
              color: '#111827',
            },
            headerTintColor: '#0EA5E9',
          }}
        />

        <Stack.Screen
          name="AddCategory"
          component={AddCategoryScreen}
          options={{
            headerShown: true,
            title: 'Add Category',
            headerTitleStyle: {
              fontWeight: '600',
              fontSize: 18,
              color: '#111827',
            },
            headerTintColor: '#0EA5E9',
          }}
        />

        <Stack.Screen
          name="EditCategory"
          component={EditCategoryScreen}
          options={{
            headerShown: true,
            title: 'Edit Category',
            headerTitleStyle: {
              fontWeight: '600',
              fontSize: 18,
              color: '#111827',
            },
            headerTintColor: '#0EA5E9',
          }}
        />

        {/* Budget Screens */}
        <Stack.Screen
          name="BudgetDetail"
          component={BudgetDetailScreen}
          options={{
            headerShown: true,
            title: 'Budget Detail',
            headerTitleStyle: {
              fontWeight: '600',
              fontSize: 18,
              color: '#111827',
            },
            headerTintColor: '#0EA5E9',
          }}
        />

        <Stack.Screen
          name="SetupBudget"
          component={SetupBudgetScreen}
          options={{
            headerShown: true,
            title: 'Setup Budget',
            headerTitleStyle: {
              fontWeight: '600',
              fontSize: 18,
              color: '#111827',
            },
            headerTintColor: '#0EA5E9',
          }}
        />

        <Stack.Screen
          name="CreateBudget"
          component={CreateBudgetScreen}
          options={{
            headerShown: true,
            title: 'Create Budget',
            headerTitleStyle: {
              fontWeight: '600',
              fontSize: 18,
              color: '#111827',
            },
            headerTintColor: '#0EA5E9',
          }}
        />

        <Stack.Screen
          name="EditBudget"
          component={EditBudgetScreen}
          options={{
            headerShown: true,
            title: 'Edit Budget',
            headerTitleStyle: {
              fontWeight: '600',
              fontSize: 18,
              color: '#111827',
            },
            headerTintColor: '#0EA5E9',
          }}
        />

        <Stack.Screen
          name="BudgetReport"
          component={BudgetReportScreen}
          options={{
            headerShown: true,
            title: 'Budget Report',
            headerTitleStyle: {
              fontWeight: '600',
              fontSize: 18,
              color: '#111827',
            },
            headerTintColor: '#0EA5E9',
          }}
        />

        {/* Analytics Screens */}
        <Stack.Screen
          name="ChartDetail"
          component={ChartDetailScreen}
          options={{
            headerShown: true,
            title: 'Chart Detail',
            headerTitleStyle: {
              fontWeight: '600',
              fontSize: 18,
              color: '#111827',
            },
            headerTintColor: '#0EA5E9',
          }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default RootNavigator;