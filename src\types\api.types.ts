// Standard API response wrapper
export interface ApiResponse<T> {
  status: string;
  statusCode: number;
  message: string;
  data: T;
  meta?: any;
  timestamp: string;
}

// Error response structure
export interface ApiError {
  status: string;
  statusCode: number;
  message: string;
  error?: string;
  timestamp: string;
}

// Pagination metadata
export interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// Paginated response
export interface PaginatedResponse<T> {
  status: string;
  statusCode: number;
  message: string;
  data: T[];
  meta: PaginationMeta;
  timestamp: string;
}
