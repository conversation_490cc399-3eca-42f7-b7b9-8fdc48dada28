import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Alert,
  ActivityIndicator,
} from 'react-native';
import * as ImagePickerExpo from 'expo-image-picker';
import theme from '../../theme';
import Card from './Card';
import { UploadAPI } from '../../../src/api/upload.api';


export interface ImagePickerProps {
  label?: string;
  onImageSelected?: (imageUrl: string) => void;
  onImageRemoved?: () => void;
  value?: string;
  containerStyle?: any;
  disabled?: boolean;
  placeholder?: string;
}

const ImagePicker: React.FC<ImagePickerProps> = ({
  label,
  onImageSelected,
  onImageRemoved,
  value,
  containerStyle,
  disabled = false,
  placeholder = 'Tap to add transaction image',
}) => {
  const [uploading, setUploading] = useState(false);

  const requestPermissions = async () => {
    const { status } = await ImagePickerExpo.requestMediaLibraryPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert(
        'Permission Required',
        'Sorry, we need camera roll permissions to upload images.',
        [{ text: 'OK' }]
      );
      return false;
    }
    return true;
  };

  const pickImageFromLibrary = async () => {
    console.log('pickImageFromLibrary called');

    console.log('Requesting permissions...');
    const hasPermission = await requestPermissions();
    console.log('Permission result:', hasPermission);
    if (!hasPermission) return;

    try {
      console.log('Launching image library...');

      // Add timeout to catch hanging promises
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Image picker timeout')), 10000);
      });

      const imagePickerPromise = ImagePickerExpo.launchImageLibraryAsync({
        mediaTypes: 'images',
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.3, // Much lower quality for server compatibility
      });

      const result = await Promise.race([imagePickerPromise, timeoutPromise]) as ImagePickerExpo.ImagePickerResult;
      console.log('Image library result:', result);

      if (!result.canceled && result.assets && result.assets[0]) {
        console.log('Selected asset:', result.assets[0]);
        await uploadImage(result.assets[0]);
      } else {
        console.log('Image picker was canceled or no assets');
      }
    } catch (error: any) {
      console.error('Error picking image:', error);
      Alert.alert('Error', `Failed to pick image: ${error.message || 'Unknown error'}`);
    }
  };

  const takePhoto = async () => {
    console.log('takePhoto called');

    console.log('Requesting camera permissions...');
    const { status } = await ImagePickerExpo.requestCameraPermissionsAsync();
    console.log('Camera permission status:', status);
    if (status !== 'granted') {
      Alert.alert(
        'Permission Required',
        'Sorry, we need camera permissions to take photos.',
        [{ text: 'OK' }]
      );
      return;
    }

    try {
      console.log('Launching camera...');

      // Add timeout to catch hanging promises
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Camera timeout')), 10000);
      });

      const cameraPromise = ImagePickerExpo.launchCameraAsync({
        mediaTypes: 'images',
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.3, // Much lower quality for server compatibility
      });

      const result = await Promise.race([cameraPromise, timeoutPromise]) as ImagePickerExpo.ImagePickerResult;
      console.log('Camera result:', result);

      if (!result.canceled && result.assets && result.assets[0]) {
        console.log('Selected camera asset:', result.assets[0]);
        await uploadImage(result.assets[0]);
      } else {
        console.log('Camera was canceled or no assets');
      }
    } catch (error: any) {
      console.error('Error taking photo:', error);
      Alert.alert('Error', `Failed to take photo: ${error.message || 'Unknown error'}`);
    }
  };

  const uploadImage = async (asset: ImagePickerExpo.ImagePickerAsset) => {
    try {
      setUploading(true);

      // Check file size (limit to 1MB for server compatibility)
      const maxSize = 1024 * 1024; // 1MB
      if (asset.fileSize && asset.fileSize > maxSize) {
        Alert.alert(
          'File Too Large',
          `Image size (${(asset.fileSize / 1024 / 1024).toFixed(1)}MB) exceeds 1MB limit. Please choose a smaller image or reduce quality.`
        );
        return;
      }

      // Use the imported UploadAPI

      const file = {
        uri: asset.uri,
        type: asset.type || 'image/jpeg',
        name: asset.fileName || `image_${Date.now()}.jpg`,
      };

      console.log(`Uploading image: ${(asset.fileSize || 0) / 1024}KB`);
      const response = await UploadAPI.uploadSingle(file);

      if (onImageSelected) {
        onImageSelected(response.url);
      }
    } catch (error: any) {
      console.error('Error uploading image:', error);

      let errorMessage = 'Failed to upload image';
      if (error.response?.status === 413) {
        errorMessage = 'Image file is too large for the server. Please choose a smaller image.';
      } else if (error.code === 'ECONNABORTED') {
        errorMessage = 'Upload timeout. Please check your connection and try again.';
      } else if (error.message) {
        errorMessage = error.message;
      }

      Alert.alert('Upload Error', errorMessage);
    } finally {
      setUploading(false);
    }
  };

  const removeImage = () => {
    Alert.alert(
      'Remove Image',
      'Are you sure you want to remove this image?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: () => {
            if (onImageRemoved) {
              onImageRemoved();
            }
          },
        },
      ]
    );
  };

  const showImageOptions = () => {
    if (disabled) return;

    // Show ActionSheet instead of Modal
    Alert.alert(
      'Add Image',
      'Choose an option',
      [
        {
          text: 'Take Photo',
          onPress: takePhoto,
        },
        {
          text: 'Choose from Library',
          onPress: pickImageFromLibrary,
        },
        {
          text: 'Cancel',
          style: 'cancel',
        },
      ]
    );
  };

  return (
    <Card style={[styles.container, containerStyle]}>
      {label && <Text style={styles.label}>{label}</Text>}
      
      <TouchableOpacity
        style={[
          styles.imageContainer,
          disabled && styles.disabled,
          value && value.trim() && styles.hasImage,
        ]}
        onPress={value && value.trim() ? removeImage : showImageOptions}
        disabled={disabled || uploading}
      >
        {uploading ? (
          <View style={styles.uploadingContainer}>
            <ActivityIndicator size="large" color={theme.colors.primary[500]} />
            <Text style={styles.uploadingText}>Uploading...</Text>
          </View>
        ) : value && value.trim() ? (
          <View style={styles.imageWrapper}>
            <Image source={{ uri: value }} style={styles.image} />
            <View style={styles.removeButton}>
              <Text style={styles.removeButtonText}>✕</Text>
            </View>
          </View>
        ) : (
          <View style={styles.placeholderContainer}>
            <Text style={styles.placeholderIcon}>📷</Text>
            <Text style={styles.placeholderText}>{placeholder}</Text>
          </View>
        )}
      </TouchableOpacity>


    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: theme.spacing.md,
  },
  label: {
    fontSize: theme.typography.fontSizes.md,
    fontWeight: theme.typography.fontWeights.semibold,
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.sm,
  },
  imageContainer: {
    height: 200,
    borderRadius: theme.borderRadius.lg,
    backgroundColor: theme.colors.gray[50],
    borderWidth: 2,
    borderColor: theme.colors.gray[200],
    borderStyle: 'dashed',
    overflow: 'hidden',
  },
  disabled: {
    opacity: 0.5,
  },
  hasImage: {
    borderStyle: 'solid',
    borderColor: theme.colors.primary[300],
  },
  uploadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: theme.spacing.sm,
  },
  uploadingText: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.secondary,
  },
  imageWrapper: {
    flex: 1,
    position: 'relative',
  },
  image: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  removeButton: {
    position: 'absolute',
    top: theme.spacing.sm,
    right: theme.spacing.sm,
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  removeButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  placeholderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    gap: theme.spacing.sm,
  },
  placeholderIcon: {
    fontSize: 48,
  },
  placeholderText: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.secondary,
    textAlign: 'center',
  },
});

export default ImagePicker;
