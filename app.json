{"expo": {"name": "expo-app", "slug": "expo-app", "version": "1.0.0", "scheme": "expoapp", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": true, "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "infoPlist": {"NSCameraUsageDescription": "This app needs access to camera to take photos for transactions.", "NSPhotoLibraryUsageDescription": "This app needs access to photo library to select images for transactions."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.anonymous.expoapp", "permissions": ["android.permission.CAMERA", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE"]}, "web": {"favicon": "./assets/favicon.png"}, "plugins": [["expo-image-picker", {"photosPermission": "The app accesses your photos to let you share them.", "cameraPermission": "The app accesses your camera to let you take photos."}]], "extra": {"eas": {"projectId": "cd2716b9-a518-4309-948f-636cddd157af"}}}}