import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Image,
  Modal,
} from 'react-native';
import { useNavigation, useRoute, RouteProp, useFocusEffect } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { StatusBar } from 'expo-status-bar';
import { RootStackParamList } from '../navigation/types';
import { Transaction, TransactionType } from '../types/transaction';
import { TransactionAPI } from '../../src/api/transaction.api';
import theme from '../theme';
import Screen from '../components/common/Screen';
import Card from '../components/ui/Card';
import Button from '../components/ui/Button';

type TransactionDetailScreenRouteProp = RouteProp<
  RootStackParamList,
  'TransactionDetail'
>;

type TransactionDetailScreenNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'TransactionDetail'
>;

const TransactionDetailScreen = () => {
  const navigation = useNavigation<TransactionDetailScreenNavigationProp>();
  const route = useRoute<TransactionDetailScreenRouteProp>();
  const { id } = route.params;
  
  const [transaction, setTransaction] = useState<Transaction | null>(null);
  const [loading, setLoading] = useState(true);
  const [deleting, setDeleting] = useState(false);
  const [showImageModal, setShowImageModal] = useState(false);

  useFocusEffect(
    React.useCallback(() => {
      loadTransaction();
    }, [id])
  );

  const loadTransaction = async () => {
    try {
      setLoading(true);
      const transactionData = await TransactionAPI.getById(id);
      setTransaction(transactionData);
    } catch (error) {
      console.error('Error loading transaction:', error);
      Alert.alert('Error', 'Unable to load transaction details');
      navigation.goBack();
    } finally {
      setLoading(false);
    }
  };
  
  const formatCurrency = (amount: number) => {
    return amount.toLocaleString('en-US') + ' VND';
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('en-US');
  };

  const getInputMethodText = (method: string) => {
    switch (method) {
      case 'manual': return 'Manual';
      case 'voice': return 'Voice';
      case 'chat': return 'Chat';
      case 'image': return 'Image';
      default: return 'Unknown';
    }
  };
  
  const handleEditTransaction = () => {
    navigation.navigate('EditTransaction', { id });
  };
  
  const handleDeleteTransaction = () => {
    Alert.alert(
      'Confirm Delete',
      'Are you sure you want to delete this transaction? This action cannot be undone.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: confirmDelete,
        },
      ]
    );
  };
  
  const confirmDelete = async () => {
    try {
      setDeleting(true);
      await TransactionAPI.delete(id);
      
      Alert.alert(
        'Success',
        'Transaction has been deleted',
        [
          {
            text: 'OK',
            onPress: () => navigation.goBack(),
          },
        ]
      );
    } catch (error) {
      console.error('Error deleting transaction:', error);
      Alert.alert('Error', 'Unable to delete transaction. Please try again.');
    } finally {
      setDeleting(false);
    }
  };

  const renderLoadingState = () => (
    <View style={styles.loadingContainer}>
      <ActivityIndicator size="large" color={theme.colors.primary[500]} />
      <Text style={styles.loadingText}>Loading...</Text>
    </View>
  );

  const renderTransactionHeader = () => {
    if (!transaction) return null;

    return (
      <Card style={[
        styles.headerCard,
        transaction.type === TransactionType.INCOME ? styles.incomeHeader : styles.expenseHeader
      ]}>
        <View style={styles.headerContent}>
          <View style={styles.iconContainer}>
            <Text style={styles.iconText}>
              {transaction.category?.icon || (transaction.type === TransactionType.INCOME ? '💰' : '💸')}
            </Text>
          </View>
          
          <View style={styles.headerInfo}>
            <Text style={styles.categoryText}>
              {transaction.category?.name || 'No Category'}
            </Text>
            <Text style={styles.amountText}>
              {transaction.type === TransactionType.INCOME ? '+' : '-'} {formatCurrency(transaction.amount)}
            </Text>
            <Text style={styles.dateText}>
              {formatDate(transaction.transaction_date)}
            </Text>
          </View>
        </View>
      </Card>
    );
  };

  const renderDetailItem = (label: string, value: string | React.ReactNode, isLast = false) => (
    <View style={[styles.detailItem, isLast && styles.lastDetailItem]}>
      <Text style={styles.detailLabel}>{label}</Text>
      {typeof value === 'string' ? (
        <Text style={styles.detailValue}>{value}</Text>
      ) : (
        value
      )}
    </View>
  );

  const renderImageSection = () => {
    if (!transaction?.image_url) return null;

    return (
      <Card style={styles.detailsCard}>
        <Text style={styles.sectionTitle}>Transaction Image</Text>
        <TouchableOpacity
          style={styles.imageContainer}
          onPress={() => setShowImageModal(true)}
        >
          <Image
            source={{ uri: transaction.image_url }}
            style={styles.transactionImage}
            resizeMode="cover"
          />
          <View style={styles.imageOverlay}>
            <Text style={styles.imageOverlayText}>Tap to view full size</Text>
          </View>
        </TouchableOpacity>
      </Card>
    );
  };

  const renderImageModal = () => {
    if (!transaction?.image_url) return null;

    return (
      <Modal
        visible={showImageModal}
        transparent
        animationType="fade"
        onRequestClose={() => setShowImageModal(false)}
      >
        <View style={styles.modalOverlay}>
          <TouchableOpacity
            style={styles.modalCloseArea}
            onPress={() => setShowImageModal(false)}
          >
            <Image
              source={{ uri: transaction.image_url }}
              style={styles.fullSizeImage}
              resizeMode="contain"
            />
          </TouchableOpacity>
        </View>
      </Modal>
    );
  };

  const renderTransactionDetails = () => {
    if (!transaction) return null;

    return (
      <Card style={styles.detailsCard}>
        <Text style={styles.sectionTitle}>Transaction Details</Text>

        {renderDetailItem(
          'Transaction Type',
          <View style={styles.typeContainer}>
            <Text style={[
              styles.typeText,
              transaction.type === TransactionType.INCOME ? styles.incomeType : styles.expenseType
            ]}>
              {transaction.type === TransactionType.INCOME ? 'Income' : 'Expense'}
            </Text>
          </View>
        )}

        {transaction.description && renderDetailItem('Description', transaction.description)}

        {transaction.notes && renderDetailItem('Notes', transaction.notes)}

        {renderDetailItem('Input Method', getInputMethodText(transaction.input_method))}

        {renderDetailItem('Created Date', formatDateTime(transaction.created_at))}

        {renderDetailItem('Last Updated', formatDateTime(transaction.updated_at), true)}
      </Card>
    );
  };

  const renderActionButtons = () => (
    <View style={styles.actionContainer}>
      <Button
        title="Edit"
        onPress={handleEditTransaction}
        style={[styles.actionButton, styles.editButton]}
        disabled={loading || deleting}
      />

      <Button
        title={deleting ? 'Deleting...' : 'Delete'}
        onPress={handleDeleteTransaction}
        style={[styles.actionButton, styles.deleteButton]}
        disabled={loading || deleting}
      />
    </View>
  );

  if (loading) {
    return (
      <Screen>
        <StatusBar style="dark" />
        {renderLoadingState()}
      </Screen>
    );
  }

  if (!transaction) {
    return (
      <Screen>
        <StatusBar style="dark" />
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Transaction not found</Text>
          <Button
            title="Go Back"
            onPress={() => navigation.goBack()}
            style={styles.backButton}
          />
        </View>
      </Screen>
    );
  }

  return (
    <Screen>
      <StatusBar style="dark" />
      <ScrollView 
        style={styles.container}
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
      >
        {renderTransactionHeader()}
        {renderImageSection()}
        {renderTransactionDetails()}
        {renderActionButtons()}
      </ScrollView>
      {renderImageModal()}
    </Screen>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background.secondary,
  },
  scrollContainer: {
    padding: theme.spacing.md,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: theme.spacing.sm,
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.secondary,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: theme.spacing.xl,
  },
  errorText: {
    fontSize: theme.typography.fontSizes.lg,
    color: theme.colors.text.secondary,
    marginBottom: theme.spacing.xl,
    textAlign: 'center',
  },
  backButton: {
    paddingHorizontal: theme.spacing.xl,
  },

  // Header Card
  headerCard: {
    marginBottom: theme.spacing.md,
    padding: theme.spacing.lg,
  },
  incomeHeader: {
    backgroundColor: theme.colors.success,
  },
  expenseHeader: {
    backgroundColor: theme.colors.error,
  },
  headerContent: {
    alignItems: 'center',
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  iconText: {
    fontSize: 40,
  },
  headerInfo: {
    alignItems: 'center',
  },
  categoryText: {
    fontSize: theme.typography.fontSizes.lg,
    fontWeight: theme.typography.fontWeights.semibold,
    color: theme.colors.text.inverse,
    marginBottom: theme.spacing.sm,
  },
  amountText: {
    fontSize: theme.typography.fontSizes['3xl'],
    fontWeight: theme.typography.fontWeights.bold,
    color: theme.colors.text.inverse,
    marginBottom: theme.spacing.sm,
  },
  dateText: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.inverse,
    opacity: 0.9,
  },

  // Details Card
  detailsCard: {
    marginBottom: theme.spacing.md,
    padding: theme.spacing.md,
  },
  sectionTitle: {
    fontSize: theme.typography.fontSizes.lg,
    fontWeight: theme.typography.fontWeights.semibold,
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.md,
  },
  detailItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    paddingVertical: theme.spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.gray[100],
  },
  lastDetailItem: {
    borderBottomWidth: 0,
  },
  detailLabel: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.secondary,
    flex: 1,
    marginRight: theme.spacing.md,
  },
  detailValue: {
    fontSize: theme.typography.fontSizes.md,
    color: theme.colors.text.primary,
    flex: 2,
    textAlign: 'right',
  },
  typeContainer: {
    flex: 2,
    alignItems: 'flex-end',
  },
  typeText: {
    fontSize: theme.typography.fontSizes.sm,
    fontWeight: theme.typography.fontWeights.semibold,
    paddingVertical: theme.spacing.xs,
    paddingHorizontal: theme.spacing.sm,
    borderRadius: theme.borderRadius.sm,
    overflow: 'hidden',
  },
  incomeType: {
    backgroundColor: theme.colors.success,
    color: theme.colors.text.inverse,
  },
  expenseType: {
    backgroundColor: theme.colors.error,
    color: theme.colors.text.inverse,
  },

  // Action Buttons
  actionContainer: {
    flexDirection: 'row',
    gap: theme.spacing.md,
    marginTop: theme.spacing.md,
  },
  actionButton: {
    flex: 1,
    marginTop: 0,
  },
  editButton: {
    backgroundColor: theme.colors.primary[500],
  },
  deleteButton: {
    backgroundColor: theme.colors.error,
  },

  // Image Styles
  imageContainer: {
    position: 'relative',
    borderRadius: theme.borderRadius.lg,
    overflow: 'hidden',
    marginTop: theme.spacing.sm,
  },
  transactionImage: {
    width: '100%',
    height: 200,
  },
  imageOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    padding: theme.spacing.sm,
    alignItems: 'center',
  },
  imageOverlayText: {
    color: theme.colors.text.inverse,
    fontSize: theme.typography.fontSizes.sm,
    fontWeight: '500',
  },

  // Modal Styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalCloseArea: {
    flex: 1,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  fullSizeImage: {
    width: '90%',
    height: '80%',
  },
});

export default TransactionDetailScreen; 